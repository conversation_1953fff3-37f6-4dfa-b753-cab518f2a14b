# JMeter功能完整梳理

## 1. 测试计划 (Test Plan)
- 测试计划配置
- 用户定义变量
- 库文件管理
- 函数助手

## 2. 线程组 (Thread Groups)
- 标准线程组
- setUp线程组
- tearDown线程组
- 步进线程组
- 终极线程组
- 并发线程组

## 3. 采样器 (Samplers)
### HTTP采样器
- HTTP请求
- HTTP请求默认值
- SOAP/XML-RPC请求

### 其他协议采样器
- FTP请求
- JDBC请求
- JMS采样器
- LDAP请求
- Mail Reader
- TCP采样器
- OS Process采样器

## 4. 逻辑控制器 (Logic Controllers)
- If控制器
- Loop控制器
- Once Only控制器
- Interleave控制器
- Random控制器
- Random Order控制器
- Throughput控制器
- Runtime控制器
- Switch控制器
- ForEach控制器
- Module控制器
- Include控制器
- Transaction控制器
- Recording控制器
- Critical Section控制器

## 5. 监听器 (Listeners)
### 结果查看
- View Results Tree
- View Results in Table
- Summary Report
- Aggregate Report
- Graph Results
- Simple Data Writer
- Monitor Results

### 图形监听器
- Response Times Over Time
- Throughput Over Time
- Active Threads Over Time
- Response Time Distribution
- Connect Time Over Time
- Latencies Over Time
- Hits per Second
- Transactions per Second
- Response Codes per Second
- Bytes Throughput Over Time

## 6. 断言 (Assertions)
- Response Assertion
- Duration Assertion
- Size Assertion
- XML Assertion
- XPath Assertion
- BeanShell Assertion
- MD5Hex Assertion
- HTML Assertion
- Compare Assertion
- SMIME Assertion

## 7. 定时器 (Timers)
- Constant Timer
- Gaussian Random Timer
- Uniform Random Timer
- Constant Throughput Timer
- Synchronizing Timer
- BeanShell Timer
- BSF Timer
- JSR223 Timer
- Poisson Random Timer

## 8. 前置处理器 (Pre-Processors)
- HTML Link Parser
- HTTP URL Re-writing Modifier
- HTTP User Parameter Modifier
- User Parameters
- JDBC PreProcessor
- JSR223 PreProcessor
- RegEx User Parameters
- BeanShell PreProcessor
- BSF PreProcessor

## 9. 后置处理器 (Post-Processors)
- Regular Expression Extractor
- XPath Extractor
- CSS Selector Extractor
- JSON Extractor
- XPath2 Extractor
- BeanShell PostProcessor
- JSR223 PostProcessor
- JDBC PostProcessor
- Debug PostProcessor
- BSF PostProcessor

## 10. 配置元件 (Config Elements)
- CSV Data Set Config
- FTP Request Defaults
- HTTP Authorization Manager
- HTTP Cache Manager
- HTTP Cookie Manager
- HTTP Header Manager
- HTTP Request Defaults
- Java Request Defaults
- JDBC Connection Configuration
- Keystore Configuration
- LDAP Request Defaults
- LDAP Extended Request Defaults
- Login Config Element
- Simple Config Element
- Random Variable
- Counter
- User Defined Variables
- DNS Cache Manager

## 11. 函数助手 (Functions)
### 信息函数
- ${__threadNum}
- ${__samplerName}
- ${__machineIP}
- ${__machineName}
- ${__time}
- ${__log}

### 输入函数
- ${__StringFromFile}
- ${__FileToString}
- ${__CSVRead}
- ${__XPath}
- ${__property}
- ${__P}
- ${__setProperty}

### 计算函数
- ${__counter}
- ${__Random}
- ${__RandomString}
- ${__UUID}
- ${__sum}
- ${__longSum}

### 脚本函数
- ${__BeanShell}
- ${__javaScript}
- ${__jexl2}
- ${__jexl3}
- ${__groovy}

## 12. 测试片段 (Test Fragment)
- 可重用的测试组件
- 模块化测试设计

## 13. 非测试元件 (Non-Test Elements)
- HTTP(S) Test Script Recorder
- HTTP Mirror Server
- Property Display

## 14. 分布式测试
- 远程测试
- 主从模式
- 负载分发

## 15. 插件扩展
- 自定义采样器
- 自定义监听器
- 自定义函数
- 第三方插件

## 16. 报告生成
- HTML报告
- CSV结果文件
- JTL结果文件
- Dashboard报告

## 17. 命令行工具
- 非GUI模式运行
- 参数化执行
- 结果文件处理

## 18. 高级功能
- 参数化测试
- 数据驱动测试
- 关联和动态数据提取
- 检查点和断言
- 事务控制
- 错误处理
- 条件执行
- 循环控制
