#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
启动API测试服务器
"""

import sys
import os
import argparse

# 添加项目根目录到Python路径
sys.path.append('.')

from src.components.api_test.api_server import api_server


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='启动API测试服务器')
    parser.add_argument('--host', default='0.0.0.0', help='服务器地址 (默认: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=8888, help='服务器端口 (默认: 8888)')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    
    args = parser.parse_args()
    
    # 更新服务器配置
    api_server.host = args.host
    api_server.port = args.port
    
    print("🚀 EpayTools API测试服务器")
    print("=" * 50)
    print(f"📍 服务地址: http://{args.host}:{args.port}")
    print(f"🔧 调试模式: {'开启' if args.debug else '关闭'}")
    print("=" * 50)
    
    try:
        # 启动服务器
        api_server.run(debug=args.debug)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")


if __name__ == "__main__":
    main()
