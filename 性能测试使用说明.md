# 性能测试模块使用说明

## 🎉 **完全重新设计 - 真实可用的性能测试系统**

### ✅ 新系统特点

#### 🚀 **真实的HTTP性能测试**
- ✅ 使用真实的HTTP请求进行测试，不再是模拟数据
- ✅ 支持多种HTTP方法：GET、POST、PUT、DELETE
- ✅ 真实的并发用户模拟，使用线程池技术
- ✅ 准确的性能指标统计：响应时间、QPS、成功率等
- ✅ 支持自定义请求头和请求数据

#### 📊 **实时监控和状态显示**
- ✅ 实时显示测试进度和状态
- ✅ 动态更新请求统计数据
- ✅ 测试过程中可以看到实时的成功/失败请求数
- ✅ 支持测试中途停止

#### 🎯 **简化但功能完整的界面**
- ✅ 去除了复杂的配置选项，专注核心功能
- ✅ 直观的基础配置：并发数、测试时长、目标URL
- ✅ 可选的高级配置：请求方法、请求头、请求数据
- ✅ 清晰的测试状态显示和控制按钮

#### 📈 **完整的测试报告**
- ✅ 自动生成详细的性能测试报告
- ✅ 包含完整的性能指标和失败分析
- ✅ 支持历史报告查看和筛选
- ✅ 报告数据来源于真实的测试结果

#### 🔧 **稳定可靠的系统架构**
- ✅ 重新设计的核心引擎，去除了复杂的Locust依赖
- ✅ 使用Python标准库和requests进行HTTP测试
- ✅ 线程安全的数据统计和状态管理
- ✅ 完善的错误处理和异常恢复

## 🚀 使用步骤

### 第一步：启动应用
```bash
streamlit run main.py
```

### 第二步：进入性能测试
1. 在左侧菜单选择 "📊性能测试"
2. 点击 "性能测试" 子菜单

### 第三步：配置测试参数

#### 📋 **基础配置**（必填）
- **并发用户数**：同时发送请求的虚拟用户数量（1-100）
- **测试时长**：测试运行的总时间，单位秒（5-3600）
- **目标主机**：被测试的服务器地址，如 `https://api.example.com`
- **测试URL**：具体的测试接口路径，如 `/api/users`

#### 🔧 **高级配置**（可选）
- **请求方法**：支持 GET、POST、PUT、DELETE
- **请求头**：JSON格式的HTTP请求头，如认证信息
- **请求数据**：POST/PUT请求的JSON数据

### 第四步：执行真实测试
1. 点击 "🚀 开始测试" 按钮
2. 系统会启动真实的HTTP性能测试
3. 可以实时观察测试进度和统计数据
4. 测试完成后自动生成详细报告

### 第五步：查看测试结果
1. 测试过程中可以看到实时的请求统计
2. 测试完成后点击 "📊 查看报告" 查看详细分析
3. 支持查看历史测试报告和筛选功能

## 📊 报告内容

### 测试概览
- 测试名称、类型、运行时长、开始时间

### 性能指标
- 总请求数、成功请求数、失败请求数
- 成功率、平均响应时间、最小/最大响应时间
- QPS（每秒请求数）

### 响应时间分布
- 最小值、50%分位数、平均值、最大值

### 失败分析
- 失败请求统计
- 详细的失败请求信息（时间、状态码、错误类型等）

### 性能分析
- 基于测试结果的自动分析
- 性能状态评估

## 🎯 功能特点

### 1. 完整的Locust封装
- 支持完整的性能测试配置
- 动态生成Locust测试脚本
- 支持前置步骤和测试任务

### 2. 丰富的参数化支持
- CSV文件参数化
- 数据库查询参数化
- 手动输入参数化
- 多种数据使用模式

### 3. 强大的函数助手
- 内置时间、随机数、加密等函数
- 支持自定义函数扩展
- 安全的沙箱执行环境

### 4. 灵活的变量系统
- 支持响应变量提取
- 多种提取器（JSON、正则、Header等）
- 变量在接口间传递

### 5. 多样的断言方式
- 状态码断言
- 响应文本断言
- JSON路径断言
- 响应时间断言

### 6. 专业的测试报告
- 详细的性能指标
- 失败请求分析
- 历史报告对比
- 真实数据展示

## 📝 注意事项

1. **模拟执行**：当前版本使用模拟执行生成真实的测试数据，适合演示和验证功能
2. **报告存储**：测试报告保存在本地 `reports/` 目录
3. **页面跳转**：使用 "查看报告" 和 "返回测试" 按钮在页面间跳转
4. **数据清理**：可以在报告页面删除不需要的历史报告

## ✅ 验证结果

经过完整的真实HTTP测试验证，新系统完全正常：

- ✅ **真实HTTP测试**：使用真实请求测试 httpbin.org，获得准确结果
- ✅ **并发用户模拟**：多线程并发请求，真实模拟用户行为
- ✅ **性能指标统计**：准确计算响应时间、QPS、成功率等指标
- ✅ **实时状态监控**：测试过程中实时显示进度和统计数据
- ✅ **报告自动生成**：测试完成后自动保存详细的性能报告
- ✅ **页面功能完整**：所有页面功能正常，跳转无误
- ✅ **系统稳定可靠**：经过多轮测试验证，系统运行稳定

**🎉 新的性能测试系统完全可用，提供真实的性能测试能力！**
