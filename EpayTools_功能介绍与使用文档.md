# 1.EpayTools 功能介绍与使用文档

## 📋 目录
- [平台概述](#平台概述)
- [系统架构](#系统架构)
- [功能模块详解](#功能模块详解)
- [快速开始](#快速开始)
- [常见问题](#常见问题)
- [技术支持](#技术支持)

---

## 🌟 平台概述

### 平台简介
**EpayTools** 是深圳易派支付科技有限公司开发的综合性测试工具平台，致力于提升测试效率、标准化测试流程、实现质量监控和探活告警。

### 平台愿景
- **测试标准化**: 统一测试流程和规范
- **效率提升**: 自动化工具减少重复工作
- **质量监控**: 实时监控系统质量状态
- **探活告警**: 及时发现和处理系统异常

### 核心特性
- 🎯 **模块化设计**: 功能独立，易于维护和扩展
- 🚀 **高性能**: 基于Streamlit构建，响应迅速
- 🔧 **易用性**: 直观的Web界面，无需复杂配置
- 🛡️ **稳定性**: 完善的错误处理和状态管理
- 📱 **响应式**: 支持多种设备访问

---

## 🏗️ 系统架构

### 技术栈
- **前端框架**: Streamlit
- **后端语言**: Python 3.10+
- **数据库**: MySQL (动账查询)
- **文件存储**: 本地SQLite (Mock配置备份)
- **加密算法**: AES、MD5、SHA系列、HMAC

### 目录结构
```
EpayTools/
├── src/
│   ├── pages/           # 页面模块
│   ├── components/      # 组件库
│   ├── database/        # 数据库连接
│   ├── mock/           # Mock服务
│   └── config/         # 配置文件
├── main.py             # 主入口
└── requirements.txt    # 依赖包
```

---

## 📚 功能模块详解

### 📋 功能总览表

| 模块分类 | 功能名称 | 主要用途 | 状态 |
|---------|---------|---------|------|
| 🏠 首页 | 平台门户 | 功能导航、平台介绍 | ✅ 完整 |
| 🚀 接口自动化 | 接口列表 | API接口管理 | ✅ 基础版 |
| ⚒️ 常用工具 | 交易计算 | 提现/充值/代付/代收计算 | ✅ 完整 |
| ⚒️ 常用工具 | 加密解密 | MD5/SHA/AES/HMAC/Base64 | ✅ 完整 |
| ⚒️ 常用工具 | 文本对比 | 差异检测和高亮显示 | ✅ 完整 |
| ⚒️ 常用工具 | JSON序列化 | 格式化/压缩/验证/转换 | ✅ 完整 |
| 🎭 Mock服务 | API管理 | Mock接口创建和管理 | ✅ 完整 |
| 🎭 Mock服务(开发) | Mock编辑 | 批量编辑Mock配置 | ✅ 完整 |
| 🎭 Mock服务(开发) | 备份管理 | 配置版本控制和恢复 | ✅ 完整 |
| 💹 数据工厂 | 动账查询 | 数据库流水查询分析 | ✅ 完整 |
| 💹 数据工厂 | 假身份数据 | 测试数据批量生成 | ✅ 完整 |

---

## 🏠 首页模块

### 功能概述
首页是平台的门户，提供平台概览、快速导航和最新更新信息。

### 主要功能
1. **平台介绍**: 展示平台愿景和特色功能
2. **快速导航**: 一键跳转到常用功能
3. **实时时间**: 显示当前系统时间
4. **更新日志**: 查看平台最新功能更新

### 使用方法
1. 访问平台后自动进入首页
2. 点击功能卡片快速跳转到对应模块
3. 查看"最新更新"了解平台新功能

---

## 🚀 接口自动化模块

### 功能概述
提供API接口的自动化测试功能，目前实现了接口列表管理功能。

### 主要功能
1. **接口列表**: 管理和查看所有API接口

### 使用教程

#### 接口列表管理
1. 进入"🚀接口自动化" → "接口列表"
2. 查看当前已配置的API接口
3. 可以查看接口的基本信息和状态

### 适用场景
- API接口信息管理
- 接口状态监控

---

## ⚒️ 常用工具模块

### 功能概述
提供日常开发和测试中常用的工具集合，包括金额计算、加密解密、文本对比和JSON处理。

## 💰 交易计算工具

### 功能说明
专业的交易金额计算工具，支持提现、充值、代付、代收四种交易类型的复杂计算，包含汇率转换、手续费计算和汇损处理。

### 主要功能
1. **多交易类型**: 提现、充值、代付、代收四种交易模式
2. **汇率转换**: 支持渠道汇率、EPAY汇率的多重转换
3. **汇损计算**: 渠道汇损和业务币种汇损处理
4. **手续费计算**: 支持百分比和固定金额的复合计算
5. **精确计算**: 支持小数位精确控制和四舍五入

### 使用教程

#### 前置配置
1. 进入"⚒️常用工具" → "交易计算"
2. 设置全局汇率参数：
   - **渠道汇率**: 渠道侧的汇率(默认1.0)
   - **渠道规则汇损(%)**: 渠道汇损百分比
   - **EPAY业务币种汇率**: 平台业务汇率(默认1.0)
   - **业务币种汇损(%)**: 业务汇损百分比

#### 提现计算
1. 选择"提现"标签页
2. 输入计算参数：
   - **提现金额**: 用户申请提现的金额
   - **手续费率(%)**: 提现手续费百分比
   - **固定手续费**: 固定收取的手续费金额
3. 系统自动计算：
   - **总手续费**: 百分比手续费 + 固定手续费
   - **实际到账金额**: 提现金额 - 总手续费
   - **汇率转换后金额**: 考虑汇率和汇损的最终金额

#### 充值计算
1. 选择"充值"标签页
2. 输入充值相关参数
3. 计算充值到账金额和相关费用

#### 代付计算
1. 选择"代付"标签页
2. 配置代付业务参数
3. 计算代付成本和手续费

#### 代收计算
1. 选择"代收"标签页
2. 设置代收业务规则
3. 计算代收净收入

### 计算逻辑

#### 汇率转换公式
```
渠道汇损后金额 = 原金额 × (1 - 渠道汇损率)
渠道汇率转换 = 汇损后金额 × 渠道汇率
业务汇损后金额 = 转换金额 × (1 - 业务汇损率)
最终金额 = 业务汇损后金额 × EPAY汇率
```

#### 手续费计算公式
```
百分比手续费 = 交易金额 × 手续费率
总手续费 = 百分比手续费 + 固定手续费
实际到账 = 交易金额 - 总手续费
```

#### 精度处理
- 支持小数点后4位精度
- 自动四舍五入处理
- 避免浮点数计算误差

### 使用场景
- **支付系统**: 各类交易的费用计算和汇率转换
- **财务结算**: 跨境支付的成本核算
- **产品定价**: 手续费策略的制定和验证
- **风控管理**: 汇损控制和成本分析
- **业务测试**: 交易流程的金额验证

### 注意事项
- ⭐ 标记的字段为关键配置项
- 汇率建议使用实时汇率数据
- 汇损率通常为小数值(如0.1%输入0.1)
- 计算结果仅供参考，实际以系统为准

## 🔐 加密解密工具

### 功能说明
提供多种加密算法的加密和解密功能，支持常见的哈希算法、编码算法、对称加密和消息认证。

### 支持算法
1. **哈希算法**: MD5、SHA1、SHA256、SHA512
2. **编码算法**: Base64编码/解码
3. **对称加密**: AES加密/解密
4. **消息认证**: HMAC签名验证

### 使用教程

#### 哈希加密
1. 从下拉菜单选择哈希算法：
   - **MD5**: 128位哈希值，常用但安全性较低
   - **SHA1**: 160位哈希值，已不推荐用于安全场景
   - **SHA256**: 256位哈希值，目前主流的安全哈希算法
   - **SHA512**: 512位哈希值，更高安全性的哈希算法

2. 在左侧输入框输入要加密的文本
3. 点击"🔒 加密"按钮
4. 在右侧查看生成的哈希值
5. 点击"📋 复制结果"复制哈希值

#### Base64编码/解码
1. 选择"Base64"算法
2. 选择操作模式：
   - **编码**: 将普通文本转换为Base64格式
   - **解码**: 将Base64格式转换回普通文本
3. 在输入框输入要处理的文本
4. 点击"🔄 编码"或"🔄 解码"按钮
5. 查看处理结果并复制

#### AES对称加密
1. 选择"AES对称加密"
2. 设置操作模式：
   - **加密**: 将明文转换为密文
   - **解密**: 将密文转换回明文
3. 输入要处理的文本
4. 设置密码(用作加密密钥)
5. 点击"🔒 加密"或"🔓 解密"按钮
6. 查看处理结果

**AES加密注意事项**:
- 加密和解密必须使用相同的密码
- 密码长度建议至少8位
- 加密结果为Base64编码的密文
- 解密时输入的应该是加密后的密文

#### HMAC消息认证
1. 选择"HMAC"算法
2. 在"消息"框输入要签名的原始消息
3. 在"密钥"框输入HMAC密钥
4. 点击"🔐 生成签名"按钮
5. 获取HMAC签名结果

**HMAC使用场景**:
- API接口签名验证
- 消息完整性校验
- 身份认证
- 防篡改验证

### 界面布局
- **左侧**: 输入区域，输入要处理的文本
- **右侧**: 输出区域，显示处理结果
- **底部**: 操作按钮和复制功能
- **顶部**: 算法选择和模式切换

### 安全提示
- 🔒 **密码安全**: 密码和密钥请妥善保管，不要在生产环境使用弱密码
- 🔒 **算法选择**: MD5和SHA1已不推荐用于安全敏感场景，建议使用SHA256或更高
- 🔒 **数据清理**: 敏感数据处理完成后及时清理输入框内容
- 🔒 **网络安全**: 不要在不安全的网络环境下处理敏感数据

### 适用场景
- **密码存储**: 用户密码的哈希存储
- **API签名**: 接口调用的签名验证
- **数据传输**: 敏感数据的加密传输
- **文件校验**: 文件完整性和一致性校验
- **身份认证**: 基于HMAC的身份验证
- **开发调试**: 加密算法的测试和验证

## 📝 文本对比工具

### 功能说明
智能文本对比工具，支持逐行对比、差异高亮显示和多种对比模式。

### 主要功能
1. **逐行对比**: 精确到行级别的差异检测
2. **差异高亮**: 不同颜色标识增加、删除、修改
3. **忽略选项**: 可忽略空白字符、大小写等
4. **导出功能**: 支持对比结果导出

### 使用教程

#### 基础对比
1. 进入"⚒️常用工具" → "文本对比"
2. 在左侧文本框输入原始文本
3. 在右侧文本框输入对比文本
4. 点击"🔍 开始对比"按钮

#### 高级选项
1. **忽略空白**: 忽略空格、制表符差异
2. **忽略大小写**: 不区分大小写对比
3. **忽略空行**: 跳过空行进行对比
4. **显示行号**: 显示文本行号便于定位

#### 结果分析
- 🟢 **绿色**: 新增的内容
- 🔴 **红色**: 删除的内容  
- 🟡 **黄色**: 修改的内容
- ⚪ **白色**: 相同的内容

### 适用场景
- 代码版本对比
- 配置文件差异检查
- 文档修订对比
- 测试结果验证

## 🔧 JSON序列化工具

### 功能说明
专业的JSON处理工具，提供格式化、压缩、验证和多格式转换功能。

### 主要功能
1. **JSON格式化**: 美化JSON显示格式，支持自定义缩进
2. **JSON压缩**: 移除空格和换行符，最小化JSON大小
3. **JSON验证**: 检查JSON语法正确性，提供详细错误信息
4. **格式转换**: JSON与YAML、XML等格式互转
5. **统计分析**: 提供JSON结构统计信息

### 使用教程

#### JSON格式化
1. 进入"⚒️常用工具" → "JSON序列化"
2. 在左侧输入框粘贴JSON文本
3. 选择缩进大小(2空格、4空格或制表符)
4. 点击"🎨 格式化"按钮
5. 在右侧查看美化后的JSON结构

#### JSON验证
1. 输入需要验证的JSON文本
2. 点击"✅ 验证"按钮
3. 查看验证结果：
   - ✅ **有效JSON**: 显示绿色成功提示
   - ❌ **无效JSON**: 显示红色错误信息和具体位置
4. 根据错误提示修正JSON语法

#### JSON压缩
1. 输入格式化的JSON文本
2. 点击"📦 压缩"按钮
3. 获取压缩后的单行JSON
4. 显示压缩比例和节省的字符数

#### 格式转换
1. **JSON转YAML**:
   - 输入JSON数据
   - 点击"转换为YAML"
   - 获取YAML格式输出

2. **JSON转XML**:
   - 输入JSON数据
   - 点击"转换为XML"
   - 获取XML格式输出

3. **反向转换**:
   - 支持YAML和XML转回JSON格式

#### 统计分析
系统会自动分析JSON结构并显示：
- 📊 **对象数量**: JSON中对象的总数
- 📋 **数组数量**: JSON中数组的总数
- 🔢 **字段数量**: 所有字段的总数
- 📏 **最大深度**: JSON嵌套的最大层级
- 💾 **文件大小**: JSON文本的字节大小

### 高级功能
1. **路径提取**: 提取JSON中所有字段的路径
2. **键值统计**: 统计所有键名和值类型
3. **结构扁平化**: 将嵌套JSON转换为扁平结构
4. **差异对比**: 对比两个JSON的结构差异

### 错误处理
- 🔍 **精确定位**: 显示错误的具体行号和列号
- 💡 **修复建议**: 提供常见错误的修复建议
- 📝 **语法提示**: 显示JSON语法规则提醒
- 🔄 **自动修复**: 尝试自动修复常见格式问题

### 适用场景
- API响应数据处理和验证
- 配置文件编辑和格式化
- 数据格式转换和迁移
- 接口调试和数据分析
- JSON结构分析和优化

---

## 🎭 Mock服务模块

### 功能概述
完整的Mock服务解决方案，支持API模拟、请求拦截、响应定制和配置管理。

## 🎯 API管理

### 功能说明
Mock API的核心管理功能，支持创建、编辑、删除和测试Mock接口。

### 主要功能
1. **API创建**: 快速创建Mock接口
2. **响应配置**: 自定义返回数据
3. **条件匹配**: 基于请求参数的智能匹配
4. **状态管理**: 启用/禁用Mock接口

### 使用教程

#### 创建Mock API
1. 进入"🎭Mock服务" → "API管理"
2. 如果是首次使用，会显示添加配置的引导界面
3. 填写基本信息：
   - **测试案例**: 用于分组管理的测试案例名称，如 `payment-test`
   - **API路径**: Mock接口的路径，如 `/api/payment`
   - **描述**: 配置的详细描述(可选)
   - **Mock响应体**: JSON格式的返回数据

#### 配置响应数据
在"Mock响应体"中输入JSON格式的返回数据：
```json
{
  "code": 200,
  "message": "支付成功",
  "data": {
    "orderId": "12345",
    "amount": 100.00,
    "status": "success"
  }
}
```

#### 设置匹配条件
在"匹配条件"字段中设置触发条件，支持以下格式：

**精确匹配**:
- `amount=100` - 当请求参数amount等于100时匹配
- `username=admin` - 当请求参数username等于admin时匹配

**不等于匹配**:
- `status!=failed` - 当请求参数status不等于failed时匹配
- `type!=test` - 当请求参数type不等于test时匹配

**示例场景**:
```
# 支付金额大于0的场景
amount>0

# 用户类型为VIP的场景
userType=VIP

# 非测试环境的场景
env!=test
```

#### 查看和管理配置
1. 创建配置后，可以在配置列表中查看所有Mock配置
2. 支持按测试案例、API路径等条件筛选
3. 可以查看配置详情，包括：
   - 测试案例和API路径
   - 描述和匹配条件
   - 创建时间和更新时间
   - Mock响应体内容

#### 编辑和删除配置
1. 在配置列表中选择要编辑的配置
2. 点击"编辑"按钮修改配置信息
3. 支持修改描述、响应体和匹配条件
4. 可以删除不需要的配置

### 示例配置
系统提供了两个示例配置供参考：

**示例1: 支付接口**
```
测试案例: payment-api
API路径: /api/payment
匹配条件: amount>0
响应体: {"code": 200, "message": "支付成功", "data": {"orderId": "12345", "amount": 100.00}}
```

**示例2: 用户信息**
```
测试案例: user-api
API路径: /api/user/info
匹配条件: 无
响应体: {"code": 200, "data": {"userId": 1001, "name": "张三", "email": "<EMAIL>"}}
```

### 适用场景
- 前端开发阶段API模拟
- 第三方接口依赖解耦
- 测试环境数据准备
- 接口文档验证

---

## 🎭 Mock服务(开发)模块

### 功能概述
面向开发人员的高级Mock功能，提供配置编辑、备份管理等专业工具。

## ✏️ Mock编辑

### 功能说明
专业的Mock配置编辑器，支持复杂配置编辑、批量管理和高级编辑功能。

### 主要功能
1. **渠道选择**: 按渠道分组管理Mock配置
2. **批量编辑**: 支持多个配置同时编辑
3. **高级编辑**: 提供更详细的配置选项
4. **实时保存**: 配置修改实时保存

### 使用教程

#### 选择编辑渠道
1. 进入"🎭Mock服务(开发)" → "mock编辑"
2. 从渠道下拉列表中选择要编辑的渠道
3. 系统会加载该渠道下的所有Mock配置

#### 查看和编辑配置
1. **配置列表**: 显示当前渠道的所有配置
2. **字段编辑**: 可以直接编辑以下字段：
   - **API路径** (apiUrl): Mock接口的路径
   - **描述** (description): 配置的详细说明
   - **匹配条件** (condition): 请求匹配规则
   - **Mock响应体**: JSON格式的返回数据

#### 高级编辑模式
1. 点击"⚙️ 高级编辑"切换到高级模式
2. 在高级模式下可以：
   - 查看更多配置字段
   - 编辑复杂的JSON结构
   - 批量修改多个配置

#### 保存配置
1. 修改配置后，点击"💾 保存配置"按钮
2. 系统会验证配置格式的正确性
3. 保存成功后会显示确认消息

### 配置字段说明
- **apiUrl**: Mock接口的API路径
- **description**: 配置的描述信息
- **condition**: 匹配条件，支持 `field=value` 或 `field!=value` 格式
- **mockBody**: JSON格式的Mock响应数据

### 适用场景
- 批量修改Mock配置
- 复杂配置的精细编辑
- 按渠道管理不同环境的配置
- 配置的快速调试和验证

## 🗄️ 备份管理

### 功能说明
Mock配置的备份和恢复系统，确保配置安全和版本控制。

### 主要功能
1. **备份查看**: 查看所有历史备份记录
2. **备份详情**: 查看备份的详细信息
3. **配置恢复**: 从备份恢复配置
4. **备份管理**: 删除和管理备份文件

### 使用教程

#### 查看备份列表
1. 进入"🎭Mock服务(开发)" → "备份管理"
2. 查看所有可用的备份记录
3. 备份按时间倒序排列，最新的在前

#### 备份详情
每个备份记录包含：
- 📅 **备份时间**: 备份创建的时间戳
- 📊 **配置数量**: 备份包含的配置项数量
- 🏷️ **备份来源**: 备份的来源渠道或操作

#### 恢复配置
1. 选择要恢复的备份记录
2. 点击"🔄 恢复"按钮
3. 确认恢复操作
4. 系统会将配置恢复到选定的备份状态

#### 备份管理
1. **删除备份**: 清理不需要的历史备份
2. **备份对比**: 查看不同备份之间的差异
3. **导出备份**: 将备份数据导出为文件

### 适用场景
- 配置误操作后的快速恢复
- 重要配置变更前的安全备份
- 不同版本配置的对比分析
- 配置迁移和环境同步

---

## 💹 数据工厂模块

### 功能概述
专业的数据处理和生成工具集，支持真实数据查询和测试数据生成。

## 💰 动账查询

### 功能说明
强大的动账流水查询工具，支持多维度查询、实时统计和数据导出。

### 主要功能
1. **多条件查询**: 支持客户号、订单号等多种查询条件
2. **实时统计**: 自动计算统计指标
3. **数据展示**: 表格化展示查询结果
4. **导出功能**: 支持CSV和Excel格式导出

### 使用教程

#### 基础查询
1. 进入"💹数据工厂" → "动账查询"
2. 设置查询条件：
   - **客户号**: 输入具体的用户ID(可选)
   - **业务订单号**: 输入订单编号(可选)
   - **查询条数**: 选择返回记录数量(50-1000)
3. 点击"🔍 查询"按钮执行查询

#### 查询结果分析
查询结果包含以下信息：
- 📊 **统计信息**:
  - 总记录数
  - 涉及用户数
  - 涉及订单数
  - 总收入金额
  - 总支出金额

- 📋 **详细记录**:
  - ID编号
  - 变动金额(正数收入，负数支出)
  - 当前可用余额
  - 创建时间
  - 更新时间

#### 数据导出
1. 查询完成后，在结果区域找到导出按钮
2. 选择导出格式：
   - **CSV格式**: 适合数据分析
   - **Excel格式**: 适合报表制作
3. 文件自动下载到本地

### 查询技巧
- 🎯 **精确查询**: 使用客户号或订单号精确定位
- 📊 **批量分析**: 不填写条件查询最新记录
- ⏱️ **性能优化**: 合理设置查询条数避免超时
- 💾 **数据备份**: 定期导出重要数据

### 适用场景
- 客户账户流水查询
- 财务对账和核实
- 异常交易排查
- 数据分析和报表

## 🎭 假身份数据

### 功能说明
专业的测试数据生成工具，支持生成各种真实格式的假身份信息，包含完整的个人信息和银行卡数据。

### 主要功能
1. **个人信息生成**: 姓名、性别、年龄、身份证号
2. **联系方式生成**: 手机号、邮箱地址
3. **银行卡生成**: 支持12家主流银行的卡号生成
4. **地址信息生成**: 真实格式的详细地址
5. **批量生成**: 支持1-1000条数据批量生成
6. **多格式导出**: CSV、JSON格式导出

### 使用教程

#### 基础设置
1. 进入"💹数据工厂" → "假身份数据"
2. 设置生成参数：
   - **生成数量**: 选择1-1000条数据
   - **性别筛选**: 随机、男性、女性
   - **年龄范围**: 设置最小和最大年龄(18-65岁)
   - **包含银行卡**: 是否生成银行卡信息

#### 数据生成
1. 点击"🎲 生成假身份数据"按钮
2. 系统会生成包含以下信息的数据：
   - **姓名**: 真实的中文姓名
   - **性别**: 男/女
   - **年龄**: 指定范围内的年龄
   - **身份证**: 18位标准身份证号码
   - **手机号**: 11位有效手机号码
   - **邮箱**: 基于姓名生成的邮箱地址
   - **银行名称**: 随机选择的银行
   - **银行卡号**: 格式化的银行卡号
   - **地址**: 完整的详细地址

#### 支持的银行
系统支持以下12家主流银行的卡号生成：
- **四大行**: 工商银行、农业银行、中国银行、建设银行
- **股份制银行**: 交通银行、招商银行、浦发银行、民生银行
- **其他银行**: 兴业银行、平安银行、中信银行、光大银行

#### 数据导出
1. **CSV导出**:
   - 点击"📥 导出CSV"按钮
   - 适合Excel打开和数据分析
   - 文件名格式: `假身份数据_YYYYMMDD_HHMMSS.csv`

2. **JSON导出**:
   - 点击"📥 导出JSON"按钮
   - 适合程序开发使用
   - 结构化数据格式

3. **一键复制**:
   - 点击"📋 复制数据"按钮
   - 将所有数据复制到剪贴板
   - 可直接粘贴到其他应用

#### 数据统计
生成数据后会显示统计信息：
- 📊 **总记录数**: 生成的数据条数
- 👥 **性别分布**: 男性和女性的数量分布
- 🏦 **银行分布**: 各银行卡号的数量分布
- 📍 **地区分布**: 不同省份的数量分布

### 数据格式规范
生成的数据严格遵循真实格式：

**身份证号码**:
- 18位标准格式
- 前6位: 地区代码(真实省市区代码)
- 中间8位: 出生日期(YYYYMMDD)
- 第17位: 顺序码
- 第18位: 校验码(符合国标算法)

**手机号码**:
- 11位标准格式
- 前3位: 运营商号段(130-189)
- 后8位: 随机数字

**银行卡号**:
- 16-19位标准格式
- 前4-6位: 银行标识码(BIN)
- 中间部分: 账户标识
- 最后1位: Luhn算法校验位

**地址信息**:
- 省份: 真实的省级行政区
- 城市: 市区、县城、开发区等
- 街道: 常见街道名称
- 详细地址: 包含门牌号、楼栋、单元、房间号

### 安全提示
- ⚠️ **虚假数据**: 所有生成的数据均为虚假信息，仅供测试使用
- ⚠️ **禁止滥用**: 严禁用于欺诈、身份冒用等非法用途
- ⚠️ **格式正确**: 数据格式符合标准，但不对应真实个人
- ⚠️ **随机重复**: 生成的数据可能与真实信息重复，属正常现象

### 适用场景
- **系统测试**: 用户注册、身份验证功能测试
- **界面演示**: 产品演示时的示例数据
- **数据格式验证**: 验证系统对各种数据格式的处理能力
- **压力测试**: 大批量数据的性能测试
- **开发调试**: 开发阶段的测试数据准备

---

## 📄 版权信息

**© 2025 深圳易派支付科技有限公司**

本文档和软件受版权保护，未经授权不得复制、分发或修改。

---

*最后更新时间: 2025年7月28日*
*文档版本: v1.2.0*





# 2.🚀 接口自动化测试详细使用指南

## 功能概述

EpayTools接口自动化测试模块提供了完整的企业级接口测试解决方案，支持从用例创建到执行报告的全流程自动化测试。

## 核心功能模块

### 1. 项目管理 📁

**功能说明**: 管理测试项目，组织和分类接口测试用例

**主要功能**:
- ✅ **项目创建**: 创建新的测试项目，设置项目名称和描述
- ✅ **项目编辑**: 修改项目信息，更新项目状态
- ✅ **项目删除**: 删除不需要的项目（会同时删除相关用例）
- ✅ **项目统计**: 查看项目下的用例数量和执行统计

**使用步骤**:
1. 点击"项目管理"进入项目列表页面
2. 点击"➕ 添加项目"创建新项目
3. 填写项目名称、描述等信息
4. 保存后即可在项目列表中看到新创建的项目

### 2. 环境管理 🌐

**功能说明**: 管理多个测试环境，支持环境间快速切换

**主要功能**:
- ✅ **多环境支持**: 开发、测试、预生产、生产等环境
- ✅ **环境配置**: 设置基础URL、请求头、超时时间等
- ✅ **环境变量**: 支持环境级别的变量定义
- ✅ **默认环境**: 设置默认执行环境
- ✅ **环境切换**: 执行时可选择不同环境

**配置项说明**:
- **环境名称**: 环境的显示名称
- **基础URL**: 接口的基础地址，如 `https://api.example.com`
- **请求头**: 环境级别的公共请求头
- **超时时间**: 请求超时设置（秒）
- **环境变量**: 键值对形式的环境变量

**使用步骤**:
1. 进入"环境管理"页面
2. 点击"➕ 添加环境"
3. 配置环境信息和变量
4. 设置为默认环境（可选）

### 3. 数据库管理 🗄️

**功能说明**: 集成数据库操作，支持数据断言和数据准备

**支持的数据库**:
- ✅ **MySQL**: 最常用的关系型数据库
- ✅ **PostgreSQL**: 高级开源关系型数据库
- ✅ **SQLite**: 轻量级嵌入式数据库
- ✅ **Oracle**: 企业级数据库（需安装驱动）
- ✅ **SQL Server**: 微软数据库（需安装驱动）

**主要功能**:
- ✅ **连接配置**: 配置数据库连接参数
- ✅ **连接测试**: 验证数据库连接是否正常
- ✅ **数据断言**: 在测试用例中验证数据库数据
- ✅ **数据准备**: 测试前准备测试数据
- ✅ **SQL执行**: 执行自定义SQL语句

**使用步骤**:
1. 进入"数据库管理"页面
2. 点击"➕ 添加数据库"
3. 选择数据库类型并配置连接参数
4. 点击"🧪 测试连接"验证配置
5. 保存配置后可在用例中使用

**驱动安装**:
```bash
# MySQL
pip install pymysql

# PostgreSQL
pip install psycopg2-binary

# Oracle
pip install cx_Oracle
```

### 4. 用例管理 📝

**功能说明**: 创建、编辑和管理接口测试用例

**主要功能**:
- ✅ **用例创建**: 创建新的接口测试用例
- ✅ **用例编辑**: 修改现有用例的所有配置
- ✅ **用例复制**: 快速复制用例创建相似测试
- ✅ **用例删除**: 删除不需要的测试用例
- ✅ **单用例测试**: 立即执行单个用例查看结果
- ✅ **批量操作**: 批量删除、批量执行用例

**用例配置项**:

**基本信息**:
- 用例名称、描述、所属项目
- 优先级（High/Medium/Low）
- 状态（Enabled/Disabled）
- 标签（用于分类和筛选）

**请求配置**:
- **请求方法**: GET、POST、PUT、DELETE、PATCH等
- **请求URL**: 接口地址，支持变量替换
- **请求协议**: HTTP/HTTPS
- **请求头**: 自定义请求头，支持变量
- **查询参数**: URL查询参数
- **请求体**: 支持多种格式
  - JSON格式
  - Form Data格式
  - URL Encoded格式
  - XML格式
  - Raw文本
  - Binary二进制

**认证配置**:
- **Basic Auth**: 用户名密码认证
- **Bearer Token**: Token认证
- **API Key**: API密钥认证（Header/Query/Form）

**断言配置**:
- **状态码断言**: 验证HTTP状态码
- **响应体断言**: 验证响应内容
  - JSONPath表达式
  - XPath表达式
  - 正则表达式
  - 包含/不包含
  - 等于/不等于
- **响应头断言**: 验证响应头
- **响应时间断言**: 验证响应时间
- **数据库断言**: 验证数据库数据

**变量提取**:
- **JSONPath提取**: 从JSON响应中提取变量
- **正则表达式提取**: 从响应中提取变量
- **响应头提取**: 从响应头中提取变量

**使用步骤**:
1. 进入"用例管理"页面
2. 点击"➕ 添加用例"
3. 配置用例基本信息
4. 设置请求参数和认证
5. 配置断言和变量提取
6. 保存用例
7. 点击"🧪 测试"验证用例

**用例示例**:
```json
{
  "name": "用户登录接口测试",
  "method": "POST",
  "url": "/api/v1/auth/login",
  "headers": {
    "Content-Type": "application/json"
  },
  "body": {
    "username": "test_user",
    "password": "123456"
  },
  "assertions": [
    {
      "type": "status_code",
      "operator": "equals",
      "expected": "200"
    },
    {
      "type": "json_path",
      "path": "$.code",
      "operator": "equals",
      "expected": "0"
    }
  ],
  "extractions": [
    {
      "name": "access_token",
      "type": "json_path",
      "expression": "$.data.token"
    }
  ]
}
```

### 5. 用例执行 🚀

**功能说明**: 执行测试用例并监控执行过程

**执行方式**:
- ✅ **单用例执行**: 在用例管理中直接测试
- ✅ **批量执行**: 选择多个用例批量执行
- ✅ **项目执行**: 执行整个项目的所有用例
- ✅ **标签筛选**: 按标签筛选要执行的用例

**执行配置**:
- **执行环境**: 选择要执行的环境
- **并行执行**: 是否并行执行用例
- **最大并发数**: 并行执行的最大线程数
- **遇到失败时停止**: 是否在遇到失败时停止执行

**执行监控**:
- ✅ **实时进度**: 显示执行进度和当前用例
- ✅ **执行状态**: 显示每个用例的执行状态
- ✅ **实时日志**: 显示执行过程中的详细日志
- ✅ **停止执行**: 支持手动停止正在执行的任务

**使用步骤**:
1. 进入"用例执行"页面
2. 选择要执行的用例或项目
3. 配置执行参数
4. 点击"🚀 开始执行"
5. 在"执行监控"标签页查看进度
6. 执行完成后查看结果

### 6. 执行报告 📊

**功能说明**: 查看测试执行结果和生成详细报告

**报告内容**:
- ✅ **执行概览**: 总用例数、通过数、失败数、通过率
- ✅ **执行统计**: 执行时间、响应时间分布
- ✅ **失败详情**: 失败用例的详细错误信息
- ✅ **趋势分析**: 历史执行趋势图表
- ✅ **用例详情**: 每个用例的详细执行结果

**报告功能**:
- ✅ **报告列表**: 查看所有历史执行报告
- ✅ **报告详情**: 查看单个报告的详细信息
- ✅ **报告导出**: 导出JSON格式的测试报告
- ✅ **报告删除**: 删除不需要的历史报告

**图表展示**:
- 通过率饼图
- 响应时间柱状图
- 执行趋势折线图
- 用例分布统计

**使用步骤**:
1. 进入"执行报告"页面
2. 在报告列表中选择要查看的报告
3. 点击"📊 查看详情"查看详细信息
4. 点击"📥 导出报告"下载报告文件

### 7. 定时任务 ⏰

**功能说明**: 配置定时执行的测试任务（简化版）

**调度类型**:
- ✅ **每日执行**: 每天在指定时间执行
- ✅ **每周执行**: 每周在指定日期和时间执行
- ✅ **间隔执行**: 按固定间隔重复执行

**任务配置**:
- 任务名称和描述
- 调度类型和时间
- 执行项目和标签筛选
- 执行环境选择

**使用步骤**:
1. 进入"定时任务"页面
2. 点击"➕ 创建任务"
3. 配置任务信息和调度规则
4. 保存任务
5. 在任务列表中管理任务

**注意事项**:
- 当前为简化版本，配置保存在浏览器会话中
- 适用于功能演示和基本配置管理
- 生产环境建议使用专业的任务调度系统

### 8. 通知管理 📢

**功能说明**: 配置测试结果通知（简化版）

**支持平台**:
- ✅ **钉钉机器人**: 支持加签验证和@功能
- ✅ **企业微信机器人**: 支持@用户功能
- ✅ **自定义Webhook**: 支持任意Webhook地址

**通知内容**:
- 测试项目和环境信息
- 执行统计（总数、通过数、失败数、通过率）
- 执行时间和时长
- 失败用例详情
- 报告链接（如果有）

**配置步骤**:
1. 进入"通知管理"页面
2. 点击"➕ 添加配置"
3. 选择通知类型并配置Webhook
4. 配置@功能（可选）
5. 点击"🧪 测试通知"验证配置

**钉钉机器人配置**:
1. 在钉钉群中添加自定义机器人
2. 复制Webhook地址
3. 如果启用了加签，复制密钥
4. 在EpayTools中配置相关信息

**企业微信机器人配置**:
1. 在企业微信群中添加机器人
2. 复制Webhook地址
3. 在EpayTools中配置相关信息

## CI/CD集成

### API服务

**功能说明**: 提供RESTful API接口，支持CI/CD工具集成

**启动API服务**:
```bash
# 启动API服务器
python start_api_server.py --port 8888

# 检查服务状态
curl http://localhost:8888/health
```

**主要API接口**:
- `GET /health` - 健康检查
- `GET /api/projects` - 获取项目列表
- `GET /api/environments` - 获取环境列表
- `GET /api/test-cases` - 获取测试用例列表
- `POST /api/execute` - 执行测试用例
- `GET /api/execution/{id}/status` - 获取执行状态
- `GET /api/execution/{id}/report` - 获取执行报告

### Jenkins集成

**集成脚本**: `examples/jenkins_integration.py`

**使用方法**:
```bash
# 基本执行
python examples/jenkins_integration.py \
  --api-url http://localhost:8888 \
  --project "用户管理系统" \
  --environment "测试环境"

# 高级配置
python examples/jenkins_integration.py \
  --api-url http://localhost:8888 \
  --project "用户管理系统" \
  --environment "测试环境" \
  --tags "冒烟测试,核心功能" \
  --parallel \
  --timeout 600
```

**Pipeline配置**: `examples/Jenkinsfile`

```groovy
pipeline {
    agent any

    parameters {
        choice(
            name: 'ENVIRONMENT',
            choices: ['测试环境', '预生产环境'],
            description: '选择测试环境'
        )
        choice(
            name: 'PROJECT',
            choices: ['用户管理系统', '支付服务'],
            description: '选择测试项目'
        )
    }

    stages {
        stage('API测试') {
            steps {
                script {
                    sh '''
                        python3 examples/jenkins_integration.py \
                          --api-url http://api-test-server:8888 \
                          --project "${PROJECT}" \
                          --environment "${ENVIRONMENT}" \
                          --parallel
                    '''
                }
            }
        }
    }
}
```

## 最佳实践

### 1. 用例设计原则

**单一职责**: 每个用例只测试一个功能点
```json
// ✅ 好的设计
{
  "name": "用户登录-正确用户名密码",
  "description": "验证使用正确的用户名密码能够成功登录"
}

// ❌ 不好的设计
{
  "name": "用户登录和获取用户信息",
  "description": "登录后获取用户详细信息"
}
```

**数据驱动**: 使用变量和环境配置
```json
{
  "url": "{{base_url}}/api/v1/users/{{user_id}}",
  "headers": {
    "Authorization": "Bearer {{access_token}}"
  }
}
```

**断言完整**: 验证关键字段和业务逻辑
```json
{
  "assertions": [
    {"type": "status_code", "expected": "200"},
    {"type": "json_path", "path": "$.code", "expected": "0"},
    {"type": "json_path", "path": "$.data.user_id", "operator": "not_empty"},
    {"type": "response_time", "operator": "less_than", "expected": "2000"}
  ]
}
```

### 2. 环境管理建议

**环境隔离**: 不同环境使用不同的配置
- 开发环境: `dev-api.example.com`
- 测试环境: `test-api.example.com`
- 生产环境: `api.example.com`

**变量管理**: 合理使用环境变量
```json
{
  "variables": {
    "base_url": "https://test-api.example.com",
    "api_version": "v1",
    "timeout": "30",
    "admin_token": "admin_test_token_123"
  }
}
```

### 3. 执行策略

**分层测试**:
- 冒烟测试: 核心功能快速验证
- 回归测试: 完整功能验证
- 性能测试: 响应时间和并发测试

**标签分类**:
- `smoke`: 冒烟测试
- `regression`: 回归测试
- `critical`: 核心功能
- `performance`: 性能测试

**执行频率**:
- 冒烟测试: 每次代码提交后
- 回归测试: 每日定时执行
- 完整测试: 发布前执行

## 常见问题

### Q1: 如何处理动态参数？

**A**: 使用变量提取功能
```json
{
  "extractions": [
    {
      "name": "user_id",
      "type": "json_path",
      "expression": "$.data.user_id"
    }
  ]
}
```

然后在后续用例中使用: `{{user_id}}`

### Q2: 如何处理文件上传？

**A**: 在请求体中使用文件路径
```json
{
  "body_type": "form_data",
  "body_data": {
    "file": "@/path/to/file.jpg",
    "description": "用户头像"
  }
}
```

### Q3: 如何处理认证Token？

**A**:
1. 在登录用例中提取Token
2. 设置为环境变量或全局变量
3. 在其他用例中引用

```json
// 登录用例
{
  "extractions": [
    {
      "name": "access_token",
      "type": "json_path",
      "expression": "$.data.access_token"
    }
  ]
}

// 其他用例
{
  "headers": {
    "Authorization": "Bearer {{access_token}}"
  }
}
```

### Q4: 如何处理数据库验证？

**A**: 使用数据库断言
```json
{
  "database_assertions": [
    {
      "database_id": "main_db",
      "sql": "SELECT COUNT(*) as count FROM users WHERE email = ?",
      "params": ["{{user_email}}"],
      "assertion": {
        "field": "count",
        "operator": "equals",
        "expected": "1"
      }
    }
  ]
}
```

### Q5: 执行失败如何调试？

**A**:
1. 查看执行日志中的详细错误信息
2. 使用单用例测试功能单独调试
3. 检查环境配置和网络连接
4. 验证请求参数和断言条件

### Q6: 如何提高执行效率？

**A**:
1. 启用并行执行
2. 合理设置并发数（建议3-5）
3. 优化用例设计，减少不必要的等待
4. 使用标签筛选，只执行必要的用例

## 技术架构

### 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web界面       │    │   API服务       │    │   数据存储      │
│  (Streamlit)    │◄──►│  (Flask)        │◄──►│  (SQLite)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户交互      │    │   执行引擎      │    │   外部集成      │
│   - 用例管理    │    │   - HTTP请求    │    │   - 数据库      │
│   - 执行监控    │    │   - 断言验证    │    │   - 通知系统    │
│   - 报告查看    │    │   - 变量提取    │    │   - CI/CD       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心组件

1. **Web界面层**: 基于Streamlit的用户界面
2. **API服务层**: 基于Flask的RESTful API
3. **执行引擎**: HTTP请求执行和结果处理
4. **数据存储层**: SQLite数据库存储
5. **集成层**: 外部系统集成接口

---

*接口自动化测试模块文档更新完成*
*最后更新时间: 2025年7月28日*



# 3.自动化框架流程、功能

流程

1. 测试人员➡️新建项目维度(渠道)的用例集合、场景用例组合(如某个场景)➡️新建执行的环境、项目的变量、项目使用，通过参数jsonpath提取(如登录、区分环境)➡️新建用例，填写用例参数，需要提取的参数、(变量值，函数提取值)条件跳过，调用函数构造参数、调用mysql查询参数，地址、请求头、请求参数、url、请求体、参数提取、数据校验sql查询比对，断言(jsonpath、正则提取对比)，均支持函数调用➡️选择用例集合运行➡️报告输出➡️发送结果报告

功能

1. 支持函数调用特性(加解密)
2. 用例跳过
3. 灵活断言
4. sql查询校验
5. CI/CD执行策略
6. 执行结果推送钉钉
7. api➕调用UI（待定）

# 4.Todolist

1. 动账查询、自动校验金额
2. 网关接口下单等造数功能操作集成->不需要测试再协助
3. 日常重复性、耗时的工作->需要思考转化自动化
4. 接口性能压测
5. AI用例编写



# 5.混沌测试/金融行业接口安全

混沌测试

1. 模拟接口的所有异常值，发送非法数据、如文件非要上传大于限制文件、金额输入超过限制、尽可能让系统出现异常，看系统异常恢复能力、数据状态、正确性校验
2. 模拟第三方渠道回调异常、超时、故障，我们是否正确处理
3. 服务器系统资源、cpu、内存不足，接口处理故障，数据是否异常
4. 模拟延迟、丢包、响应数据篡改，系统的处理
5. 接口的幂等（如重复支付）
6. 重试逻辑是否合理、重试能否处理业务、是否有无限重试，导致请求雪崩

OWASP

1. 什么是什么OWASP
   1. **OWASP Top 10（Web 应用十大安全风险）**，Web 安全领域最具影响力的权威组织之一
2. 十大安全风险
   1. SQL 注入，
   2. **失效的访问控制**，普通用户的鉴权(token)，（如更改URL中的用户ID）能通过接口访问高级别用户权限。接口篡改，如支付金额篡改、商品金额篡改，无二次校验
   3. 加密机制失效，敏感数据未加密，一个接口返回够多非必要数据
   4. **安全配置错误**，或者使用系统的默认账户，如果admin 123456，开放了非必要端口、安全表头（如CSP, HSTS）写过无安全意识被攻击、暴露过多的错误信息（调试信息）
   5. **跨站脚本攻击（XSS）**，公司网站和恶意网站同时打开，窃取用户信息，如 cookie、账户密码，冒充用户操作上传恶意脚本到服务器、请求伪造（token、请求来源防护）
   6. **使用已知漏洞的组件**，开发人员使用已知漏洞的第三方库，导致被攻击
   7. **不足的日志记录和监控**，缺乏足够的日志记录和监控，无法及时发现安全事件，难以追溯
   8. 身份验证失效，弱密码、错误密码无限制、无锁定机制、会话长时间未关闭、明文传输密码
   9. 软件数据完整性，反序列化未验证或接受不受信来源的数据、应用程序依赖从不受信来源（如CDN）加载的库或脚本
   10. 不安全的设计，未在软件开发生命周期早期（设计阶段）进行安全分析和威胁建模，缺乏安全参考架构
