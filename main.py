#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/21
# <AUTHOR> Kai


import streamlit as st
import importlib.util
from pathlib import Path
from src.config.menu_config import MENU_STRUCTURE


def init_session_state():
    """初始化会话状态"""
    # 从URL参数获取页面状态
    query_params = st.query_params

    # 如果URL中有页面参数，使用URL参数
    if "main" in query_params:
        main_menu = query_params["main"]
        sub_menu = query_params.get("sub", None)

        # 验证菜单是否存在
        if main_menu in MENU_STRUCTURE:
            st.session_state.current_main = main_menu
            if sub_menu and sub_menu in MENU_STRUCTURE[main_menu]["submenus"]:
                st.session_state.current_sub = sub_menu
            else:
                st.session_state.current_sub = None
        else:
            # 无效的菜单，使用默认值
            st.session_state.current_main = "🏠首页"
            st.session_state.current_sub = None
    else:
        # 没有URL参数，使用默认值或session state
        if "current_main" not in st.session_state or st.session_state.current_main is None:
            st.session_state.current_main = "🏠首页"
            st.session_state.current_sub = None

        if "current_sub" not in st.session_state:
            st.session_state.current_sub = None

    # 确保首页状态正确
    if st.session_state.current_main == "🏠首页":
        st.session_state.current_sub = None


def update_url_params():
    """更新URL参数以保持页面状态"""
    params = {"main": st.session_state.current_main}
    if st.session_state.current_sub:
        params["sub"] = st.session_state.current_sub
    st.query_params.update(params)


def navigate_to_page(main_menu: str, sub_menu: str = None):
    """导航到指定页面的通用函数"""
    st.session_state.current_main = main_menu
    st.session_state.current_sub = sub_menu
    update_url_params()
    st.rerun()


def render_sidebar():
    with st.sidebar:
        st.title("🎈 Epay tools")

        # 添加CSS样式让侧边栏按钮变小
        st.markdown("""
        <style>
        /* 侧边栏所有按钮变小 */
        .stSidebar .stButton > button {
            height: 32px !important;
            padding: 4px 12px !important;
            font-size: 14px !important;
            margin: 2px 0 !important;
            min-height: 32px !important;
        }
        /* expander内的按钮也变小 */
        .stSidebar .stExpanderDetails .stButton > button {
            height: 28px !important;
            padding: 2px 8px !important;
            font-size: 13px !important;
            margin: 1px 0 !important;
            min-height: 28px !important;
        }
        </style>
        """, unsafe_allow_html=True)

        # 遍历所有一级菜单
        for main_menu in MENU_STRUCTURE:
            # 1. 显示一级菜单（作为 expander 标题，常驻显示）
            # 有子菜单则用 expander，无子菜单直接显示按钮
            has_sub = len(MENU_STRUCTURE[main_menu]["submenus"]) > 0

            if not has_sub:
                # 无子菜单：直接显示按钮
                if st.button(
                        main_menu,
                        key=f"main_{main_menu}",
                        use_container_width=True,
                        type="primary" if st.session_state.current_main == main_menu else "secondary"
                ):
                    navigate_to_page(main_menu)
            else:
                # 有子菜单：用 expander 包裹，点击标题展开/折叠
                with st.expander(
                        main_menu,
                        expanded=st.session_state.current_main == main_menu  # 当前菜单自动展开
                ):
                    # 2. 渲染子菜单（点击展开后显示）
                    for sub_menu in MENU_STRUCTURE[main_menu]["submenus"]:
                        if st.button(
                                sub_menu,
                                key=f"sub_{main_menu}_{sub_menu}",
                                use_container_width=True,
                                type="primary" if (
                                        st.session_state.current_main == main_menu
                                        and st.session_state.current_sub == sub_menu
                                ) else "secondary"
                        ):
                            navigate_to_page(main_menu, sub_menu)

        # 公司名称
        st.caption("© 2025 深圳易派支付科技有限公司")


def load_page_module(page_path):
    """动态导入 src/pages 下的页面模块"""
    full_path = Path("src/pages") / page_path
    if not full_path.exists():
        st.error(f"页面文件不存在: {full_path}")
        return None

    # 动态导入模块
    spec = importlib.util.spec_from_file_location("page_module", full_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module


def show_current_page():
    """显示当前选中的页面"""
    main = st.session_state.current_main
    sub = st.session_state.current_sub
    menu_info = MENU_STRUCTURE.get(main)

    if not menu_info:
        st.error(f"无效的菜单: {main}")
        return

    # 确定页面路径
    page_path = None

    if not sub and not menu_info["submenus"]:
        # 无 submenu 的主菜单（如首页）
        page_path = menu_info.get("page_path")
    elif sub and "page_paths" in menu_info:
        # 有 submenu 时，使用子菜单对应的路径
        page_path = menu_info["page_paths"].get(sub)
    else:
        # 如果有子菜单但没有选择子菜单，显示提示
        if menu_info["submenus"]:
            st.warning("请选择子菜单")
            return
        else:
            # 没有子菜单的情况，使用page_path
            page_path = menu_info.get("page_path")

    if not page_path:
        st.error(f"未找到页面路径: main={main}, sub={sub}")
        return

    # 加载并执行页面模块
    page_module = load_page_module(page_path)
    if page_module and hasattr(page_module, "render"):
        page_module.render()  # 调用页面模块的 render 函数
    else:
        st.error("页面模块格式错误（需包含 render 函数）")


def main():
    st.set_page_config(page_title="EpayTools", page_icon="🚀", layout="wide")
    init_session_state()
    # 确保URL参数与当前状态同步
    update_url_params()
    render_sidebar()
    show_current_page()


if __name__ == "__main__":
    main()
