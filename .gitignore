# EpayTools .gitignore

# ===== Python 相关 =====
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
venvs/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# ===== 项目特定文件 =====
# 数据库文件
data/
*.db
*.sqlite
*.sqlite3

# 日志文件
logs/
*.log

# 临时文件
temp/
tmp/
*.tmp
*.temp

# 备份文件
backup/
*.bak
*.backup

# 配置文件 (包含敏感信息)
.env.local
.env.production
config.local.py
secrets.json

# 用户上传文件
uploads/
files/

# ===== IDE 和编辑器 =====
# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===== 操作系统 =====
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===== Docker 相关 =====
# Docker 构建缓存 (可选，通常不忽略)
# Dockerfile
# docker-compose.yml

# Docker 数据卷 (如果映射到项目目录)
docker-data/
docker-logs/

# ===== Streamlit 相关 =====
# Streamlit 缓存
.streamlit/secrets.toml

# ===== 其他 =====
# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 证书文件
*.pem
*.key
*.crt
*.p12

# 密钥文件
*.rsa
*.dsa

# 测试文件 (排除核心功能文件)
test_*.py
*_test.py
tests/
# 但不忽略这些核心功能文件
!src/components/api_test/test_engine.py
!src/pages/performance/performance_test.py

# 文档生成
docs/build/
site/

# 性能分析
*.prof

# 本地开发配置
local_config.py
dev_config.py

# 第三方工具
.coverage
.nyc_output
.sass-cache/

# ===== EpayTools 项目特定 =====
# 进程ID文件
*.pid
epaytools.pid

# 部署脚本生成的管理脚本
stop.sh
restart.sh
status.sh

# 运行时生成的文件
nohup.out

# 开发时的临时文件
test_*.json
debug_*.py
temp_*.py

# 用户配置文件
user_config.json
local_settings.json

# 导出的数据文件
export_*.csv
export_*.json
fake_identities_*.csv
fake_identities_*.json

# Mock服务生成的文件
mock_*.json
mock_response_*.json

# 备份压缩文件
backup_*.tar.gz
backup_*.zip

# 项目特定的忽略文件
# 如果有特定的文件需要忽略，在这里添加