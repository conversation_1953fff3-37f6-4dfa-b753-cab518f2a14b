#!/bin/bash

# EpayTools Docker 更新脚本
# 用于快速更新 Docker 容器中的代码

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker 未运行，请先启动 Docker"
        exit 1
    fi
    log_info "Docker 运行正常"
}

# 检查容器是否存在
check_container() {
    if ! docker ps -a --format "table {{.Names}}" | grep -q "epaytools"; then
        log_warning "容器 epaytools 不存在，将创建新容器"
        return 1
    fi
    return 0
}

# 备份数据
backup_data() {
    log_info "备份数据..."
    
    # 创建备份目录
    BACKUP_DIR="backup/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 备份数据库
    if [ -d "data" ]; then
        cp -r data "$BACKUP_DIR/"
        log_success "数据库备份完成: $BACKUP_DIR/data"
    fi
    
    # 备份日志
    if [ -d "logs" ]; then
        cp -r logs "$BACKUP_DIR/"
        log_success "日志备份完成: $BACKUP_DIR/logs"
    fi
    
    log_success "数据备份完成: $BACKUP_DIR"
}

# 停止并删除旧容器
stop_old_container() {
    log_info "停止旧容器..."
    
    if docker ps --format "table {{.Names}}" | grep -q "epaytools"; then
        docker stop epaytools
        log_success "容器已停止"
    fi
    
    if docker ps -a --format "table {{.Names}}" | grep -q "epaytools"; then
        docker rm epaytools
        log_success "旧容器已删除"
    fi
}

# 删除旧镜像 (可选)
remove_old_image() {
    log_info "删除旧镜像..."
    
    if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "epaytools:latest"; then
        docker rmi epaytools:latest
        log_success "旧镜像已删除"
    fi
}

# 构建新镜像
build_new_image() {
    log_info "构建新镜像..."
    
    # 构建镜像
    docker build -t epaytools:latest .
    
    if [ $? -eq 0 ]; then
        log_success "新镜像构建完成"
    else
        log_error "镜像构建失败"
        exit 1
    fi
}

# 启动新容器
start_new_container() {
    log_info "启动新容器..."
    
    # 使用 docker-compose 启动
    if [ -f "docker-compose.yml" ]; then
        docker-compose up -d
        log_success "容器已通过 docker-compose 启动"
    else
        # 直接使用 docker run
        docker run -d \
            --name epaytools \
            -p 8501:8501 \
            -v $(pwd)/data:/app/data \
            -v $(pwd)/logs:/app/logs \
            --restart unless-stopped \
            epaytools:latest
        log_success "容器已启动"
    fi
}

# 等待服务启动
wait_for_service() {
    log_info "等待服务启动..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8501/_stcore/health > /dev/null 2>&1; then
            log_success "服务启动成功"
            return 0
        fi
        
        log_info "等待服务启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    log_error "服务启动超时"
    return 1
}

# 显示服务状态
show_status() {
    log_info "服务状态:"
    docker ps --filter "name=epaytools" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    echo ""
    log_info "访问地址:"
    echo "  本地访问: http://localhost:8501"
    echo "  网络访问: http://$(hostname -I | awk '{print $1}'):8501"
}

# 清理 Docker 缓存
cleanup_docker() {
    log_info "清理 Docker 缓存..."
    
    # 清理未使用的镜像
    docker image prune -f
    
    # 清理未使用的容器
    docker container prune -f
    
    # 清理未使用的网络
    docker network prune -f
    
    log_success "Docker 缓存清理完成"
}

# 显示帮助信息
show_help() {
    echo "EpayTools Docker 更新脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -b, --backup        备份数据后更新"
    echo "  -f, --force         强制重建镜像"
    echo "  -c, --cleanup       更新后清理缓存"
    echo "  --no-backup         跳过数据备份"
    echo "  --quick             快速更新 (仅重启容器)"
    echo ""
    echo "示例:"
    echo "  $0                  # 标准更新"
    echo "  $0 -b               # 备份数据后更新"
    echo "  $0 -f -c            # 强制重建并清理缓存"
    echo "  $0 --quick          # 快速更新"
}

# 快速更新 (仅重启容器)
quick_update() {
    log_info "执行快速更新..."
    
    check_docker
    
    if check_container; then
        log_info "重启容器..."
        docker-compose restart epaytools
        wait_for_service
        show_status
        log_success "快速更新完成"
    else
        log_error "容器不存在，请执行完整更新"
        exit 1
    fi
}

# 主函数
main() {
    local backup=false
    local force=false
    local cleanup=false
    local no_backup=false
    local quick=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -b|--backup)
                backup=true
                shift
                ;;
            -f|--force)
                force=true
                shift
                ;;
            -c|--cleanup)
                cleanup=true
                shift
                ;;
            --no-backup)
                no_backup=true
                shift
                ;;
            --quick)
                quick=true
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 快速更新
    if [ "$quick" = true ]; then
        quick_update
        return
    fi
    
    log_info "开始 Docker 更新流程..."
    
    # 检查 Docker
    check_docker
    
    # 备份数据
    if [ "$backup" = true ] || [ "$no_backup" = false ]; then
        backup_data
    fi
    
    # 停止旧容器
    stop_old_container
    
    # 删除旧镜像 (如果强制重建)
    if [ "$force" = true ]; then
        remove_old_image
    fi
    
    # 构建新镜像
    build_new_image
    
    # 启动新容器
    start_new_container
    
    # 等待服务启动
    wait_for_service
    
    # 清理缓存
    if [ "$cleanup" = true ]; then
        cleanup_docker
    fi
    
    # 显示状态
    show_status
    
    log_success "Docker 更新完成!"
}

# 执行主函数
main "$@"
