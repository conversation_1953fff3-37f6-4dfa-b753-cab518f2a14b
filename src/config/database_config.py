#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
数据库配置文件
"""

# 测试数据库配置
DATABASE_CONFIG = {
    'host': 'sz-epaycloud-testpd2025.rwlb.rds.aliyuncs.com',        # 数据库主机地址
    'port': 3306,              # 数据库端口
    'user': 'epay_prorw_test',  # 数据库用户名
    'password': 'Qn&jG7wqG@2J4X%7Ut%V&2tt',  # 数据库密码
    'database': 'epay_cloud',  # 数据库名称
    'charset': 'utf8mb4',      # 字符集
    'autocommit': True         # 自动提交
}

# 连接池配置
CONNECTION_POOL_CONFIG = {
    'max_connections': 10,     # 最大连接数
    'min_connections': 1,      # 最小连接数
    'connection_timeout': 30,  # 连接超时时间（秒）
    'read_timeout': 30,        # 读取超时时间（秒）
    'write_timeout': 30        # 写入超时时间（秒）
}

# 查询配置
QUERY_CONFIG = {
    'default_limit': 100,      # 默认查询限制
    'max_limit': 10000,        # 最大查询限制
    'timeout': 60              # 查询超时时间（秒）
}
