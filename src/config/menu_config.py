#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/18
# <AUTHOR> Kai


<PERSON>NU_STRUCTURE = {
    "🏠首页": {
        "submenus": [],
        "page_path": "home.py"
    },
    "🚀接口自动化": {
        "submenus": ["项目管理", "环境管理", "数据库管理", "用例管理", "用例执行", "执行报告", "定时任务", "通知管理"],
        "page_paths": {
            "项目管理": "api_auto_test/project_management.py",
            "环境管理": "api_auto_test/environment_management.py",
            "数据库管理": "api_auto_test/database_management.py",
            "用例管理": "api_auto_test/case_management.py",
            "用例执行": "api_auto_test/case_execution.py",
            "执行报告": "api_auto_test/execution_reports.py",
            "定时任务": "api_auto_test/scheduled_tasks_simple.py",
            "通知管理": "api_auto_test/notification_management_simple.py"
        }
    },
    "⚒️常用工具": {
        "submenus": ["金额计算", "加密解密", "文本对比", "JSON序列化"],
        "page_paths": {
            "金额计算": "tools/transaction.py",
            "加密解密": "tools/encryption.py",
            "文本对比": "tools/text_diff.py",
            "JSON序列化": "tools/json_serializer.py"
        }
    },
    "🎭Mock服务": {
        "submenus": ["API管理", "API测试", "请求日志", "服务管理"],
        "page_paths": {
            "API管理": "mock/mock_management_new.py",
            "API测试": "mock/mock_tester.py",
            "请求日志": "mock/mock_logs.py",
            "服务管理": "mock/mock_service.py"
        }
    },
    "🎭Mock服务(开发)": {
        "submenus": ["mock编辑", "备份管理"],
        "page_paths": {
            "mock编辑": "mock/mock_edit.py",
            "备份管理": "mock/mock_backup_manager.py"
        }
    },
    "💹数据工厂": {
        "submenus": ["动账查询", "假身份数据"],
        "page_paths": {
            "动账查询": "data_factory/account_detail_query.py",
            "假身份数据": "data_factory/fake_identity.py"
        }
    },
    "📊性能测试": {
        "submenus": ["性能测试", "性能报告"],
        "page_paths": {
            "性能测试": "performance/performance_test.py",
            "性能报告": "performance/performance_report.py"
        }
    }

}
