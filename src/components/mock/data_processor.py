"""
Mock配置数据处理组件
负责数据过滤、转换、验证等逻辑
"""

import json
from typing import Dict, List, Any


class MockConfigFilter:
    """Mock配置过滤器"""
    
    @staticmethod
    def filter_by_search(configs: List[Dict[str, Any]], search_term: str) -> List[Dict[str, Any]]:
        """根据搜索条件过滤配置"""
        if not search_term or not search_term.strip():
            return configs
        
        filtered_list = []
        for config in configs:
            config_test_case = config.get("testCase", "")
            # 检查是否匹配搜索条件
            if search_term.lower() in config_test_case.lower():
                # 检查是否有配置数量，没有配置的渠道不显示
                mock_config_list = config.get("mockConfigList", [])
                config_count = len(mock_config_list) if mock_config_list is not None else 0
                if config_count > 0:
                    filtered_list.append(config)
        return filtered_list
    
    @staticmethod
    def get_config_count(config: Dict[str, Any]) -> int:
        """安全获取配置数量"""
        mock_config_list = config.get("mockConfigList", [])
        return len(mock_config_list) if mock_config_list is not None else 0


class JSONProcessor:
    """JSON数据处理器"""
    
    @staticmethod
    def validate_json(json_str: str) -> Tuple[bool, Any, str]:
        """验证JSON格式"""
        try:
            parsed = json.loads(json_str)
            return True, parsed, "JSON格式正确"
        except json.JSONDecodeError as e:
            return False, None, f"JSON格式错误: {str(e)}"
    
    @staticmethod
    def format_json(obj: Any, indent: int = 2) -> str:
        """格式化JSON对象"""
        try:
            return json.dumps(obj, indent=indent, ensure_ascii=False)
        except Exception:
            return str(obj)
    
    @staticmethod
    def count_fields(json_obj: Any) -> int:
        """计算JSON对象的字段数量"""
        if isinstance(json_obj, dict):
            count = len(json_obj)
            for value in json_obj.values():
                if isinstance(value, (dict, list)):
                    count += JSONProcessor.count_fields(value)
            return count
        elif isinstance(json_obj, list):
            count = 0
            for item in json_obj:
                if isinstance(item, (dict, list)):
                    count += JSONProcessor.count_fields(item)
            return count
        return 0
    
    @staticmethod
    def flatten_json(json_obj: Any, prefix: str = "") -> Dict[str, str]:
        """扁平化JSON对象"""
        result = {}
        
        if isinstance(json_obj, dict):
            for key, value in json_obj.items():
                new_key = f"{prefix}.{key}" if prefix else key
                if isinstance(value, (dict, list)):
                    result.update(JSONProcessor.flatten_json(value, new_key))
                else:
                    result[new_key] = str(value)
        elif isinstance(json_obj, list):
            for i, item in enumerate(json_obj):
                new_key = f"{prefix}[{i}]"
                if isinstance(item, (dict, list)):
                    result.update(JSONProcessor.flatten_json(item, new_key))
                else:
                    result[new_key] = str(item)
        
        return result
    
    @staticmethod
    def reconstruct_json(flat_dict: Dict[str, str]) -> Dict[str, Any]:
        """从扁平化字典重构JSON对象"""
        result = {}
        
        for key, value in flat_dict.items():
            keys = key.split('.')
            current = result
            
            for i, k in enumerate(keys[:-1]):
                if '[' in k and ']' in k:
                    # 处理数组索引
                    array_key = k.split('[')[0]
                    index = int(k.split('[')[1].split(']')[0])
                    
                    if array_key not in current:
                        current[array_key] = []
                    
                    while len(current[array_key]) <= index:
                        current[array_key].append({})
                    
                    current = current[array_key][index]
                else:
                    if k not in current:
                        current[k] = {}
                    current = current[k]
            
            # 设置最终值
            final_key = keys[-1]
            if '[' in final_key and ']' in final_key:
                array_key = final_key.split('[')[0]
                index = int(final_key.split('[')[1].split(']')[0])
                
                if array_key not in current:
                    current[array_key] = []
                
                while len(current[array_key]) <= index:
                    current[array_key].append("")
                
                current[array_key][index] = value
            else:
                current[final_key] = value
        
        return result


class ConfigValidator:
    """配置验证器"""
    
    @staticmethod
    def validate_mock_config(config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证Mock配置"""
        errors = []
        
        # 检查必需字段
        required_fields = ["testCase", "mockConfigList"]
        for field in required_fields:
            if field not in config:
                errors.append(f"缺少必需字段: {field}")
        
        # 检查mockConfigList
        mock_config_list = config.get("mockConfigList", [])
        if not isinstance(mock_config_list, list):
            errors.append("mockConfigList必须是数组")
        else:
            for i, mock_config in enumerate(mock_config_list):
                if not isinstance(mock_config, dict):
                    errors.append(f"配置项{i+1}必须是对象")
                    continue
                
                # 检查配置项必需字段
                config_required = ["apiUrl"]
                for field in config_required:
                    if field not in mock_config:
                        errors.append(f"配置项{i+1}缺少字段: {field}")
        
        return len(errors) == 0, errors
    
    @staticmethod
    def validate_json_field(field_value: str, field_name: str) -> Tuple[bool, str]:
        """验证JSON字段"""
        if not field_value.strip():
            return True, ""  # 空值允许
        
        try:
            json.loads(field_value)
            return True, ""
        except json.JSONDecodeError as e:
            return False, f"{field_name}格式错误: {str(e)}"
