"""
Mock配置API客户端组件
负责所有与Mock配置相关的API调用
"""

import requests
from typing import Dict, List, Any, Tuple


class MockConfigAPIClient:
    """Mock配置API客户端"""
    
    BASE_URL = "http://web.epaydev.xyz/capi/netforward/mock"
    
    @classmethod
    def query_configs(cls, test_case: str = "") -> Tuple[bool, List[Dict[str, Any]]]:
        """查询Mock配置"""
        try:
            url = f"{cls.BASE_URL}/queryMockConfig"
            headers = {"Content-Type": "application/json"}
            data = {"testCase": test_case}
            
            response = requests.post(url, headers=headers, json=data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 1:
                    mock_list = result.get("data", {}).get("mockResDTOList", [])
                    return True, mock_list
                else:
                    return False, []
            return False, []
            
        except requests.exceptions.Timeout:
            return False, []
        except Exception:
            return False, []
    
    @classmethod
    def save_config(cls, test_case: str, mock_config_list: List[Dict[str, Any]]) -> Tuple[bool, str]:
        """保存Mock配置"""
        try:
            url = f"{cls.BASE_URL}/addMockConfig"
            headers = {"Content-Type": "application/json"}
            payload = {
                "testCase": test_case,
                "mockConfigList": mock_config_list
            }
            
            response = requests.post(url, headers=headers, json=payload, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                
                # 检查多种成功标识
                success_indicators = [
                    result.get("success", False),
                    result.get("code") == 200,
                    result.get("code") == "200",
                    result.get("status") == "success",
                    "成功" in str(result.get("message", "")),
                    "操作成功" in str(result.get("message", ""))
                ]
                
                if any(success_indicators):
                    return True, "保存成功"
                else:
                    return False, result.get("message", "保存失败")
            else:
                return False, f"HTTP错误: {response.status_code}"
                
        except requests.exceptions.Timeout:
            return False, "请求超时"
        except Exception as e:
            return False, f"网络错误: {str(e)}"
    
    @classmethod
    def get_config_detail(cls, test_case: str) -> Tuple[bool, Dict[str, Any]]:
        """获取配置详情"""
        success, configs = cls.query_configs(test_case)
        if success and configs:
            return True, configs[0]
        return False, {}
