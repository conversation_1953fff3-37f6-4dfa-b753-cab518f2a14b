"""
Mock配置状态管理组件
负责Streamlit session_state的管理
"""

import streamlit as st
from typing import Dict, List, Any, Optional


class MockEditStateManager:
    """Mock编辑状态管理器"""
    
    # 状态键名常量
    EDIT_MODE = "edit_mode"
    EDIT_CONFIG = "edit_config"
    MOCK_CONFIGS = "mock_configs"
    ADVANCED_MODE = "advanced_edit_mode"
    JUST_SAVED = "just_saved"
    SEARCH_CHANNEL = "search_channel"
    
    @classmethod
    def get_edit_mode(cls) -> bool:
        """获取编辑模式状态"""
        return st.session_state.get(cls.EDIT_MODE, False)
    
    @classmethod
    def set_edit_mode(cls, mode: bool):
        """设置编辑模式状态"""
        st.session_state[cls.EDIT_MODE] = mode
    
    @classmethod
    def get_edit_config(cls) -> Optional[Dict[str, Any]]:
        """获取编辑配置"""
        return st.session_state.get(cls.EDIT_CONFIG)
    
    @classmethod
    def set_edit_config(cls, config: Dict[str, Any]):
        """设置编辑配置"""
        st.session_state[cls.EDIT_CONFIG] = config
    
    @classmethod
    def get_mock_configs(cls) -> List[Dict[str, Any]]:
        """获取Mock配置列表"""
        return st.session_state.get(cls.MOCK_CONFIGS, [])
    
    @classmethod
    def set_mock_configs(cls, configs: List[Dict[str, Any]]):
        """设置Mock配置列表"""
        st.session_state[cls.MOCK_CONFIGS] = configs
    
    @classmethod
    def get_advanced_mode(cls) -> bool:
        """获取高级编辑模式状态"""
        return st.session_state.get(cls.ADVANCED_MODE, False)
    
    @classmethod
    def set_advanced_mode(cls, mode: bool):
        """设置高级编辑模式状态"""
        st.session_state[cls.ADVANCED_MODE] = mode
    
    @classmethod
    def get_just_saved(cls) -> bool:
        """获取刚保存状态"""
        return st.session_state.get(cls.JUST_SAVED, False)
    
    @classmethod
    def set_just_saved(cls, saved: bool):
        """设置刚保存状态"""
        st.session_state[cls.JUST_SAVED] = saved
    
    @classmethod
    def get_search_channel(cls) -> str:
        """获取搜索渠道"""
        return st.session_state.get(cls.SEARCH_CHANNEL, "")
    
    @classmethod
    def set_search_channel(cls, channel: str):
        """设置搜索渠道"""
        st.session_state[cls.SEARCH_CHANNEL] = channel
    
    @classmethod
    def clear_edit_state(cls):
        """清除编辑状态"""
        keys_to_remove = [
            cls.EDIT_MODE, 
            cls.EDIT_CONFIG, 
            cls.ADVANCED_MODE, 
            cls.JUST_SAVED
        ]
        for key in keys_to_remove:
            if key in st.session_state:
                del st.session_state[key]
    
    @classmethod
    def clear_all_state(cls):
        """清除所有状态"""
        keys_to_remove = [
            cls.EDIT_MODE,
            cls.EDIT_CONFIG,
            cls.MOCK_CONFIGS,
            cls.ADVANCED_MODE,
            cls.JUST_SAVED,
            cls.SEARCH_CHANNEL
        ]
        for key in keys_to_remove:
            if key in st.session_state:
                del st.session_state[key]
    
    @classmethod
    def get_state_summary(cls) -> Dict[str, Any]:
        """获取状态摘要（用于调试）"""
        return {
            "edit_mode": cls.get_edit_mode(),
            "has_edit_config": cls.get_edit_config() is not None,
            "mock_configs_count": len(cls.get_mock_configs()),
            "advanced_mode": cls.get_advanced_mode(),
            "just_saved": cls.get_just_saved(),
            "search_channel": cls.get_search_channel()
        }


class BackupStateManager:
    """备份状态管理器"""
    
    BACKUP_LIST = "backup_list"
    SELECTED_BACKUP = "selected_backup"
    
    @classmethod
    def get_backup_list(cls) -> List[Dict[str, Any]]:
        """获取备份列表"""
        return st.session_state.get(cls.BACKUP_LIST, [])
    
    @classmethod
    def set_backup_list(cls, backups: List[Dict[str, Any]]):
        """设置备份列表"""
        st.session_state[cls.BACKUP_LIST] = backups
    
    @classmethod
    def get_selected_backup(cls) -> Optional[Dict[str, Any]]:
        """获取选中的备份"""
        return st.session_state.get(cls.SELECTED_BACKUP)
    
    @classmethod
    def set_selected_backup(cls, backup: Dict[str, Any]):
        """设置选中的备份"""
        st.session_state[cls.SELECTED_BACKUP] = backup
    
    @classmethod
    def clear_backup_state(cls):
        """清除备份状态"""
        keys_to_remove = [cls.BACKUP_LIST, cls.SELECTED_BACKUP]
        for key in keys_to_remove:
            if key in st.session_state:
                del st.session_state[key]


class FormStateManager:
    """表单状态管理器"""
    
    @staticmethod
    def generate_field_key(field_name: str, index: int, prefix: str = "form") -> str:
        """生成表单字段的唯一键"""
        return f"{prefix}_{field_name}_{index}"
    
    @staticmethod
    def get_field_value(field_name: str, index: int, default: str = "", prefix: str = "form") -> str:
        """获取表单字段值"""
        key = FormStateManager.generate_field_key(field_name, index, prefix)
        return st.session_state.get(key, default)
    
    @staticmethod
    def set_field_value(field_name: str, index: int, value: str, prefix: str = "form"):
        """设置表单字段值"""
        key = FormStateManager.generate_field_key(field_name, index, prefix)
        st.session_state[key] = value
    
    @staticmethod
    def clear_form_state(prefix: str = "form"):
        """清除表单状态"""
        keys_to_remove = [key for key in st.session_state.keys() if key.startswith(f"{prefix}_")]
        for key in keys_to_remove:
            del st.session_state[key]
    
    @staticmethod
    def collect_form_data(fields: List[str], count: int, prefix: str = "form") -> List[Dict[str, str]]:
        """收集表单数据"""
        result = []
        for i in range(count):
            item = {}
            for field in fields:
                item[field] = FormStateManager.get_field_value(field, i, "", prefix)
            result.append(item)
        return result


class NavigationStateManager:
    """导航状态管理器"""
    
    CURRENT_PAGE = "current_page"
    PAGE_HISTORY = "page_history"
    
    @classmethod
    def get_current_page(cls) -> str:
        """获取当前页面"""
        return st.session_state.get(cls.CURRENT_PAGE, "main")
    
    @classmethod
    def set_current_page(cls, page: str):
        """设置当前页面"""
        # 保存到历史记录
        history = cls.get_page_history()
        current = cls.get_current_page()
        if current != page and current not in history[-3:]:  # 保留最近3个页面
            history.append(current)
            if len(history) > 5:  # 最多保留5个历史页面
                history = history[-5:]
            st.session_state[cls.PAGE_HISTORY] = history
        
        st.session_state[cls.CURRENT_PAGE] = page
    
    @classmethod
    def get_page_history(cls) -> List[str]:
        """获取页面历史"""
        return st.session_state.get(cls.PAGE_HISTORY, [])
    
    @classmethod
    def go_back(cls) -> str:
        """返回上一页"""
        history = cls.get_page_history()
        if history:
            previous_page = history.pop()
            st.session_state[cls.PAGE_HISTORY] = history
            cls.set_current_page(previous_page)
            return previous_page
        return "main"
    
    @classmethod
    def clear_navigation_state(cls):
        """清除导航状态"""
        keys_to_remove = [cls.CURRENT_PAGE, cls.PAGE_HISTORY]
        for key in keys_to_remove:
            if key in st.session_state:
                del st.session_state[key]
