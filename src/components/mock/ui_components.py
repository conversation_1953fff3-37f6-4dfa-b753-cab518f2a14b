"""
Mock配置UI组件
负责可复用的UI组件渲染
"""

import streamlit as st
from typing import Dict, List, Any, Optional, Tuple
from .data_processor import MockConfigFilter


class SearchBar:
    """搜索栏组件"""
    
    @staticmethod
    def render(placeholder: str = "输入渠道名称搜索...") -> str:
        """渲染搜索栏"""
        col1, col2 = st.columns([4, 1])
        
        with col1:
            search_term = st.text_input(
                "渠道名称查询",
                placeholder=placeholder,
                key="search_channel"
            )
        
        with col2:
            st.markdown("<br>", unsafe_allow_html=True)  # 对齐按钮
            search_clicked = st.button("🔍 查询", type="primary", use_container_width=True)
        
        return search_term if search_clicked else ""


class ConfigTable:
    """配置表格组件"""
    
    @staticmethod
    def render(configs: List[Dict[str, Any]]) -> List[int]:
        """渲染配置表格"""
        if not configs:
            st.info("📋 暂无配置数据")
            return []
        
        st.markdown("### 📊 Mock配置列表")
        
        # 准备表格数据
        table_data = []
        for i, config in enumerate(configs):
            config_count = MockConfigFilter.get_config_count(config)
            table_data.append({
                "序号": i + 1,
                "渠道名称": config.get("testCase", ""),
                "配置数量": config_count,
                "编辑": False
            })
        
        # 显示表格
        edited_df = st.data_editor(
            table_data,
            use_container_width=True,
            hide_index=True,
            column_config={
                "序号": st.column_config.NumberColumn("序号", width="small"),
                "渠道名称": st.column_config.TextColumn("🔗 渠道名称", width="medium"),
                "配置数量": st.column_config.NumberColumn("⚙️ 配置项", width="small"),
                "编辑": st.column_config.CheckboxColumn("✏️ 编辑", width="small")
            },
            disabled=["序号", "渠道名称", "配置数量"]
        )
        
        # 返回选中的行索引
        selected_rows = [i for i, row in enumerate(edited_df) if row["编辑"]]
        return selected_rows


class FormField:
    """表单字段组件"""
    
    @staticmethod
    def render_text_input(label: str, value: str = "", key: str = None) -> str:
        """渲染文本输入框"""
        return st.text_input(
            label,
            value=value,
            key=key or f"field_{label}_{hash(value)}"
        )
    
    @staticmethod
    def render_text_area(label: str, value: str = "", height: int = 100, key: str = None) -> str:
        """渲染文本区域"""
        return st.text_area(
            label,
            value=value,
            height=height,
            key=key or f"field_{label}_{hash(value)}"
        )
    
    @staticmethod
    def render_selectbox(label: str, options: List[str], value: str = "", key: str = None) -> str:
        """渲染选择框"""
        current_index = options.index(value) if value in options else 0
        return st.selectbox(
            label,
            options=options,
            index=current_index,
            key=key or f"field_{label}_{hash(value)}"
        )
    
    @staticmethod
    def render_json_editor(label: str, value: str = "", height: int = 200, key: str = None) -> str:
        """渲染JSON编辑器"""
        return st.text_area(
            label,
            value=value,
            height=height,
            help="请输入有效的JSON格式",
            key=key or f"json_{label}_{hash(value)}"
        )


class NavigationButtons:
    """导航按钮组件"""
    
    @staticmethod
    def render(show_back: bool = True, show_save: bool = True, 
               show_backup: bool = True, show_reset: bool = True,
               show_advanced: bool = True) -> Dict[str, bool]:
        """渲染导航按钮"""
        buttons = {}
        cols = []
        
        if show_back:
            cols.append("back")
        if show_backup:
            cols.append("backup")
        if show_reset:
            cols.append("reset")
        if show_advanced:
            cols.append("advanced")
        if show_save:
            cols.append("save")
        
        columns = st.columns(len(cols))
        
        for i, (button_type, col) in enumerate(zip(cols, columns)):
            with col:
                if button_type == "back":
                    buttons["back"] = st.button("← 返回", use_container_width=True)
                elif button_type == "backup":
                    buttons["backup"] = st.button("💾 备份", use_container_width=True)
                elif button_type == "reset":
                    buttons["reset"] = st.button("🔄 重置", use_container_width=True)
                elif button_type == "advanced":
                    buttons["advanced"] = st.button("⚙️ 高级", use_container_width=True)
                elif button_type == "save":
                    buttons["save"] = st.button("💾 保存", type="primary", use_container_width=True)
        
        return buttons


class ConfigForm:
    """配置表单组件"""
    
    @staticmethod
    def render_basic_config(mock_config: Dict[str, Any], index: int) -> Dict[str, Any]:
        """渲染基础配置表单"""
        st.markdown(f"**📋 配置项 {index + 1}**")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**基础配置**")
            mock_config["apiUrl"] = FormField.render_text_input(
                "接口路径", 
                mock_config.get("apiUrl", ""),
                key=f"apiUrl_{index}"
            )
            mock_config["description"] = FormField.render_text_input(
                "接口描述", 
                mock_config.get("description", ""),
                key=f"description_{index}"
            )
            mock_config["mockRule"] = FormField.render_selectbox(
                "Mock规则",
                ["default", "custom", "random"],
                mock_config.get("mockRule", "default"),
                key=f"mockRule_{index}"
            )
        
        with col2:
            st.markdown("**响应配置**")
            mock_config["mockBody"] = FormField.render_json_editor(
                "响应体",
                mock_config.get("mockBody", ""),
                height=120,
                key=f"mockBody_{index}"
            )
            mock_config["condition"] = FormField.render_text_area(
                "匹配条件",
                mock_config.get("condition", ""),
                height=80,
                key=f"condition_{index}"
            )
        
        return mock_config
    
    @staticmethod
    def render_advanced_config(config: Dict[str, Any]) -> Tuple[str, bool]:
        """渲染高级配置表单"""
        from .data_processor import JSONProcessor
        
        st.markdown("### ⚙️ 高级编辑模式 - JSON配置")
        
        # 格式化JSON
        formatted_json = JSONProcessor.format_json(config)
        
        # JSON编辑器
        edited_json = st.text_area(
            "编辑JSON配置",
            value=formatted_json,
            height=400,
            help="在此处编辑完整的JSON配置"
        )
        
        # 验证JSON
        from .data_processor import JSONProcessor
        json_valid, parsed_config, message = JSONProcessor.validate_json(edited_json)
        
        if json_valid:
            st.success(f"✅ {message}")
        else:
            st.error(f"❌ {message}")
        
        return edited_json, json_valid


class StatusDisplay:
    """状态显示组件"""
    
    @staticmethod
    def render_backup_status(test_case: str) -> str:
        """渲染备份状态"""
        try:
            from src.database.mock_backup import MockBackupDB
            backup_db = MockBackupDB()
            if backup_db.backup_exists(test_case):
                return "🟢 已备份"
            else:
                return "🔴 未备份"
        except Exception:
            return "❓ 未知"
    
    @staticmethod
    def render_config_info(test_case: str, config_count: int):
        """渲染配置信息"""
        backup_status = StatusDisplay.render_backup_status(test_case)
        st.markdown(f"**编辑配置: {test_case}** ({backup_status}) - {config_count}个配置项")
    
    @staticmethod
    def show_success(message: str):
        """显示成功消息"""
        st.success(f"✅ {message}")
    
    @staticmethod
    def show_error(message: str):
        """显示错误消息"""
        st.error(f"❌ {message}")
    
    @staticmethod
    def show_warning(message: str):
        """显示警告消息"""
        st.warning(f"⚠️ {message}")
    
    @staticmethod
    def show_info(message: str):
        """显示信息消息"""
        st.info(f"💡 {message}")
