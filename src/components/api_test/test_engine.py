#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
测试执行引擎
"""

import time
import traceback
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from .models import *
from .executor import VariableManager, AssertionEngine, VariableExtractor, HttpRequestExecutor
from .database import db_manager, DatabaseAssertion, DatabaseDataPreparer


class TestEngine:
    """测试执行引擎"""
    
    def __init__(self):
        self.variable_manager = VariableManager()
        self.http_executor = HttpRequestExecutor(self.variable_manager)
        self.is_running = False
        self.stop_requested = False
        self.progress_callback: Optional[Callable] = None
        self.result_callback: Optional[Callable] = None
    
    def set_environment(self, environment: Environment):
        """设置执行环境"""
        if environment:
            # 设置环境变量
            for key, value in environment.variables.items():
                self.variable_manager.set_variable(key, value, "env")
    
    def set_progress_callback(self, callback: Callable[[int, int], None]):
        """设置进度回调"""
        self.progress_callback = callback
    
    def set_result_callback(self, callback: Callable[[ExecutionResult], None]):
        """设置结果回调"""
        self.result_callback = callback
    
    def execute_single_case(self, test_case: TestCase, 
                           environment: Environment = None) -> ExecutionResult:
        """执行单个测试用例"""
        result = ExecutionResult(
            case_id=test_case.id,
            case_name=test_case.name,
            start_time=datetime.now()
        )
        
        try:
            # 检查是否跳过
            if test_case.skip:
                result.status = ExecutionStatus.SKIPPED
                result.skip_reason = test_case.skip_reason
                result.end_time = datetime.now()
                return result
            
            # 执行前置操作
            pre_result = self._execute_pre_operations(test_case.pre_operations)
            if not pre_result["success"]:
                result.status = ExecutionStatus.ERROR
                result.error_message = f"前置操作失败: {pre_result['error']}"
                result.end_time = datetime.now()
                return result
            
            # 执行HTTP请求（支持重试）
            retry_count = 0
            max_retries = test_case.retry_count
            
            while retry_count <= max_retries:
                try:
                    # 发送请求
                    response, response_time = self.http_executor.execute_request(
                        test_case.request, environment
                    )
                    
                    # 记录请求信息
                    result.request_url = response.request.url
                    result.request_method = response.request.method
                    result.request_headers = dict(response.request.headers)
                    result.request_body = response.request.body or ""
                    
                    # 记录响应信息
                    result.response_status_code = response.status_code
                    result.response_headers = dict(response.headers)
                    result.response_body = response.text
                    result.response_time = response_time
                    result.response_size = len(response.content)
                    
                    # 提取变量
                    extracted_vars = VariableExtractor.extract_variables(
                        test_case.variable_extractions, response, self.variable_manager
                    )
                    result.extracted_variables = extracted_vars
                    
                    # 执行断言
                    assertion_results = []
                    all_passed = True

                    for assertion in test_case.assertions:
                        if assertion.assertion_type == AssertionType.DATABASE:
                            # 数据库断言
                            assertion_result = self._execute_database_assertion(assertion)
                        else:
                            # 普通断言
                            assertion_result = AssertionEngine.run_assertion(
                                assertion, response, response_time
                            )

                        assertion_results.append(assertion_result)
                        if not assertion_result["passed"]:
                            all_passed = False
                    
                    result.assertion_results = assertion_results
                    
                    # 设置执行状态
                    if all_passed:
                        result.status = ExecutionStatus.PASSED
                        break  # 成功则退出重试循环
                    else:
                        result.status = ExecutionStatus.FAILED
                        if retry_count < max_retries:
                            retry_count += 1
                            result.retry_count = retry_count
                            time.sleep(test_case.retry_interval)
                            continue
                        else:
                            break
                
                except Exception as e:
                    if retry_count < max_retries:
                        retry_count += 1
                        result.retry_count = retry_count
                        time.sleep(test_case.retry_interval)
                        continue
                    else:
                        result.status = ExecutionStatus.ERROR
                        result.error_message = str(e)
                        result.error_traceback = traceback.format_exc()
                        break
            
            # 执行后置操作
            self._execute_post_operations(test_case.post_operations)
        
        except Exception as e:
            result.status = ExecutionStatus.ERROR
            result.error_message = str(e)
            result.error_traceback = traceback.format_exc()
        
        finally:
            result.end_time = datetime.now()
            # 清理临时变量
            self.variable_manager.clear_temp_variables()
        
        return result
    
    def execute_multiple_cases(self, test_cases: List[TestCase], 
                              environment: Environment = None,
                              parallel: bool = False,
                              max_workers: int = 5,
                              stop_on_failure: bool = False) -> ExecutionReport:
        """执行多个测试用例"""
        report = ExecutionReport(
            name=f"测试执行报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            environment_id=environment.id if environment else "",
            environment_name=environment.name if environment else "",
            total_cases=len(test_cases),
            start_time=datetime.now()
        )
        
        self.is_running = True
        self.stop_requested = False
        
        try:
            if parallel and len(test_cases) > 1:
                # 并行执行
                results = self._execute_parallel(
                    test_cases, environment, max_workers, stop_on_failure, report
                )
            else:
                # 串行执行
                results = self._execute_sequential(
                    test_cases, environment, stop_on_failure, report
                )
            
            # 统计结果
            for result in results:
                report.case_results.append(result)
                
                if result.status == ExecutionStatus.PASSED:
                    report.passed_cases += 1
                elif result.status == ExecutionStatus.FAILED:
                    report.failed_cases += 1
                elif result.status == ExecutionStatus.SKIPPED:
                    report.skipped_cases += 1
                elif result.status == ExecutionStatus.ERROR:
                    report.error_cases += 1
        
        except Exception as e:
            print(f"执行过程中发生错误: {e}")
        
        finally:
            report.end_time = datetime.now()
            if report.start_time and report.end_time:
                report.total_duration = (report.end_time - report.start_time).total_seconds()
            
            self.is_running = False
            self.stop_requested = False
        
        return report
    
    def _execute_sequential(self, test_cases: List[TestCase], 
                           environment: Environment,
                           stop_on_failure: bool,
                           report: ExecutionReport) -> List[ExecutionResult]:
        """串行执行测试用例"""
        results = []
        
        for i, test_case in enumerate(test_cases):
            if self.stop_requested:
                break
            
            # 更新进度
            if self.progress_callback:
                self.progress_callback(i, len(test_cases))
            
            # 执行用例
            result = self.execute_single_case(test_case, environment)
            results.append(result)
            
            # 结果回调
            if self.result_callback:
                self.result_callback(result)
            
            # 检查是否需要在失败时停止
            if stop_on_failure and result.status == ExecutionStatus.FAILED:
                break
        
        return results
    
    def _execute_parallel(self, test_cases: List[TestCase],
                         environment: Environment,
                         max_workers: int,
                         stop_on_failure: bool,
                         report: ExecutionReport) -> List[ExecutionResult]:
        """并行执行测试用例"""
        results = []
        completed_count = 0
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_case = {
                executor.submit(self.execute_single_case, case, environment): case
                for case in test_cases
            }
            
            # 处理完成的任务
            for future in as_completed(future_to_case):
                if self.stop_requested:
                    # 取消未完成的任务
                    for f in future_to_case:
                        f.cancel()
                    break
                
                try:
                    result = future.result()
                    results.append(result)
                    completed_count += 1
                    
                    # 更新进度
                    if self.progress_callback:
                        self.progress_callback(completed_count, len(test_cases))
                    
                    # 结果回调
                    if self.result_callback:
                        self.result_callback(result)
                    
                    # 检查是否需要在失败时停止
                    if stop_on_failure and result.status == ExecutionStatus.FAILED:
                        self.stop_requested = True
                
                except Exception as e:
                    print(f"并行执行任务失败: {e}")
        
        return results
    
    def stop_execution(self):
        """停止执行"""
        self.stop_requested = True
    
    def _execute_pre_operations(self, operations: List[PrePostOperation]) -> Dict[str, Any]:
        """执行前置操作"""
        for operation in operations:
            if not operation.enabled:
                continue

            try:
                if operation.operation_type == "http_request":
                    # 执行HTTP请求
                    self._execute_http_operation(operation)
                elif operation.operation_type == "sql_script":
                    # 执行SQL脚本
                    self._execute_sql_operation(operation)
                elif operation.operation_type == "database_prepare":
                    # 数据库数据准备
                    self._execute_database_prepare_operation(operation)
                elif operation.operation_type == "custom_script":
                    # 执行自定义脚本
                    self._execute_custom_script_operation(operation)
            except Exception as e:
                return {"success": False, "error": str(e)}

        return {"success": True}

    def _execute_http_operation(self, operation: PrePostOperation):
        """执行HTTP操作"""
        config = operation.config
        if not config:
            return

        # 构建请求配置
        request_config = RequestConfig(
            method=RequestMethod(config.get("method", "GET")),
            url=config.get("url", ""),
            headers=[KeyValue(**h) for h in config.get("headers", [])],
            query_params=[KeyValue(**p) for p in config.get("query_params", [])],
            body_type=BodyType(config.get("body_type", "None")),
            body_data=config.get("body_data", "")
        )

        # 执行请求
        response, response_time = self.http_executor.execute_request(request_config)

        # 提取变量（如果配置了）
        if "variable_extractions" in config:
            extractions = [VariableExtraction(**v) for v in config["variable_extractions"]]
            VariableExtractor.extract_variables(extractions, response, self.variable_manager)

    def _execute_sql_operation(self, operation: PrePostOperation):
        """执行SQL操作"""
        config = operation.config
        if not config:
            return

        db_config_id = config.get("database_config_id")
        sql_script = config.get("sql_script", "")

        if not db_config_id or not sql_script:
            raise Exception("数据库配置ID和SQL脚本不能为空")

        # 替换变量
        sql_script = self.variable_manager.replace_variables(sql_script)

        # 执行SQL
        data_preparer = DatabaseDataPreparer(db_manager)
        results = data_preparer.execute_sql_script(db_config_id, sql_script)

        # 如果配置了变量提取，将查询结果保存到变量
        if "result_variable" in config and results:
            result_var_name = config["result_variable"]
            if results and isinstance(results[0], list) and results[0]:
                # 如果是查询结果，保存第一行的数据
                first_row = results[0][0]
                for key, value in first_row.items():
                    self.variable_manager.set_variable(f"{result_var_name}_{key}", str(value))

    def _execute_database_prepare_operation(self, operation: PrePostOperation):
        """执行数据库数据准备操作"""
        config = operation.config
        if not config:
            return

        db_config_id = config.get("database_config_id")
        operation_type = config.get("operation", "insert")  # insert, update, delete
        table = config.get("table", "")
        data = config.get("data", {})
        conditions = config.get("conditions", {})

        if not db_config_id or not table:
            raise Exception("数据库配置ID和表名不能为空")

        # 替换变量
        for key, value in data.items():
            data[key] = self.variable_manager.replace_variables(str(value))

        for key, value in conditions.items():
            conditions[key] = self.variable_manager.replace_variables(str(value))

        # 执行数据库操作
        data_preparer = DatabaseDataPreparer(db_manager)

        if operation_type == "insert":
            data_preparer.insert_test_data(db_config_id, table, data)
        elif operation_type == "update":
            data_preparer.update_test_data(db_config_id, table, data, conditions)
        elif operation_type == "delete":
            data_preparer.delete_test_data(db_config_id, table, conditions)

    def _execute_custom_script_operation(self, operation: PrePostOperation):
        """执行自定义脚本操作"""
        config = operation.config
        if not config:
            return

        script_type = config.get("script_type", "python")
        script_content = config.get("script_content", "")

        if not script_content:
            return

        # 替换变量
        script_content = self.variable_manager.replace_variables(script_content)

        if script_type == "python":
            # 执行Python脚本
            # 为了安全，这里只是示例，实际使用时需要考虑安全性
            local_vars = {
                "variables": self.variable_manager.get_all_variables(),
                "set_variable": self.variable_manager.set_variable
            }
            exec(script_content, {}, local_vars)

    def _execute_database_assertion(self, assertion: Assertion) -> Dict[str, Any]:
        """执行数据库断言"""
        result = {
            "assertion_id": assertion.id,
            "description": assertion.description,
            "passed": False,
            "actual_value": None,
            "expected_value": assertion.expected_value,
            "error_message": ""
        }

        try:
            if not assertion.enabled:
                result["passed"] = True
                result["error_message"] = "断言已禁用"
                return result

            # 解析断言配置
            # field_path 格式: "db_config_id:sql_query" 或 "db_config_id:table:conditions"
            if ":" not in assertion.field_path:
                result["error_message"] = "数据库断言配置格式错误"
                return result

            parts = assertion.field_path.split(":", 2)
            db_config_id = parts[0]

            if len(parts) == 2:
                # SQL查询模式: "db_config_id:sql_query"
                sql_query = self.variable_manager.replace_variables(parts[1])

                db_assertion = DatabaseAssertion(db_manager)

                if assertion.operator == "count_equals":
                    # 检查记录数量
                    expected_count = int(assertion.expected_value)
                    passed, message = db_assertion.assert_query_result(
                        db_config_id, sql_query, expected_count=expected_count
                    )
                elif assertion.operator == "record_exists":
                    # 检查记录存在
                    results = db_manager.execute_query(db_config_id, sql_query)
                    passed = len(results) > 0
                    message = f"查询结果数量: {len(results)}"
                elif assertion.operator == "record_not_exists":
                    # 检查记录不存在
                    results = db_manager.execute_query(db_config_id, sql_query)
                    passed = len(results) == 0
                    message = f"查询结果数量: {len(results)}"
                elif assertion.operator == "field_equals":
                    # 检查字段值
                    results = db_manager.execute_query(db_config_id, sql_query)
                    if results:
                        # 假设检查第一行第一个字段
                        first_row = results[0]
                        first_value = list(first_row.values())[0] if first_row else None
                        passed = str(first_value) == assertion.expected_value
                        message = f"实际值: {first_value}, 期望值: {assertion.expected_value}"
                        result["actual_value"] = first_value
                    else:
                        passed = False
                        message = "查询无结果"
                else:
                    passed = False
                    message = f"不支持的数据库断言操作符: {assertion.operator}"

                result["passed"] = passed
                result["error_message"] = message if not passed else ""

            elif len(parts) == 3:
                # 表记录模式: "db_config_id:table:conditions"
                table = parts[1]
                conditions_str = self.variable_manager.replace_variables(parts[2])

                # 解析条件 (简单的key=value格式)
                conditions = {}
                for condition in conditions_str.split(","):
                    if "=" in condition:
                        key, value = condition.split("=", 1)
                        conditions[key.strip()] = value.strip()

                db_assertion = DatabaseAssertion(db_manager)

                if assertion.operator == "record_exists":
                    passed, message = db_assertion.assert_record_exists(db_config_id, table, conditions)
                elif assertion.operator == "record_not_exists":
                    passed, message = db_assertion.assert_record_not_exists(db_config_id, table, conditions)
                else:
                    passed = False
                    message = f"不支持的表记录断言操作符: {assertion.operator}"

                result["passed"] = passed
                result["error_message"] = message if not passed else ""

            else:
                result["error_message"] = "数据库断言配置格式错误"

        except Exception as e:
            result["passed"] = False
            result["error_message"] = f"数据库断言执行错误: {str(e)}"

        return result
    
    def _execute_post_operations(self, operations: List[PrePostOperation]):
        """执行后置操作"""
        for operation in operations:
            if not operation.enabled:
                continue

            try:
                if operation.operation_type == "http_request":
                    # 执行HTTP请求
                    self._execute_http_operation(operation)
                elif operation.operation_type == "sql_script":
                    # 执行SQL脚本
                    self._execute_sql_operation(operation)
                elif operation.operation_type == "database_prepare":
                    # 数据库数据准备
                    self._execute_database_prepare_operation(operation)
                elif operation.operation_type == "custom_script":
                    # 执行自定义脚本
                    self._execute_custom_script_operation(operation)
            except Exception as e:
                print(f"后置操作执行失败: {e}")


class ExecutionManager:
    """执行管理器"""
    
    def __init__(self):
        self.engines: Dict[str, TestEngine] = {}
        self.lock = threading.Lock()
    
    def create_engine(self, session_id: str) -> TestEngine:
        """创建执行引擎"""
        with self.lock:
            engine = TestEngine()
            self.engines[session_id] = engine
            return engine
    
    def get_engine(self, session_id: str) -> Optional[TestEngine]:
        """获取执行引擎"""
        return self.engines.get(session_id)
    
    def remove_engine(self, session_id: str):
        """移除执行引擎"""
        with self.lock:
            if session_id in self.engines:
                engine = self.engines[session_id]
                engine.stop_execution()
                del self.engines[session_id]
    
    def stop_all_engines(self):
        """停止所有执行引擎"""
        with self.lock:
            for engine in self.engines.values():
                engine.stop_execution()
            self.engines.clear()


# 全局执行管理器实例
execution_manager = ExecutionManager()
