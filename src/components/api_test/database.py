#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
数据库集成模块
"""

import sqlite3
from typing import Dict, List, Any, Optional, Tuple
import json
import traceback
from dataclasses import dataclass
from enum import Enum

# 可选的数据库驱动
try:
    import pymysql
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False

try:
    import psycopg2
    POSTGRESQL_AVAILABLE = True
except ImportError:
    POSTGRESQL_AVAILABLE = False

try:
    import cx_Oracle
    ORACLE_AVAILABLE = True
except ImportError:
    ORACLE_AVAILABLE = False


class DatabaseType(Enum):
    """数据库类型枚举"""
    MYSQL = "MySQL"
    POSTGRESQL = "PostgreSQL"
    SQLITE = "SQLite"
    ORACLE = "Oracle"
    SQL_SERVER = "SQL Server"


@dataclass
class DatabaseConfig:
    """数据库配置"""
    id: str
    name: str
    db_type: DatabaseType
    host: str = ""
    port: int = 3306
    database: str = ""
    username: str = ""
    password: str = ""
    charset: str = "utf8mb4"
    connection_params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.connection_params is None:
            self.connection_params = {}


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.connections: Dict[str, Any] = {}
        self.configs: Dict[str, DatabaseConfig] = {}
    
    def add_config(self, config: DatabaseConfig):
        """添加数据库配置"""
        self.configs[config.id] = config
    
    def get_connection(self, config_id: str) -> Any:
        """获取数据库连接"""
        if config_id not in self.configs:
            raise Exception(f"数据库配置不存在: {config_id}")
        
        config = self.configs[config_id]
        
        # 检查是否已有连接
        if config_id in self.connections:
            conn = self.connections[config_id]
            try:
                # 测试连接是否有效
                if config.db_type == DatabaseType.MYSQL:
                    conn.ping(reconnect=True)
                elif config.db_type == DatabaseType.POSTGRESQL:
                    conn.status  # 检查连接状态
                elif config.db_type == DatabaseType.SQLITE:
                    conn.execute("SELECT 1")
                
                return conn
            except:
                # 连接无效，重新创建
                self._close_connection(config_id)
        
        # 创建新连接
        conn = self._create_connection(config)
        self.connections[config_id] = conn
        return conn
    
    def _create_connection(self, config: DatabaseConfig) -> Any:
        """创建数据库连接"""
        try:
            if config.db_type == DatabaseType.MYSQL:
                if not MYSQL_AVAILABLE:
                    raise Exception("MySQL驱动未安装，请运行: pip install pymysql")
                return pymysql.connect(
                    host=config.host,
                    port=config.port,
                    user=config.username,
                    password=config.password,
                    database=config.database,
                    charset=config.charset,
                    autocommit=True,
                    **config.connection_params
                )

            elif config.db_type == DatabaseType.POSTGRESQL:
                if not POSTGRESQL_AVAILABLE:
                    raise Exception("PostgreSQL驱动未安装，请运行: pip install psycopg2-binary")
                return psycopg2.connect(
                    host=config.host,
                    port=config.port,
                    user=config.username,
                    password=config.password,
                    database=config.database,
                    **config.connection_params
                )

            elif config.db_type == DatabaseType.SQLITE:
                return sqlite3.connect(
                    config.database,
                    **config.connection_params
                )

            elif config.db_type == DatabaseType.ORACLE:
                if not ORACLE_AVAILABLE:
                    raise Exception("Oracle驱动未安装，请运行: pip install cx_Oracle")
                dsn = cx_Oracle.makedsn(config.host, config.port, service_name=config.database)
                return cx_Oracle.connect(
                    user=config.username,
                    password=config.password,
                    dsn=dsn,
                    **config.connection_params
                )

            else:
                raise Exception(f"不支持的数据库类型: {config.db_type}")

        except Exception as e:
            raise Exception(f"数据库连接失败: {e}")
    
    def execute_query(self, config_id: str, sql: str, params: Tuple = None) -> List[Dict[str, Any]]:
        """执行查询SQL"""
        conn = self.get_connection(config_id)
        cursor = conn.cursor()
        
        try:
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)
            
            # 获取列名
            if cursor.description:
                columns = [desc[0] for desc in cursor.description]
                # 获取数据
                rows = cursor.fetchall()
                # 转换为字典列表
                result = []
                for row in rows:
                    result.append(dict(zip(columns, row)))
                return result
            else:
                return []
        
        except Exception as e:
            raise Exception(f"SQL查询失败: {e}")
        finally:
            cursor.close()
    
    def execute_update(self, config_id: str, sql: str, params: Tuple = None) -> int:
        """执行更新SQL"""
        conn = self.get_connection(config_id)
        cursor = conn.cursor()
        
        try:
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)
            
            # 提交事务
            if hasattr(conn, 'commit'):
                conn.commit()
            
            return cursor.rowcount
        
        except Exception as e:
            # 回滚事务
            if hasattr(conn, 'rollback'):
                conn.rollback()
            raise Exception(f"SQL执行失败: {e}")
        finally:
            cursor.close()
    
    def test_connection(self, config: DatabaseConfig) -> Tuple[bool, str]:
        """测试数据库连接"""
        try:
            conn = self._create_connection(config)
            cursor = conn.cursor()
            
            # 执行简单查询测试
            if config.db_type == DatabaseType.MYSQL:
                if not MYSQL_AVAILABLE:
                    raise Exception("MySQL驱动未安装")
                cursor.execute("SELECT 1")
            elif config.db_type == DatabaseType.POSTGRESQL:
                if not POSTGRESQL_AVAILABLE:
                    raise Exception("PostgreSQL驱动未安装")
                cursor.execute("SELECT 1")
            elif config.db_type == DatabaseType.SQLITE:
                cursor.execute("SELECT 1")
            elif config.db_type == DatabaseType.ORACLE:
                if not ORACLE_AVAILABLE:
                    raise Exception("Oracle驱动未安装")
                cursor.execute("SELECT 1 FROM DUAL")
            
            cursor.fetchone()
            cursor.close()
            conn.close()
            
            return True, "连接成功"
        
        except Exception as e:
            return False, f"连接失败: {e}"
    
    def _close_connection(self, config_id: str):
        """关闭数据库连接"""
        if config_id in self.connections:
            try:
                self.connections[config_id].close()
            except:
                pass
            del self.connections[config_id]
    
    def close_all_connections(self):
        """关闭所有数据库连接"""
        for config_id in list(self.connections.keys()):
            self._close_connection(config_id)


class DatabaseAssertion:
    """数据库断言"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    def assert_query_result(self, config_id: str, sql: str, expected_count: int = None,
                           expected_values: Dict[str, Any] = None,
                           params: Tuple = None) -> Tuple[bool, str]:
        """断言查询结果"""
        try:
            results = self.db_manager.execute_query(config_id, sql, params)
            
            # 检查记录数量
            if expected_count is not None:
                if len(results) != expected_count:
                    return False, f"期望记录数: {expected_count}, 实际记录数: {len(results)}"
            
            # 检查字段值
            if expected_values and results:
                first_row = results[0]
                for field, expected_value in expected_values.items():
                    if field not in first_row:
                        return False, f"字段不存在: {field}"
                    
                    actual_value = first_row[field]
                    if str(actual_value) != str(expected_value):
                        return False, f"字段 {field} 期望值: {expected_value}, 实际值: {actual_value}"
            
            return True, "数据库断言通过"
        
        except Exception as e:
            return False, f"数据库断言失败: {e}"
    
    def assert_record_exists(self, config_id: str, table: str, conditions: Dict[str, Any]) -> Tuple[bool, str]:
        """断言记录存在"""
        try:
            where_clause = " AND ".join([f"{k} = %s" for k in conditions.keys()])
            sql = f"SELECT COUNT(*) as count FROM {table} WHERE {where_clause}"
            params = tuple(conditions.values())
            
            results = self.db_manager.execute_query(config_id, sql, params)
            count = results[0]['count'] if results else 0
            
            if count > 0:
                return True, f"记录存在，数量: {count}"
            else:
                return False, "记录不存在"
        
        except Exception as e:
            return False, f"断言记录存在失败: {e}"
    
    def assert_record_not_exists(self, config_id: str, table: str, conditions: Dict[str, Any]) -> Tuple[bool, str]:
        """断言记录不存在"""
        exists, message = self.assert_record_exists(config_id, table, conditions)
        if exists:
            return False, "记录不应该存在，但实际存在"
        else:
            return True, "记录不存在，断言通过"


class DatabaseDataPreparer:
    """数据库数据准备器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    def insert_test_data(self, config_id: str, table: str, data: Dict[str, Any]) -> bool:
        """插入测试数据"""
        try:
            fields = list(data.keys())
            values = list(data.values())
            placeholders = ", ".join(["%s"] * len(fields))
            
            sql = f"INSERT INTO {table} ({', '.join(fields)}) VALUES ({placeholders})"
            
            self.db_manager.execute_update(config_id, sql, tuple(values))
            return True
        
        except Exception as e:
            raise Exception(f"插入测试数据失败: {e}")
    
    def update_test_data(self, config_id: str, table: str, data: Dict[str, Any], 
                        conditions: Dict[str, Any]) -> int:
        """更新测试数据"""
        try:
            set_clause = ", ".join([f"{k} = %s" for k in data.keys()])
            where_clause = " AND ".join([f"{k} = %s" for k in conditions.keys()])
            
            sql = f"UPDATE {table} SET {set_clause} WHERE {where_clause}"
            params = tuple(list(data.values()) + list(conditions.values()))
            
            return self.db_manager.execute_update(config_id, sql, params)
        
        except Exception as e:
            raise Exception(f"更新测试数据失败: {e}")
    
    def delete_test_data(self, config_id: str, table: str, conditions: Dict[str, Any]) -> int:
        """删除测试数据"""
        try:
            where_clause = " AND ".join([f"{k} = %s" for k in conditions.keys()])
            sql = f"DELETE FROM {table} WHERE {where_clause}"
            params = tuple(conditions.values())
            
            return self.db_manager.execute_update(config_id, sql, params)
        
        except Exception as e:
            raise Exception(f"删除测试数据失败: {e}")
    
    def execute_sql_script(self, config_id: str, sql_script: str) -> List[Any]:
        """执行SQL脚本"""
        try:
            results = []
            # 分割多个SQL语句
            statements = [stmt.strip() for stmt in sql_script.split(';') if stmt.strip()]
            
            for sql in statements:
                if sql.upper().startswith(('SELECT', 'SHOW', 'DESCRIBE', 'EXPLAIN')):
                    # 查询语句
                    result = self.db_manager.execute_query(config_id, sql)
                    results.append(result)
                else:
                    # 更新语句
                    affected_rows = self.db_manager.execute_update(config_id, sql)
                    results.append({"affected_rows": affected_rows})
            
            return results
        
        except Exception as e:
            raise Exception(f"执行SQL脚本失败: {e}")


# 全局数据库管理器实例
db_manager = DatabaseManager()
