#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
通知模块 - 支持钉钉、企业微信、邮件等通知方式
"""

import requests
import json
import time
import hmac
import hashlib
import base64
import urllib.parse
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum


class NotificationType(Enum):
    """通知类型"""
    DINGTALK = "dingtalk"
    WECHAT_WORK = "wechat_work"
    EMAIL = "email"
    WEBHOOK = "webhook"


@dataclass
class NotificationConfig:
    """通知配置"""
    name: str
    notification_type: NotificationType
    webhook_url: str
    secret: Optional[str] = None
    access_token: Optional[str] = None
    enabled: bool = True
    
    # 钉钉特有配置
    at_mobiles: List[str] = None
    at_all: bool = False
    
    # 企业微信特有配置
    mentioned_list: List[str] = None
    mentioned_mobile_list: List[str] = None


class NotificationManager:
    """通知管理器"""
    
    def __init__(self):
        self.configs: Dict[str, NotificationConfig] = {}
    
    def add_config(self, config: NotificationConfig):
        """添加通知配置"""
        self.configs[config.name] = config
    
    def remove_config(self, name: str):
        """移除通知配置"""
        if name in self.configs:
            del self.configs[name]
    
    def send_test_result_notification(self, report_data: Dict[str, Any], 
                                    config_names: List[str] = None) -> Dict[str, bool]:
        """发送测试结果通知"""
        results = {}
        
        # 如果没有指定配置，使用所有启用的配置
        if config_names is None:
            config_names = [name for name, config in self.configs.items() if config.enabled]
        
        for config_name in config_names:
            if config_name in self.configs:
                config = self.configs[config_name]
                try:
                    if config.notification_type == NotificationType.DINGTALK:
                        success = self._send_dingtalk_notification(config, report_data)
                    elif config.notification_type == NotificationType.WECHAT_WORK:
                        success = self._send_wechat_work_notification(config, report_data)
                    elif config.notification_type == NotificationType.WEBHOOK:
                        success = self._send_webhook_notification(config, report_data)
                    else:
                        success = False
                    
                    results[config_name] = success
                except Exception as e:
                    print(f"发送通知失败 ({config_name}): {e}")
                    results[config_name] = False
            else:
                results[config_name] = False
        
        return results
    
    def _send_dingtalk_notification(self, config: NotificationConfig, 
                                  report_data: Dict[str, Any]) -> bool:
        """发送钉钉通知"""
        try:
            # 构建消息内容
            message = self._build_dingtalk_message(report_data, config)
            
            # 计算签名
            timestamp = str(round(time.time() * 1000))
            headers = {'Content-Type': 'application/json'}
            url = config.webhook_url
            
            if config.secret:
                sign = self._calculate_dingtalk_sign(timestamp, config.secret)
                url += f"&timestamp={timestamp}&sign={sign}"
            
            # 发送请求
            response = requests.post(url, headers=headers, json=message, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                return result.get('errcode') == 0
            
            return False
            
        except Exception as e:
            print(f"钉钉通知发送失败: {e}")
            return False
    
    def _send_wechat_work_notification(self, config: NotificationConfig, 
                                     report_data: Dict[str, Any]) -> bool:
        """发送企业微信通知"""
        try:
            # 构建消息内容
            message = self._build_wechat_work_message(report_data, config)
            
            headers = {'Content-Type': 'application/json'}
            
            # 发送请求
            response = requests.post(config.webhook_url, headers=headers, 
                                   json=message, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                return result.get('errcode') == 0
            
            return False
            
        except Exception as e:
            print(f"企业微信通知发送失败: {e}")
            return False
    
    def _send_webhook_notification(self, config: NotificationConfig, 
                                 report_data: Dict[str, Any]) -> bool:
        """发送Webhook通知"""
        try:
            headers = {'Content-Type': 'application/json'}
            
            # 发送原始报告数据
            response = requests.post(config.webhook_url, headers=headers, 
                                   json=report_data, timeout=10)
            
            return response.status_code == 200
            
        except Exception as e:
            print(f"Webhook通知发送失败: {e}")
            return False
    
    def _build_dingtalk_message(self, report_data: Dict[str, Any], 
                              config: NotificationConfig) -> Dict[str, Any]:
        """构建钉钉消息"""
        # 计算通过率
        total_cases = report_data.get('total_cases', 0)
        passed_cases = report_data.get('passed_cases', 0)
        failed_cases = report_data.get('failed_cases', 0)
        pass_rate = (passed_cases / total_cases * 100) if total_cases > 0 else 0
        
        # 判断测试结果
        if failed_cases == 0:
            status_emoji = "✅"
            status_text = "测试通过"
            color = "#00CC96"
        else:
            status_emoji = "❌"
            status_text = "测试失败"
            color = "#EF553B"
        
        # 构建Markdown消息
        markdown_text = f"""
# {status_emoji} 接口自动化测试报告

**项目**: {report_data.get('project_name', '未知项目')}
**环境**: {report_data.get('environment_name', '未知环境')}
**状态**: {status_text}

## 📊 执行统计
- **总用例数**: {total_cases}
- **通过数**: {passed_cases}
- **失败数**: {failed_cases}
- **跳过数**: {report_data.get('skipped_cases', 0)}
- **通过率**: {pass_rate:.1f}%

## ⏱️ 执行信息
- **开始时间**: {report_data.get('start_time', '')}
- **结束时间**: {report_data.get('end_time', '')}
- **执行时长**: {report_data.get('total_duration', 0):.1f}秒

"""
        
        # 添加失败用例详情
        failed_cases_list = report_data.get('failed_cases_detail', [])
        if failed_cases_list:
            markdown_text += "\n## ❌ 失败用例\n"
            for case in failed_cases_list[:5]:  # 最多显示5个失败用例
                markdown_text += f"- **{case.get('case_name', '')}**: {case.get('error_message', '')}\n"
            
            if len(failed_cases_list) > 5:
                markdown_text += f"- ... 还有 {len(failed_cases_list) - 5} 个失败用例\n"
        
        # 添加报告链接
        if report_data.get('report_url'):
            markdown_text += f"\n[📊 查看详细报告]({report_data['report_url']})"
        
        message = {
            "msgtype": "markdown",
            "markdown": {
                "title": f"{status_emoji} 接口测试报告 - {status_text}",
                "text": markdown_text
            }
        }
        
        # 添加@功能
        if config.at_all or config.at_mobiles:
            message["at"] = {
                "isAtAll": config.at_all,
                "atMobiles": config.at_mobiles or []
            }
        
        return message
    
    def _build_wechat_work_message(self, report_data: Dict[str, Any], 
                                 config: NotificationConfig) -> Dict[str, Any]:
        """构建企业微信消息"""
        # 计算通过率
        total_cases = report_data.get('total_cases', 0)
        passed_cases = report_data.get('passed_cases', 0)
        failed_cases = report_data.get('failed_cases', 0)
        pass_rate = (passed_cases / total_cases * 100) if total_cases > 0 else 0
        
        # 判断测试结果
        if failed_cases == 0:
            status_emoji = "✅"
            status_text = "测试通过"
        else:
            status_emoji = "❌"
            status_text = "测试失败"
        
        # 构建Markdown消息
        content = f"""# {status_emoji} 接口自动化测试报告

**项目**: {report_data.get('project_name', '未知项目')}
**环境**: {report_data.get('environment_name', '未知环境')}
**状态**: {status_text}
**通过率**: {pass_rate:.1f}%

**执行统计**: 总计{total_cases}个用例，通过{passed_cases}个，失败{failed_cases}个
**执行时长**: {report_data.get('total_duration', 0):.1f}秒
"""
        
        message = {
            "msgtype": "markdown",
            "markdown": {
                "content": content
            }
        }
        
        # 添加@功能
        if config.mentioned_list or config.mentioned_mobile_list:
            message["markdown"]["mentioned_list"] = config.mentioned_list or []
            message["markdown"]["mentioned_mobile_list"] = config.mentioned_mobile_list or []
        
        return message
    
    def _calculate_dingtalk_sign(self, timestamp: str, secret: str) -> str:
        """计算钉钉签名"""
        string_to_sign = f"{timestamp}\n{secret}"
        hmac_code = hmac.new(
            secret.encode('utf-8'),
            string_to_sign.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
        return sign


# 全局通知管理器实例
notification_manager = NotificationManager()


def send_test_notification(report_data: Dict[str, Any], 
                         config_names: List[str] = None) -> Dict[str, bool]:
    """发送测试通知的便捷函数"""
    return notification_manager.send_test_result_notification(report_data, config_names)


def add_dingtalk_config(name: str, webhook_url: str, secret: str = None,
                       at_mobiles: List[str] = None, at_all: bool = False):
    """添加钉钉通知配置的便捷函数"""
    config = NotificationConfig(
        name=name,
        notification_type=NotificationType.DINGTALK,
        webhook_url=webhook_url,
        secret=secret,
        at_mobiles=at_mobiles or [],
        at_all=at_all
    )
    notification_manager.add_config(config)


def add_wechat_work_config(name: str, webhook_url: str,
                          mentioned_list: List[str] = None,
                          mentioned_mobile_list: List[str] = None):
    """添加企业微信通知配置的便捷函数"""
    config = NotificationConfig(
        name=name,
        notification_type=NotificationType.WECHAT_WORK,
        webhook_url=webhook_url,
        mentioned_list=mentioned_list or [],
        mentioned_mobile_list=mentioned_mobile_list or []
    )
    notification_manager.add_config(config)
