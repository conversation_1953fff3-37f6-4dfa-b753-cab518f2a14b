#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
接口自动化测试数据访问层
"""

import sqlite3
import json
import os
from typing import List, Optional, Dict, Any
from datetime import datetime
from .models import *


class ApiTestDAO:
    """接口测试数据访问对象"""

    def __init__(self, db_path: str = "data/api_test.db"):
        self.db_path = db_path
        self._ensure_db_dir()
        self._init_database()

    def _serialize_request_config(self, request: RequestConfig) -> Dict[str, Any]:
        """序列化请求配置"""
        return {
            "method": request.method.value,
            "url": request.url,
            "protocol": request.protocol.value,
            "headers": [h.__dict__ for h in request.headers],
            "query_params": [p.__dict__ for p in request.query_params],
            "body_type": request.body_type.value,
            "body_data": request.body_data,
            "auth": {
                "auth_type": request.auth.auth_type.value,
                "username": request.auth.username,
                "password": request.auth.password,
                "token": request.auth.token,
                "api_key": request.auth.api_key,
                "api_key_location": request.auth.api_key_location,
                "api_key_name": request.auth.api_key_name,
                "oauth2_config": request.auth.oauth2_config,
                "custom_config": request.auth.custom_config
            },
            "timeout": request.timeout,
            "allow_redirects": request.allow_redirects,
            "verify_ssl": request.verify_ssl
        }

    def _deserialize_request_config(self, data: Dict[str, Any]) -> RequestConfig:
        """反序列化请求配置"""
        headers = [KeyValue(**h) for h in data.get("headers", [])]
        query_params = [KeyValue(**p) for p in data.get("query_params", [])]

        auth_data = data.get("auth", {})
        auth = AuthConfig(
            auth_type=AuthType(auth_data.get("auth_type", "None")),
            username=auth_data.get("username", ""),
            password=auth_data.get("password", ""),
            token=auth_data.get("token", ""),
            api_key=auth_data.get("api_key", ""),
            api_key_location=auth_data.get("api_key_location", "header"),
            api_key_name=auth_data.get("api_key_name", "X-API-Key"),
            oauth2_config=auth_data.get("oauth2_config", {}),
            custom_config=auth_data.get("custom_config", {})
        )

        return RequestConfig(
            method=RequestMethod(data.get("method", "GET")),
            url=data.get("url", ""),
            protocol=Protocol(data.get("protocol", "HTTPS")),
            headers=headers,
            query_params=query_params,
            body_type=BodyType(data.get("body_type", "None")),
            body_data=data.get("body_data", ""),
            auth=auth,
            timeout=data.get("timeout", 30),
            allow_redirects=data.get("allow_redirects", True),
            verify_ssl=data.get("verify_ssl", True)
        )

    def _serialize_assertion(self, assertion: Assertion) -> Dict[str, Any]:
        """序列化断言"""
        return {
            "id": assertion.id,
            "assertion_type": assertion.assertion_type.value,
            "field_path": assertion.field_path,
            "operator": assertion.operator,
            "expected_value": assertion.expected_value,
            "enabled": assertion.enabled,
            "description": assertion.description
        }

    def _deserialize_assertion(self, data: Dict[str, Any]) -> Assertion:
        """反序列化断言"""
        return Assertion(
            id=data.get("id", ""),
            assertion_type=AssertionType(data.get("assertion_type", "Status Code")),
            field_path=data.get("field_path", ""),
            operator=data.get("operator", "equals"),
            expected_value=data.get("expected_value", ""),
            enabled=data.get("enabled", True),
            description=data.get("description", "")
        )

    def _serialize_variable_extraction(self, extraction: VariableExtraction) -> Dict[str, Any]:
        """序列化变量提取"""
        return {
            "id": extraction.id,
            "variable_name": extraction.variable_name,
            "source": extraction.source.value if hasattr(extraction.source, 'value') else extraction.source,
            "extraction_method": extraction.extraction_method.value if hasattr(extraction.extraction_method, 'value') else extraction.extraction_method,
            "expression": extraction.expression,
            "default_value": extraction.default_value,
            "enabled": extraction.enabled,
            "description": getattr(extraction, 'description', '')
        }

    def _deserialize_variable_extraction(self, data: Dict[str, Any]) -> VariableExtraction:
        """反序列化变量提取"""
        # 处理枚举类型
        source = data.get("source", "response_body")
        if isinstance(source, str):
            try:
                source = VariableSource(source)
            except ValueError:
                source = VariableSource.RESPONSE_BODY

        extraction_method = data.get("extraction_method", "json_path")
        if isinstance(extraction_method, str):
            try:
                extraction_method = ExtractionMethod(extraction_method)
            except ValueError:
                extraction_method = ExtractionMethod.JSON_PATH

        return VariableExtraction(
            id=data.get("id", ""),
            variable_name=data.get("variable_name", ""),
            source=source,
            extraction_method=extraction_method,
            expression=data.get("expression", ""),
            default_value=data.get("default_value", ""),
            enabled=data.get("enabled", True),
            description=data.get("description", "")
        )

    def _serialize_pre_post_operation(self, operation: PrePostOperation) -> Dict[str, Any]:
        """序列化前后置操作"""
        return {
            "id": operation.id,
            "operation_type": operation.operation_type,
            "config": operation.config,
            "enabled": operation.enabled,
            "description": operation.description
        }

    def _deserialize_pre_post_operation(self, data: Dict[str, Any]) -> PrePostOperation:
        """反序列化前后置操作"""
        return PrePostOperation(
            id=data.get("id", ""),
            operation_type=data.get("operation_type", "http_request"),
            config=data.get("config", {}),
            enabled=data.get("enabled", True),
            description=data.get("description", "")
        )
    
    def _ensure_db_dir(self):
        """确保数据库目录存在"""
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
    
    def _init_database(self):
        """初始化数据库表"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 项目表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS projects (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    created_at TEXT,
                    updated_at TEXT,
                    created_by TEXT
                )
            ''')
            
            # 模块表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS modules (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    project_id TEXT,
                    parent_id TEXT,
                    created_at TEXT,
                    updated_at TEXT,
                    FOREIGN KEY (project_id) REFERENCES projects (id)
                )
            ''')
            
            # 环境表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS environments (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    base_url TEXT,
                    variables TEXT,
                    headers TEXT,
                    timeout INTEGER,
                    is_default INTEGER,
                    created_at TEXT,
                    updated_at TEXT
                )
            ''')
            
            # 测试用例表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS test_cases (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    project_id TEXT,
                    module_id TEXT,
                    tags TEXT,
                    priority TEXT,
                    status TEXT,
                    request_config TEXT,
                    assertions TEXT,
                    variable_extractions TEXT,
                    pre_operations TEXT,
                    post_operations TEXT,
                    skip INTEGER,
                    skip_reason TEXT,
                    retry_count INTEGER,
                    retry_interval INTEGER,
                    created_at TEXT,
                    updated_at TEXT,
                    created_by TEXT,
                    updated_by TEXT,
                    version INTEGER,
                    FOREIGN KEY (project_id) REFERENCES projects (id),
                    FOREIGN KEY (module_id) REFERENCES modules (id)
                )
            ''')

            # 添加数据库断言字段（如果不存在）
            try:
                cursor.execute('ALTER TABLE test_cases ADD COLUMN database_assertions TEXT')
            except sqlite3.OperationalError:
                # 字段已存在，忽略错误
                pass

            # 修复现有的变量提取数据
            self._fix_variable_extractions(cursor)
            
            # 测试套件表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS test_suites (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    project_id TEXT,
                    case_ids TEXT,
                    tags TEXT,
                    parallel_execution INTEGER,
                    max_workers INTEGER,
                    stop_on_failure INTEGER,
                    created_at TEXT,
                    updated_at TEXT,
                    FOREIGN KEY (project_id) REFERENCES projects (id)
                )
            ''')
            
            # 执行报告表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS execution_reports (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    environment_id TEXT,
                    environment_name TEXT,
                    total_cases INTEGER,
                    passed_cases INTEGER,
                    failed_cases INTEGER,
                    skipped_cases INTEGER,
                    error_cases INTEGER,
                    start_time TEXT,
                    end_time TEXT,
                    total_duration REAL,
                    case_results TEXT,
                    created_by TEXT,
                    tags TEXT
                )
            ''')

            # 数据库配置表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS database_configs (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    db_type TEXT NOT NULL,
                    host TEXT,
                    port INTEGER,
                    database_name TEXT,
                    username TEXT,
                    password TEXT,
                    charset TEXT,
                    connection_params TEXT,
                    created_at TEXT,
                    updated_at TEXT
                )
            ''')

            conn.commit()

            # 修复现有的变量提取数据
            self._fix_variable_extractions(cursor)
            conn.commit()

    def _fix_variable_extractions(self, cursor):
        """修复现有的变量提取数据，添加缺失的字段"""
        try:
            # 获取所有有变量提取配置的用例
            cursor.execute('SELECT id, variable_extractions FROM test_cases WHERE variable_extractions IS NOT NULL AND variable_extractions != ""')
            rows = cursor.fetchall()

            for row in rows:
                case_id, extractions_json = row

                try:
                    extractions_data = json.loads(extractions_json)
                    needs_update = False

                    for extraction in extractions_data:
                        # 添加缺失的description字段
                        if 'description' not in extraction:
                            extraction['description'] = ""
                            needs_update = True

                        # 添加缺失的id字段
                        if 'id' not in extraction:
                            extraction['id'] = f"var_extract_{datetime.now().timestamp()}"
                            needs_update = True

                        # 添加缺失的default_value字段
                        if 'default_value' not in extraction:
                            extraction['default_value'] = ""
                            needs_update = True

                    # 如果需要更新，保存回数据库
                    if needs_update:
                        updated_extractions_json = json.dumps(extractions_data)
                        cursor.execute(
                            'UPDATE test_cases SET variable_extractions = ? WHERE id = ?',
                            (updated_extractions_json, case_id)
                        )

                except (json.JSONDecodeError, Exception):
                    # 忽略错误，继续处理下一个
                    continue

        except Exception:
            # 忽略修复过程中的错误
            pass

    # 项目管理
    def create_project(self, project: Project) -> bool:
        """创建项目"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO projects (id, name, description, created_at, updated_at, created_by)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    project.id, project.name, project.description,
                    project.created_at.isoformat(), project.updated_at.isoformat(),
                    project.created_by
                ))
                conn.commit()
                return True
        except Exception as e:
            print(f"创建项目失败: {e}")
            return False
    
    def get_projects(self) -> List[Project]:
        """获取所有项目"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM projects ORDER BY created_at DESC')
                rows = cursor.fetchall()
                
                projects = []
                for row in rows:
                    project = Project(
                        id=row[0], name=row[1], description=row[2] or "",
                        created_at=datetime.fromisoformat(row[3]),
                        updated_at=datetime.fromisoformat(row[4]),
                        created_by=row[5] or ""
                    )
                    projects.append(project)
                
                return projects
        except Exception as e:
            print(f"获取项目列表失败: {e}")
            return []
    
    def get_project_by_id(self, project_id: str) -> Optional[Project]:
        """根据ID获取项目"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM projects WHERE id = ?', (project_id,))
                row = cursor.fetchone()
                
                if row:
                    return Project(
                        id=row[0], name=row[1], description=row[2] or "",
                        created_at=datetime.fromisoformat(row[3]),
                        updated_at=datetime.fromisoformat(row[4]),
                        created_by=row[5] or ""
                    )
                return None
        except Exception as e:
            print(f"获取项目失败: {e}")
            return None
    
    def update_project(self, project: Project) -> bool:
        """更新项目"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE projects 
                    SET name = ?, description = ?, updated_at = ?
                    WHERE id = ?
                ''', (project.name, project.description, datetime.now().isoformat(), project.id))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"更新项目失败: {e}")
            return False
    
    def delete_project(self, project_id: str) -> bool:
        """删除项目"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM projects WHERE id = ?', (project_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"删除项目失败: {e}")
            return False
    
    # 环境管理
    def create_environment(self, env: Environment) -> bool:
        """创建环境"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO environments 
                    (id, name, description, base_url, variables, headers, timeout, is_default, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    env.id, env.name, env.description, env.base_url,
                    json.dumps(env.variables), json.dumps([h.__dict__ for h in env.headers]),
                    env.timeout, 1 if env.is_default else 0,
                    env.created_at.isoformat(), env.updated_at.isoformat()
                ))
                conn.commit()
                return True
        except Exception as e:
            print(f"创建环境失败: {e}")
            return False
    
    def get_environments(self) -> List[Environment]:
        """获取所有环境"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM environments ORDER BY is_default DESC, created_at DESC')
                rows = cursor.fetchall()
                
                environments = []
                for row in rows:
                    variables = json.loads(row[4]) if row[4] else {}
                    headers_data = json.loads(row[5]) if row[5] else []
                    headers = [KeyValue(**h) for h in headers_data]
                    
                    env = Environment(
                        id=row[0], name=row[1], description=row[2] or "",
                        base_url=row[3] or "", variables=variables, headers=headers,
                        timeout=row[6], is_default=bool(row[7]),
                        created_at=datetime.fromisoformat(row[8]),
                        updated_at=datetime.fromisoformat(row[9])
                    )
                    environments.append(env)
                
                return environments
        except Exception as e:
            print(f"获取环境列表失败: {e}")
            return []
    
    def get_default_environment(self) -> Optional[Environment]:
        """获取默认环境"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM environments WHERE is_default = 1 LIMIT 1')
                row = cursor.fetchone()
                
                if row:
                    variables = json.loads(row[4]) if row[4] else {}
                    headers_data = json.loads(row[5]) if row[5] else []
                    headers = [KeyValue(**h) for h in headers_data]
                    
                    return Environment(
                        id=row[0], name=row[1], description=row[2] or "",
                        base_url=row[3] or "", variables=variables, headers=headers,
                        timeout=row[6], is_default=bool(row[7]),
                        created_at=datetime.fromisoformat(row[8]),
                        updated_at=datetime.fromisoformat(row[9])
                    )
                return None
        except Exception as e:
            print(f"获取默认环境失败: {e}")
            return None

    # 测试用例管理
    def create_test_case(self, test_case: TestCase) -> bool:
        """创建测试用例"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO test_cases
                    (id, name, description, project_id, module_id, tags, priority, status,
                     request_config, assertions, variable_extractions, database_assertions,
                     pre_operations, post_operations, skip, skip_reason, retry_count, retry_interval,
                     created_at, updated_at, created_by, updated_by, version)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    test_case.id, test_case.name, test_case.description,
                    test_case.project_id, test_case.module_id,
                    json.dumps(test_case.tags), test_case.priority.value, test_case.status.value,
                    json.dumps(self._serialize_request_config(test_case.request)),
                    json.dumps([self._serialize_assertion(a) for a in test_case.assertions]),
                    json.dumps([self._serialize_variable_extraction(v) for v in test_case.variable_extractions]),
                    json.dumps(test_case.database_assertions),
                    json.dumps([self._serialize_pre_post_operation(p) for p in test_case.pre_operations]),
                    json.dumps([self._serialize_pre_post_operation(p) for p in test_case.post_operations]),
                    1 if test_case.skip else 0, test_case.skip_reason,
                    test_case.retry_count, test_case.retry_interval,
                    test_case.created_at.isoformat(), test_case.updated_at.isoformat(),
                    test_case.created_by, test_case.updated_by, test_case.version
                ))
                conn.commit()
                return True
        except Exception as e:
            print(f"创建测试用例失败: {e}")
            return False

    def get_test_cases(self, project_id: str = None, module_id: str = None) -> List[TestCase]:
        """获取测试用例列表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                query = 'SELECT * FROM test_cases'
                params = []

                if project_id:
                    query += ' WHERE project_id = ?'
                    params.append(project_id)

                    if module_id:
                        query += ' AND module_id = ?'
                        params.append(module_id)
                elif module_id:
                    query += ' WHERE module_id = ?'
                    params.append(module_id)

                query += ' ORDER BY created_at DESC'

                cursor.execute(query, params)
                rows = cursor.fetchall()

                test_cases = []
                for row in rows:
                    # 解析JSON字段
                    tags = json.loads(row[5]) if row[5] else []
                    request_config = json.loads(row[8]) if row[8] else {}
                    assertions_data = json.loads(row[9]) if row[9] else []
                    extractions_data = json.loads(row[10]) if row[10] else []
                    # 处理数据库断言字段（可能不存在于旧记录中）
                    try:
                        database_assertions = json.loads(row[11]) if row[11] else []
                        pre_ops_data = json.loads(row[12]) if row[12] else []
                        post_ops_data = json.loads(row[13]) if row[13] else []
                        skip_index = 14
                    except (IndexError, json.JSONDecodeError):
                        # 兼容旧数据结构
                        database_assertions = []
                        pre_ops_data = json.loads(row[11]) if row[11] else []
                        post_ops_data = json.loads(row[12]) if row[12] else []
                        skip_index = 13

                    # 重建对象
                    request = self._deserialize_request_config(request_config) if request_config else RequestConfig()
                    assertions = [self._deserialize_assertion(a) for a in assertions_data]
                    extractions = [self._deserialize_variable_extraction(v) for v in extractions_data]
                    pre_ops = [self._deserialize_pre_post_operation(p) for p in pre_ops_data]
                    post_ops = [self._deserialize_pre_post_operation(p) for p in post_ops_data]

                    test_case = TestCase(
                        id=row[0], name=row[1], description=row[2] or "",
                        project_id=row[3] or "", module_id=row[4] or "",
                        tags=tags, priority=CasePriority(row[6]), status=CaseStatus(row[7]),
                        request=request, assertions=assertions, variable_extractions=extractions,
                        database_assertions=database_assertions,
                        pre_operations=pre_ops, post_operations=post_ops,
                        skip=bool(row[skip_index]), skip_reason=row[skip_index + 1] or "",
                        retry_count=row[15], retry_interval=row[16],
                        created_at=datetime.fromisoformat(row[17]),
                        updated_at=datetime.fromisoformat(row[18]),
                        created_by=row[19] or "", updated_by=row[20] or "",
                        version=row[21]
                    )
                    test_cases.append(test_case)

                return test_cases
        except Exception as e:
            print(f"获取测试用例列表失败: {e}")
            return []

    def get_test_case_by_id(self, case_id: str) -> Optional[TestCase]:
        """根据ID获取测试用例"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM test_cases WHERE id = ?', (case_id,))
                row = cursor.fetchone()

                if row:
                    # 解析JSON字段
                    tags = json.loads(row[5]) if row[5] else []
                    request_config = json.loads(row[8]) if row[8] else {}
                    assertions_data = json.loads(row[9]) if row[9] else []
                    extractions_data = json.loads(row[10]) if row[10] else []
                    pre_ops_data = json.loads(row[11]) if row[11] else []
                    post_ops_data = json.loads(row[12]) if row[12] else []

                    # 重建对象
                    request = self._deserialize_request_config(request_config) if request_config else RequestConfig()
                    assertions = [self._deserialize_assertion(a) for a in assertions_data]
                    extractions = [self._deserialize_variable_extraction(v) for v in extractions_data]
                    pre_ops = [self._deserialize_pre_post_operation(p) for p in pre_ops_data]
                    post_ops = [self._deserialize_pre_post_operation(p) for p in post_ops_data]

                    return TestCase(
                        id=row[0], name=row[1], description=row[2] or "",
                        project_id=row[3] or "", module_id=row[4] or "",
                        tags=tags, priority=CasePriority(row[6]), status=CaseStatus(row[7]),
                        request=request, assertions=assertions, variable_extractions=extractions,
                        pre_operations=pre_ops, post_operations=post_ops,
                        skip=bool(row[13]), skip_reason=row[14] or "",
                        retry_count=row[15], retry_interval=row[16],
                        created_at=datetime.fromisoformat(row[17]),
                        updated_at=datetime.fromisoformat(row[18]),
                        created_by=row[19] or "", updated_by=row[20] or "",
                        version=row[21]
                    )
                return None
        except Exception as e:
            print(f"获取测试用例失败: {e}")
            return None

    def update_test_case(self, test_case: TestCase) -> bool:
        """更新测试用例"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                test_case.updated_at = datetime.now()
                test_case.version += 1

                cursor.execute('''
                    UPDATE test_cases
                    SET name = ?, description = ?, project_id = ?, module_id = ?, tags = ?,
                        priority = ?, status = ?, request_config = ?, assertions = ?,
                        variable_extractions = ?, pre_operations = ?, post_operations = ?,
                        skip = ?, skip_reason = ?, retry_count = ?, retry_interval = ?,
                        updated_at = ?, updated_by = ?, version = ?
                    WHERE id = ?
                ''', (
                    test_case.name, test_case.description, test_case.project_id, test_case.module_id,
                    json.dumps(test_case.tags), test_case.priority.value, test_case.status.value,
                    json.dumps(self._serialize_request_config(test_case.request)),
                    json.dumps([self._serialize_assertion(a) for a in test_case.assertions]),
                    json.dumps([self._serialize_variable_extraction(v) for v in test_case.variable_extractions]),
                    json.dumps([self._serialize_pre_post_operation(p) for p in test_case.pre_operations]),
                    json.dumps([self._serialize_pre_post_operation(p) for p in test_case.post_operations]),
                    1 if test_case.skip else 0, test_case.skip_reason,
                    test_case.retry_count, test_case.retry_interval,
                    test_case.updated_at.isoformat(), test_case.updated_by, test_case.version,
                    test_case.id
                ))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"更新测试用例失败: {e}")
            return False

    def delete_test_case(self, case_id: str) -> bool:
        """删除测试用例"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM test_cases WHERE id = ?', (case_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"删除测试用例失败: {e}")
            return False

    def update_environment(self, environment: Environment) -> bool:
        """更新环境"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 如果设置为默认环境，先取消其他环境的默认状态
                if environment.is_default:
                    cursor.execute('UPDATE environments SET is_default = 0')

                cursor.execute('''
                    UPDATE environments
                    SET name = ?, description = ?, base_url = ?, variables = ?, headers = ?,
                        timeout = ?, is_default = ?, updated_at = ?
                    WHERE id = ?
                ''', (
                    environment.name, environment.description, environment.base_url,
                    json.dumps(environment.variables), json.dumps([h.__dict__ for h in environment.headers]),
                    environment.timeout, 1 if environment.is_default else 0,
                    environment.updated_at.isoformat(), environment.id
                ))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"更新环境失败: {e}")
            return False

    def delete_environment(self, env_id: str) -> bool:
        """删除环境"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM environments WHERE id = ?', (env_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"删除环境失败: {e}")
            return False

    def set_default_environment(self, env_id: str) -> bool:
        """设置默认环境"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                # 先取消所有环境的默认状态
                cursor.execute('UPDATE environments SET is_default = 0')
                # 设置指定环境为默认
                cursor.execute('UPDATE environments SET is_default = 1 WHERE id = ?', (env_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"设置默认环境失败: {e}")
            return False

    # 数据库配置管理
    def create_database_config(self, config) -> bool:
        """创建数据库配置"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO database_configs
                    (id, name, db_type, host, port, database_name, username, password,
                     charset, connection_params, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    config.id, config.name, config.db_type.value, config.host,
                    config.port, config.database, config.username, config.password,
                    config.charset, json.dumps(config.connection_params),
                    datetime.now().isoformat(), datetime.now().isoformat()
                ))
                conn.commit()
                return True
        except Exception as e:
            print(f"创建数据库配置失败: {e}")
            return False

    def get_database_configs(self):
        """获取所有数据库配置"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM database_configs ORDER BY created_at DESC')
                rows = cursor.fetchall()

                configs = []
                for row in rows:
                    from .database import DatabaseConfig, DatabaseType
                    config = DatabaseConfig(
                        id=row[0], name=row[1], db_type=DatabaseType(row[2]),
                        host=row[3] or "", port=row[4] or 3306,
                        database=row[5] or "", username=row[6] or "",
                        password=row[7] or "", charset=row[8] or "utf8mb4",
                        connection_params=json.loads(row[9]) if row[9] else {}
                    )
                    configs.append(config)

                return configs
        except Exception as e:
            print(f"获取数据库配置列表失败: {e}")
            return []

    def get_database_config_by_id(self, config_id: str):
        """根据ID获取数据库配置"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM database_configs WHERE id = ?', (config_id,))
                row = cursor.fetchone()

                if row:
                    from .database import DatabaseConfig, DatabaseType
                    return DatabaseConfig(
                        id=row[0], name=row[1], db_type=DatabaseType(row[2]),
                        host=row[3] or "", port=row[4] or 3306,
                        database=row[5] or "", username=row[6] or "",
                        password=row[7] or "", charset=row[8] or "utf8mb4",
                        connection_params=json.loads(row[9]) if row[9] else {}
                    )
                return None
        except Exception as e:
            print(f"获取数据库配置失败: {e}")
            return None

    def update_database_config(self, config) -> bool:
        """更新数据库配置"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE database_configs
                    SET name = ?, db_type = ?, host = ?, port = ?, database_name = ?,
                        username = ?, password = ?, charset = ?, connection_params = ?, updated_at = ?
                    WHERE id = ?
                ''', (
                    config.name, config.db_type.value, config.host, config.port,
                    config.database, config.username, config.password, config.charset,
                    json.dumps(config.connection_params), datetime.now().isoformat(),
                    config.id
                ))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"更新数据库配置失败: {e}")
            return False

    def delete_database_config(self, config_id: str) -> bool:
        """删除数据库配置"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM database_configs WHERE id = ?', (config_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"删除数据库配置失败: {e}")
            return False

    # 执行报告管理
    def create_execution_report(self, report: ExecutionReport) -> bool:
        """创建执行报告"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO execution_reports
                    (id, name, description, environment_id, environment_name,
                     total_cases, passed_cases, failed_cases, skipped_cases, error_cases,
                     start_time, end_time, total_duration, case_results, created_by, tags)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    report.id, report.name, report.description,
                    report.environment_id, report.environment_name,
                    report.total_cases, report.passed_cases, report.failed_cases,
                    report.skipped_cases, report.error_cases,
                    report.start_time.isoformat(),
                    report.end_time.isoformat() if report.end_time else None,
                    report.total_duration,
                    json.dumps([self._serialize_execution_result(r) for r in report.case_results]),
                    report.created_by, json.dumps(report.tags)
                ))
                conn.commit()
                return True
        except Exception as e:
            print(f"创建执行报告失败: {e}")
            return False

    def get_execution_reports(self, limit: int = 50) -> List[ExecutionReport]:
        """获取执行报告列表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM execution_reports
                    ORDER BY start_time DESC
                    LIMIT ?
                ''', (limit,))
                rows = cursor.fetchall()

                reports = []
                for row in rows:
                    case_results_data = json.loads(row[13]) if row[13] else []
                    case_results = [self._deserialize_execution_result(r) for r in case_results_data]
                    tags = json.loads(row[15]) if row[15] else []

                    report = ExecutionReport(
                        id=row[0], name=row[1], description=row[2] or "",
                        environment_id=row[3] or "", environment_name=row[4] or "",
                        total_cases=row[5], passed_cases=row[6], failed_cases=row[7],
                        skipped_cases=row[8], error_cases=row[9],
                        start_time=datetime.fromisoformat(row[10]),
                        end_time=datetime.fromisoformat(row[11]) if row[11] else None,
                        total_duration=row[12], case_results=case_results,
                        created_by=row[14] or "", tags=tags
                    )
                    reports.append(report)

                return reports
        except Exception as e:
            print(f"获取执行报告列表失败: {e}")
            return []

    def get_execution_report_by_id(self, report_id: str) -> Optional[ExecutionReport]:
        """根据ID获取执行报告"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM execution_reports WHERE id = ?', (report_id,))
                row = cursor.fetchone()

                if row:
                    case_results_data = json.loads(row[13]) if row[13] else []
                    case_results = [self._deserialize_execution_result(r) for r in case_results_data]
                    tags = json.loads(row[15]) if row[15] else []

                    return ExecutionReport(
                        id=row[0], name=row[1], description=row[2] or "",
                        environment_id=row[3] or "", environment_name=row[4] or "",
                        total_cases=row[5], passed_cases=row[6], failed_cases=row[7],
                        skipped_cases=row[8], error_cases=row[9],
                        start_time=datetime.fromisoformat(row[10]),
                        end_time=datetime.fromisoformat(row[11]) if row[11] else None,
                        total_duration=row[12], case_results=case_results,
                        created_by=row[14] or "", tags=tags
                    )
                return None
        except Exception as e:
            print(f"获取执行报告失败: {e}")
            return None

    def delete_execution_report(self, report_id: str) -> bool:
        """删除执行报告"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM execution_reports WHERE id = ?', (report_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"删除执行报告失败: {e}")
            return False

    def _serialize_execution_result(self, result: ExecutionResult) -> Dict[str, Any]:
        """序列化执行结果"""
        return {
            "case_id": result.case_id,
            "case_name": result.case_name,
            "status": result.status.value,
            "start_time": result.start_time.isoformat() if result.start_time else None,
            "end_time": result.end_time.isoformat() if result.end_time else None,
            "request_url": result.request_url,
            "request_method": result.request_method,
            "request_headers": result.request_headers,
            "request_body": result.request_body,
            "response_status_code": result.response_status_code,
            "response_headers": result.response_headers,
            "response_body": result.response_body,
            "response_time": result.response_time,
            "response_size": result.response_size,
            "assertion_results": result.assertion_results,
            "extracted_variables": result.extracted_variables,
            "error_message": result.error_message,
            "error_traceback": result.error_traceback,
            "skip_reason": result.skip_reason,
            "retry_count": result.retry_count
        }

    def _deserialize_execution_result(self, data: Dict[str, Any]) -> ExecutionResult:
        """反序列化执行结果"""
        return ExecutionResult(
            case_id=data.get("case_id", ""),
            case_name=data.get("case_name", ""),
            status=ExecutionStatus(data.get("status", "Error")),
            start_time=datetime.fromisoformat(data["start_time"]) if data.get("start_time") else None,
            end_time=datetime.fromisoformat(data["end_time"]) if data.get("end_time") else None,
            request_url=data.get("request_url", ""),
            request_method=data.get("request_method", ""),
            request_headers=data.get("request_headers", {}),
            request_body=data.get("request_body", ""),
            response_status_code=data.get("response_status_code", 0),
            response_headers=data.get("response_headers", {}),
            response_body=data.get("response_body", ""),
            response_time=data.get("response_time", 0),
            response_size=data.get("response_size", 0),
            assertion_results=data.get("assertion_results", []),
            extracted_variables=data.get("extracted_variables", {}),
            error_message=data.get("error_message", ""),
            error_traceback=data.get("error_traceback", ""),
            skip_reason=data.get("skip_reason", ""),
            retry_count=data.get("retry_count", 0)
        )
