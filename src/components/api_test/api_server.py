#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
CI/CD集成API服务器
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import threading
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
import uuid

from .dao import ApiTestDAO
from .test_engine import TestEngine, execution_manager
from .models import *


class ApiTestServer:
    """接口测试API服务器"""
    
    def __init__(self, host: str = "0.0.0.0", port: int = 8888):
        self.app = Flask(__name__)
        CORS(self.app)  # 允许跨域请求
        self.host = host
        self.port = port
        self.dao = ApiTestDAO()
        self.running_executions: Dict[str, Dict] = {}
        
        self._setup_routes()
    
    def _setup_routes(self):
        """设置路由"""
        
        # 健康检查
        @self.app.route('/health', methods=['GET'])
        def health_check():
            return jsonify({
                "status": "ok",
                "timestamp": datetime.now().isoformat(),
                "version": "1.0.0"
            })
        
        # 获取项目列表
        @self.app.route('/api/projects', methods=['GET'])
        def get_projects():
            try:
                projects = self.dao.get_projects()
                return jsonify({
                    "success": True,
                    "data": [self._project_to_dict(p) for p in projects]
                })
            except Exception as e:
                return jsonify({"success": False, "error": str(e)}), 500
        
        # 获取环境列表
        @self.app.route('/api/environments', methods=['GET'])
        def get_environments():
            try:
                environments = self.dao.get_environments()
                return jsonify({
                    "success": True,
                    "data": [self._environment_to_dict(e) for e in environments]
                })
            except Exception as e:
                return jsonify({"success": False, "error": str(e)}), 500
        
        # 获取测试用例列表
        @self.app.route('/api/test-cases', methods=['GET'])
        def get_test_cases():
            try:
                project_id = request.args.get('project_id')
                module_id = request.args.get('module_id')
                
                test_cases = self.dao.get_test_cases(project_id, module_id)
                return jsonify({
                    "success": True,
                    "data": [self._test_case_to_dict(tc) for tc in test_cases]
                })
            except Exception as e:
                return jsonify({"success": False, "error": str(e)}), 500
        
        # 执行测试用例
        @self.app.route('/api/execute', methods=['POST'])
        def execute_test_cases():
            try:
                data = request.get_json()
                
                # 解析请求参数
                case_ids = data.get('case_ids', [])
                environment_id = data.get('environment_id')
                parallel = data.get('parallel', False)
                max_workers = data.get('max_workers', 3)
                stop_on_failure = data.get('stop_on_failure', False)
                
                if not case_ids:
                    return jsonify({"success": False, "error": "用例ID列表不能为空"}), 400
                
                # 获取测试用例
                test_cases = []
                for case_id in case_ids:
                    case = self.dao.get_test_case_by_id(case_id)
                    if case:
                        test_cases.append(case)
                
                if not test_cases:
                    return jsonify({"success": False, "error": "没有找到有效的测试用例"}), 400
                
                # 获取环境
                environment = None
                if environment_id:
                    environments = self.dao.get_environments()
                    environment = next((e for e in environments if e.id == environment_id), None)
                
                # 创建执行ID
                execution_id = str(uuid.uuid4())
                
                # 启动异步执行
                thread = threading.Thread(
                    target=self._execute_test_cases_async,
                    args=(execution_id, test_cases, environment, parallel, max_workers, stop_on_failure)
                )
                thread.daemon = True
                thread.start()
                
                return jsonify({
                    "success": True,
                    "execution_id": execution_id,
                    "message": "测试执行已启动"
                })
                
            except Exception as e:
                return jsonify({"success": False, "error": str(e)}), 500
        
        # 获取执行状态
        @self.app.route('/api/execution/<execution_id>/status', methods=['GET'])
        def get_execution_status(execution_id):
            try:
                if execution_id not in self.running_executions:
                    return jsonify({"success": False, "error": "执行ID不存在"}), 404
                
                execution_info = self.running_executions[execution_id]
                return jsonify({
                    "success": True,
                    "data": execution_info
                })
                
            except Exception as e:
                return jsonify({"success": False, "error": str(e)}), 500
        
        # 停止执行
        @self.app.route('/api/execution/<execution_id>/stop', methods=['POST'])
        def stop_execution(execution_id):
            try:
                if execution_id not in self.running_executions:
                    return jsonify({"success": False, "error": "执行ID不存在"}), 404
                
                # 停止执行引擎
                engine = execution_manager.get_engine(execution_id)
                if engine:
                    engine.stop_execution()
                
                # 更新状态
                self.running_executions[execution_id]["status"] = "stopped"
                self.running_executions[execution_id]["end_time"] = datetime.now().isoformat()
                
                return jsonify({
                    "success": True,
                    "message": "执行已停止"
                })
                
            except Exception as e:
                return jsonify({"success": False, "error": str(e)}), 500
        
        # 获取执行报告
        @self.app.route('/api/execution/<execution_id>/report', methods=['GET'])
        def get_execution_report(execution_id):
            try:
                if execution_id not in self.running_executions:
                    return jsonify({"success": False, "error": "执行ID不存在"}), 404
                
                execution_info = self.running_executions[execution_id]
                
                if execution_info["status"] != "completed":
                    return jsonify({"success": False, "error": "执行尚未完成"}), 400
                
                report = execution_info.get("report")
                if not report:
                    return jsonify({"success": False, "error": "报告不存在"}), 404
                
                return jsonify({
                    "success": True,
                    "data": self._execution_report_to_dict(report)
                })
                
            except Exception as e:
                return jsonify({"success": False, "error": str(e)}), 500
        
        # 获取执行历史
        @self.app.route('/api/execution-history', methods=['GET'])
        def get_execution_history():
            try:
                limit = int(request.args.get('limit', 50))
                reports = self.dao.get_execution_reports(limit)
                
                return jsonify({
                    "success": True,
                    "data": [self._execution_report_to_dict(r) for r in reports]
                })
                
            except Exception as e:
                return jsonify({"success": False, "error": str(e)}), 500
        
        # 创建测试用例
        @self.app.route('/api/test-cases', methods=['POST'])
        def create_test_case():
            try:
                data = request.get_json()
                
                # 构建测试用例对象
                test_case = self._dict_to_test_case(data)
                
                # 保存到数据库
                if self.dao.create_test_case(test_case):
                    return jsonify({
                        "success": True,
                        "data": self._test_case_to_dict(test_case)
                    })
                else:
                    return jsonify({"success": False, "error": "创建测试用例失败"}), 500
                
            except Exception as e:
                return jsonify({"success": False, "error": str(e)}), 500
        
        # 更新测试用例
        @self.app.route('/api/test-cases/<case_id>', methods=['PUT'])
        def update_test_case(case_id):
            try:
                data = request.get_json()
                
                # 获取现有用例
                existing_case = self.dao.get_test_case_by_id(case_id)
                if not existing_case:
                    return jsonify({"success": False, "error": "测试用例不存在"}), 404
                
                # 更新用例数据
                updated_case = self._dict_to_test_case(data)
                updated_case.id = case_id
                
                # 保存到数据库
                if self.dao.update_test_case(updated_case):
                    return jsonify({
                        "success": True,
                        "data": self._test_case_to_dict(updated_case)
                    })
                else:
                    return jsonify({"success": False, "error": "更新测试用例失败"}), 500
                
            except Exception as e:
                return jsonify({"success": False, "error": str(e)}), 500
        
        # 删除测试用例
        @self.app.route('/api/test-cases/<case_id>', methods=['DELETE'])
        def delete_test_case(case_id):
            try:
                if self.dao.delete_test_case(case_id):
                    return jsonify({
                        "success": True,
                        "message": "测试用例删除成功"
                    })
                else:
                    return jsonify({"success": False, "error": "删除测试用例失败"}), 500
                
            except Exception as e:
                return jsonify({"success": False, "error": str(e)}), 500

    def _execute_test_cases_async(self, execution_id: str, test_cases: List[TestCase],
                                 environment: Environment, parallel: bool,
                                 max_workers: int, stop_on_failure: bool):
        """异步执行测试用例"""
        try:
            # 初始化执行信息
            self.running_executions[execution_id] = {
                "execution_id": execution_id,
                "status": "running",
                "start_time": datetime.now().isoformat(),
                "end_time": None,
                "total_cases": len(test_cases),
                "completed_cases": 0,
                "passed_cases": 0,
                "failed_cases": 0,
                "skipped_cases": 0,
                "error_cases": 0,
                "current_case": None,
                "progress": 0.0,
                "report": None
            }

            # 创建执行引擎
            engine = execution_manager.create_engine(execution_id)
            engine.set_environment(environment)

            # 设置回调函数
            def progress_callback(current, total):
                if execution_id in self.running_executions:
                    self.running_executions[execution_id]["completed_cases"] = current
                    self.running_executions[execution_id]["progress"] = current / total * 100

            def result_callback(result: ExecutionResult):
                if execution_id in self.running_executions:
                    exec_info = self.running_executions[execution_id]
                    exec_info["current_case"] = result.case_name

                    if result.status == ExecutionStatus.PASSED:
                        exec_info["passed_cases"] += 1
                    elif result.status == ExecutionStatus.FAILED:
                        exec_info["failed_cases"] += 1
                    elif result.status == ExecutionStatus.SKIPPED:
                        exec_info["skipped_cases"] += 1
                    elif result.status == ExecutionStatus.ERROR:
                        exec_info["error_cases"] += 1

            engine.set_progress_callback(progress_callback)
            engine.set_result_callback(result_callback)

            # 执行测试用例
            report = engine.execute_multiple_cases(
                test_cases, environment, parallel, max_workers, stop_on_failure
            )

            # 保存报告到数据库
            self.dao.create_execution_report(report)

            # 更新执行状态
            self.running_executions[execution_id].update({
                "status": "completed",
                "end_time": datetime.now().isoformat(),
                "report": report
            })

        except Exception as e:
            # 执行失败
            if execution_id in self.running_executions:
                self.running_executions[execution_id].update({
                    "status": "failed",
                    "end_time": datetime.now().isoformat(),
                    "error": str(e)
                })

        finally:
            # 清理执行引擎
            execution_manager.remove_engine(execution_id)

    def _project_to_dict(self, project: Project) -> Dict[str, Any]:
        """项目对象转字典"""
        return {
            "id": project.id,
            "name": project.name,
            "description": project.description,
            "created_at": project.created_at.isoformat(),
            "updated_at": project.updated_at.isoformat(),
            "created_by": project.created_by
        }

    def _environment_to_dict(self, environment: Environment) -> Dict[str, Any]:
        """环境对象转字典"""
        return {
            "id": environment.id,
            "name": environment.name,
            "description": environment.description,
            "base_url": environment.base_url,
            "variables": environment.variables,
            "headers": [{"key": h.key, "value": h.value, "enabled": h.enabled} for h in environment.headers],
            "timeout": environment.timeout,
            "is_default": environment.is_default
        }

    def _test_case_to_dict(self, test_case: TestCase) -> Dict[str, Any]:
        """测试用例对象转字典"""
        return {
            "id": test_case.id,
            "name": test_case.name,
            "description": test_case.description,
            "project_id": test_case.project_id,
            "module_id": test_case.module_id,
            "tags": test_case.tags,
            "priority": test_case.priority.value,
            "status": test_case.status.value,
            "request": {
                "method": test_case.request.method.value,
                "url": test_case.request.url,
                "protocol": test_case.request.protocol.value,
                "headers": [{"key": h.key, "value": h.value, "enabled": h.enabled} for h in test_case.request.headers],
                "query_params": [{"key": p.key, "value": p.value, "enabled": p.enabled} for p in test_case.request.query_params],
                "body_type": test_case.request.body_type.value,
                "body_data": test_case.request.body_data,
                "timeout": test_case.request.timeout
            },
            "assertions": [
                {
                    "id": a.id,
                    "assertion_type": a.assertion_type.value,
                    "field_path": a.field_path,
                    "operator": a.operator,
                    "expected_value": a.expected_value,
                    "enabled": a.enabled,
                    "description": a.description
                } for a in test_case.assertions
            ],
            "variable_extractions": [
                {
                    "id": v.id,
                    "variable_name": v.variable_name,
                    "source": v.source,
                    "extraction_method": v.extraction_method,
                    "expression": v.expression,
                    "enabled": v.enabled
                } for v in test_case.variable_extractions
            ],
            "skip": test_case.skip,
            "skip_reason": test_case.skip_reason,
            "created_at": test_case.created_at.isoformat(),
            "updated_at": test_case.updated_at.isoformat()
        }

    def _execution_report_to_dict(self, report: ExecutionReport) -> Dict[str, Any]:
        """执行报告对象转字典"""
        return {
            "id": report.id,
            "name": report.name,
            "description": report.description,
            "environment_id": report.environment_id,
            "environment_name": report.environment_name,
            "total_cases": report.total_cases,
            "passed_cases": report.passed_cases,
            "failed_cases": report.failed_cases,
            "skipped_cases": report.skipped_cases,
            "error_cases": report.error_cases,
            "start_time": report.start_time.isoformat(),
            "end_time": report.end_time.isoformat() if report.end_time else None,
            "total_duration": report.total_duration,
            "pass_rate": (report.passed_cases / report.total_cases * 100) if report.total_cases > 0 else 0,
            "case_results": [
                {
                    "case_id": r.case_id,
                    "case_name": r.case_name,
                    "status": r.status.value,
                    "response_time": r.response_time,
                    "error_message": r.error_message,
                    "skip_reason": r.skip_reason
                } for r in report.case_results
            ]
        }

    def _dict_to_test_case(self, data: Dict[str, Any]) -> TestCase:
        """字典转测试用例对象"""
        # 构建请求配置
        request_data = data.get("request", {})
        headers = [KeyValue(**h) for h in request_data.get("headers", [])]
        query_params = [KeyValue(**p) for p in request_data.get("query_params", [])]

        request_config = RequestConfig(
            method=RequestMethod(request_data.get("method", "GET")),
            url=request_data.get("url", ""),
            protocol=Protocol(request_data.get("protocol", "HTTPS")),
            headers=headers,
            query_params=query_params,
            body_type=BodyType(request_data.get("body_type", "None")),
            body_data=request_data.get("body_data", ""),
            timeout=request_data.get("timeout", 30)
        )

        # 构建断言
        assertions = []
        for a_data in data.get("assertions", []):
            assertion = Assertion(
                assertion_type=AssertionType(a_data.get("assertion_type", "Status Code")),
                field_path=a_data.get("field_path", ""),
                operator=a_data.get("operator", "equals"),
                expected_value=a_data.get("expected_value", ""),
                enabled=a_data.get("enabled", True),
                description=a_data.get("description", "")
            )
            assertions.append(assertion)

        # 构建变量提取
        variable_extractions = []
        for v_data in data.get("variable_extractions", []):
            extraction = VariableExtraction(
                variable_name=v_data.get("variable_name", ""),
                source=v_data.get("source", "response_body"),
                extraction_method=v_data.get("extraction_method", "json_path"),
                expression=v_data.get("expression", ""),
                enabled=v_data.get("enabled", True)
            )
            variable_extractions.append(extraction)

        # 构建测试用例
        test_case = TestCase(
            name=data.get("name", ""),
            description=data.get("description", ""),
            project_id=data.get("project_id", ""),
            module_id=data.get("module_id", ""),
            tags=data.get("tags", []),
            priority=CasePriority(data.get("priority", "Medium")),
            status=CaseStatus(data.get("status", "Enabled")),
            request=request_config,
            assertions=assertions,
            variable_extractions=variable_extractions,
            skip=data.get("skip", False),
            skip_reason=data.get("skip_reason", "")
        )

        return test_case

    def run(self, debug: bool = False):
        """启动API服务器"""
        print(f"🚀 API测试服务器启动中...")
        print(f"📍 地址: http://{self.host}:{self.port}")
        print(f"📖 健康检查: http://{self.host}:{self.port}/health")
        print(f"📋 API接口:")
        print(f"   GET  /api/projects - 获取项目列表")
        print(f"   GET  /api/environments - 获取环境列表")
        print(f"   GET  /api/test-cases - 获取测试用例列表")
        print(f"   POST /api/execute - 执行测试用例")
        print(f"   GET  /api/execution/<id>/status - 获取执行状态")
        print(f"   POST /api/execution/<id>/stop - 停止执行")
        print(f"   GET  /api/execution/<id>/report - 获取执行报告")

        self.app.run(host=self.host, port=self.port, debug=debug, threaded=True)


# 全局API服务器实例
api_server = ApiTestServer()
