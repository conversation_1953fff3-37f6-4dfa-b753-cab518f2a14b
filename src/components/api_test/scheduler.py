#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
定时任务调度器
"""

import threading
import time
import schedule
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
import json


class ScheduleType(Enum):
    """调度类型"""
    ONCE = "once"           # 一次性执行
    DAILY = "daily"         # 每日执行
    WEEKLY = "weekly"       # 每周执行
    INTERVAL = "interval"   # 间隔执行
    CRON = "cron"          # Cron表达式


class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"     # 等待执行
    RUNNING = "running"     # 执行中
    COMPLETED = "completed" # 已完成
    FAILED = "failed"       # 执行失败
    CANCELLED = "cancelled" # 已取消


@dataclass
class ScheduledTask:
    """定时任务"""
    id: str
    name: str
    description: str = ""
    schedule_type: ScheduleType = ScheduleType.DAILY
    schedule_config: Dict[str, Any] = field(default_factory=dict)
    
    # 执行配置
    project_ids: List[str] = field(default_factory=list)
    environment_id: str = ""
    case_ids: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    
    # 执行参数
    parallel: bool = False
    max_workers: int = 3
    stop_on_failure: bool = False
    
    # 通知配置
    notification_configs: List[str] = field(default_factory=list)
    notify_on_success: bool = True
    notify_on_failure: bool = True
    
    # 状态信息
    enabled: bool = True
    status: TaskStatus = TaskStatus.PENDING
    last_execution: Optional[datetime] = None
    next_execution: Optional[datetime] = None
    execution_count: int = 0
    success_count: int = 0
    failure_count: int = 0
    
    # 元数据
    created_at: Optional[datetime] = None
    created_by: str = "系统"
    updated_at: Optional[datetime] = None

    def __post_init__(self):
        """初始化后处理"""
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()


class TaskScheduler:
    """任务调度器"""
    
    def __init__(self):
        self.tasks: Dict[str, ScheduledTask] = {}
        self.running = False
        self.scheduler_thread: Optional[threading.Thread] = None
        self._lock = threading.Lock()
    
    def add_task(self, task: ScheduledTask) -> bool:
        """添加定时任务"""
        try:
            with self._lock:
                self.tasks[task.id] = task
                self._schedule_task(task)
                return True
        except Exception as e:
            print(f"添加定时任务失败: {e}")
            return False
    
    def remove_task(self, task_id: str) -> bool:
        """移除定时任务"""
        try:
            with self._lock:
                if task_id in self.tasks:
                    task = self.tasks[task_id]
                    self._unschedule_task(task)
                    del self.tasks[task_id]
                    return True
                return False
        except Exception as e:
            print(f"移除定时任务失败: {e}")
            return False
    
    def update_task(self, task: ScheduledTask) -> bool:
        """更新定时任务"""
        try:
            with self._lock:
                if task.id in self.tasks:
                    old_task = self.tasks[task.id]
                    self._unschedule_task(old_task)
                    
                    task.updated_at = datetime.now()
                    self.tasks[task.id] = task
                    
                    if task.enabled:
                        self._schedule_task(task)
                    
                    return True
                return False
        except Exception as e:
            print(f"更新定时任务失败: {e}")
            return False
    
    def enable_task(self, task_id: str) -> bool:
        """启用任务"""
        try:
            with self._lock:
                if task_id in self.tasks:
                    task = self.tasks[task_id]
                    task.enabled = True
                    task.updated_at = datetime.now()
                    self._schedule_task(task)
                    return True
                return False
        except Exception as e:
            print(f"启用任务失败: {e}")
            return False
    
    def disable_task(self, task_id: str) -> bool:
        """禁用任务"""
        try:
            with self._lock:
                if task_id in self.tasks:
                    task = self.tasks[task_id]
                    task.enabled = False
                    task.updated_at = datetime.now()
                    self._unschedule_task(task)
                    return True
                return False
        except Exception as e:
            print(f"禁用任务失败: {e}")
            return False
    
    def get_task(self, task_id: str) -> Optional[ScheduledTask]:
        """获取任务"""
        return self.tasks.get(task_id)
    
    def list_tasks(self) -> List[ScheduledTask]:
        """获取所有任务"""
        return list(self.tasks.values())
    
    def start(self):
        """启动调度器"""
        if not self.running:
            self.running = True
            self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.scheduler_thread.start()
            print("定时任务调度器已启动")
    
    def stop(self):
        """停止调度器"""
        self.running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        schedule.clear()
        print("定时任务调度器已停止")
    
    def execute_task_now(self, task_id: str) -> bool:
        """立即执行任务"""
        try:
            task = self.get_task(task_id)
            if task:
                self._execute_task(task)
                return True
            return False
        except Exception as e:
            print(f"立即执行任务失败: {e}")
            return False
    
    def _schedule_task(self, task: ScheduledTask):
        """调度任务"""
        if not task.enabled:
            return
        
        try:
            if task.schedule_type == ScheduleType.DAILY:
                time_str = task.schedule_config.get("time", "09:00")
                schedule.every().day.at(time_str).do(self._execute_task, task)
                
                # 计算下次执行时间
                next_run = schedule.jobs[-1].next_run if schedule.jobs else None
                task.next_execution = next_run
            
            elif task.schedule_type == ScheduleType.WEEKLY:
                day = task.schedule_config.get("day", "monday")
                time_str = task.schedule_config.get("time", "09:00")
                getattr(schedule.every(), day).at(time_str).do(self._execute_task, task)
                
                next_run = schedule.jobs[-1].next_run if schedule.jobs else None
                task.next_execution = next_run
            
            elif task.schedule_type == ScheduleType.INTERVAL:
                interval = task.schedule_config.get("interval", 60)  # 默认60分钟
                unit = task.schedule_config.get("unit", "minutes")
                
                if unit == "minutes":
                    schedule.every(interval).minutes.do(self._execute_task, task)
                elif unit == "hours":
                    schedule.every(interval).hours.do(self._execute_task, task)
                elif unit == "days":
                    schedule.every(interval).days.do(self._execute_task, task)
                
                next_run = schedule.jobs[-1].next_run if schedule.jobs else None
                task.next_execution = next_run
            
            elif task.schedule_type == ScheduleType.ONCE:
                execute_time = task.schedule_config.get("execute_time")
                if execute_time:
                    # 如果是未来时间，则调度执行
                    execute_datetime = datetime.fromisoformat(execute_time)
                    if execute_datetime > datetime.now():
                        task.next_execution = execute_datetime
                        # 一次性任务需要特殊处理
                        threading.Timer(
                            (execute_datetime - datetime.now()).total_seconds(),
                            self._execute_task,
                            args=[task]
                        ).start()
        
        except Exception as e:
            print(f"调度任务失败: {e}")
    
    def _unschedule_task(self, task: ScheduledTask):
        """取消调度任务"""
        # 移除相关的schedule任务
        schedule.clear(tag=task.id)
        task.next_execution = None
    
    def _execute_task(self, task: ScheduledTask):
        """执行任务"""
        try:
            print(f"开始执行定时任务: {task.name}")
            
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            task.last_execution = datetime.now()
            task.execution_count += 1
            
            # 执行测试用例
            success = self._run_test_cases(task)
            
            # 更新执行结果
            if success:
                task.status = TaskStatus.COMPLETED
                task.success_count += 1
                print(f"定时任务执行成功: {task.name}")
            else:
                task.status = TaskStatus.FAILED
                task.failure_count += 1
                print(f"定时任务执行失败: {task.name}")
            
            # 发送通知
            self._send_task_notification(task, success)
            
            # 如果是一次性任务，执行完后禁用
            if task.schedule_type == ScheduleType.ONCE:
                task.enabled = False
        
        except Exception as e:
            print(f"执行定时任务异常: {e}")
            task.status = TaskStatus.FAILED
            task.failure_count += 1
            self._send_task_notification(task, False)
    
    def _run_test_cases(self, task: ScheduledTask) -> bool:
        """运行测试用例"""
        try:
            from src.components.api_test.test_engine import execution_manager
            from src.components.api_test.dao import ApiTestDAO
            
            dao = ApiTestDAO()
            
            # 获取要执行的测试用例
            test_cases = []
            
            if task.case_ids:
                # 按用例ID执行
                for case_id in task.case_ids:
                    case = dao.get_test_case_by_id(case_id)
                    if case:
                        test_cases.append(case)
            else:
                # 按项目和标签筛选
                all_cases = dao.get_test_cases()
                for case in all_cases:
                    # 项目筛选
                    if task.project_ids and case.project_id not in task.project_ids:
                        continue
                    
                    # 标签筛选
                    if task.tags:
                        if not any(tag in case.tags for tag in task.tags):
                            continue
                    
                    test_cases.append(case)
            
            if not test_cases:
                print(f"定时任务 {task.name} 没有找到要执行的测试用例")
                return False
            
            # 获取执行环境
            environment = dao.get_environment_by_id(task.environment_id)
            if not environment:
                print(f"定时任务 {task.name} 执行环境不存在")
                return False
            
            # 创建执行引擎
            engine = execution_manager.create_engine(f"scheduler_{task.id}")
            engine.set_environment(environment)
            
            # 执行测试用例
            report = engine.execute_multiple_cases(
                test_cases, environment, task.parallel, task.max_workers, task.stop_on_failure
            )
            
            # 保存报告
            dao.create_execution_report(report)
            
            # 判断执行结果
            return report.failed_cases == 0 and report.error_cases == 0
        
        except Exception as e:
            print(f"运行测试用例失败: {e}")
            return False
    
    def _send_task_notification(self, task: ScheduledTask, success: bool):
        """发送任务通知"""
        try:
            # 检查是否需要发送通知
            if success and not task.notify_on_success:
                return
            if not success and not task.notify_on_failure:
                return
            
            if not task.notification_configs:
                return
            
            from src.components.api_test.notification import notification_manager
            
            # 构建通知数据
            report_data = {
                "project_name": f"定时任务: {task.name}",
                "environment_name": "定时执行",
                "total_cases": task.execution_count,
                "passed_cases": task.success_count,
                "failed_cases": task.failure_count,
                "execution_time": task.last_execution.strftime('%Y-%m-%d %H:%M:%S') if task.last_execution else "",
                "task_status": "成功" if success else "失败"
            }
            
            # 发送通知
            notification_manager.send_test_result_notification(report_data, task.notification_configs)
        
        except Exception as e:
            print(f"发送任务通知失败: {e}")
    
    def _run_scheduler(self):
        """运行调度器"""
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                print(f"调度器运行异常: {e}")
                time.sleep(5)


# 全局调度器实例
task_scheduler = TaskScheduler()
