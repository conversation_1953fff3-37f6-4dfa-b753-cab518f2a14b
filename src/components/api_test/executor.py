#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
接口自动化测试执行引擎
"""

import requests
import time
import re
import json
import traceback
import os
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import jsonpath_ng
from urllib.parse import urljoin, urlparse
import base64
import hashlib
import hmac

from .models import *


class VariableManager:
    """变量管理器"""
    
    def __init__(self):
        self.global_vars: Dict[str, str] = {}
        self.case_set_vars: Dict[str, str] = {}
        self.temp_vars: Dict[str, str] = {}
        self.env_vars: Dict[str, str] = {}
    
    def set_variable(self, name: str, value: str, scope: str = "temp"):
        """设置变量"""
        if scope == "global":
            self.global_vars[name] = value
        elif scope == "case_set":
            self.case_set_vars[name] = value
        elif scope == "env":
            self.env_vars[name] = value
        else:
            self.temp_vars[name] = value
    
    def get_variable(self, name: str) -> Optional[str]:
        """获取变量（按优先级：临时 > 用例集 > 全局 > 环境）"""
        if name in self.temp_vars:
            return self.temp_vars[name]
        elif name in self.case_set_vars:
            return self.case_set_vars[name]
        elif name in self.global_vars:
            return self.global_vars[name]
        elif name in self.env_vars:
            return self.env_vars[name]
        return None
    
    def replace_variables(self, text: str) -> str:
        """替换文本中的变量"""
        if not text:
            return text
        
        # 查找所有 {{variable}} 模式
        pattern = r'\{\{([^}]+)\}\}'
        
        def replace_func(match):
            var_name = match.group(1).strip()
            var_value = self.get_variable(var_name)
            return str(var_value) if var_value is not None else match.group(0)
        
        return re.sub(pattern, replace_func, text)
    
    def get_all_variables(self) -> Dict[str, str]:
        """获取所有变量"""
        all_vars = {}
        all_vars.update(self.env_vars)
        all_vars.update(self.global_vars)
        all_vars.update(self.case_set_vars)
        all_vars.update(self.temp_vars)
        return all_vars
    
    def clear_temp_variables(self):
        """清空临时变量"""
        self.temp_vars.clear()


class AssertionEngine:
    """断言引擎"""
    
    @staticmethod
    def run_assertion(assertion: Assertion, response: requests.Response, 
                     response_time: float) -> Dict[str, Any]:
        """执行单个断言"""
        result = {
            "assertion_id": assertion.id,
            "description": assertion.description,
            "passed": False,
            "actual_value": None,
            "expected_value": assertion.expected_value,
            "error_message": ""
        }
        
        try:
            if not assertion.enabled:
                result["passed"] = True
                result["error_message"] = "断言已禁用"
                return result
            
            # 获取实际值
            actual_value = AssertionEngine._get_actual_value(
                assertion, response, response_time
            )
            result["actual_value"] = actual_value
            
            # 执行比较
            result["passed"] = AssertionEngine._compare_values(
                actual_value, assertion.expected_value, assertion.operator
            )
            
            if not result["passed"]:
                result["error_message"] = f"断言失败: 期望 {assertion.expected_value}, 实际 {actual_value}"
        
        except Exception as e:
            result["passed"] = False
            result["error_message"] = f"断言执行错误: {str(e)}"
        
        return result
    
    @staticmethod
    def _get_actual_value(assertion: Assertion, response: requests.Response, 
                         response_time: float) -> Any:
        """获取实际值"""
        if assertion.assertion_type == AssertionType.STATUS_CODE:
            return response.status_code
        
        elif assertion.assertion_type == AssertionType.RESPONSE_TIME:
            return response_time
        
        elif assertion.assertion_type == AssertionType.RESPONSE_HEADER:
            return response.headers.get(assertion.field_path, "")
        
        elif assertion.assertion_type == AssertionType.RESPONSE_BODY:
            return response.text
        
        elif assertion.assertion_type == AssertionType.JSON_PATH:
            try:
                response_json = response.json()
                jsonpath_expr = jsonpath_ng.parse(assertion.field_path)
                matches = jsonpath_expr.find(response_json)
                return matches[0].value if matches else None
            except:
                return None
        
        elif assertion.assertion_type == AssertionType.REGEX:
            pattern = re.compile(assertion.field_path)
            match = pattern.search(response.text)
            return match.group(0) if match else None

        elif assertion.assertion_type == AssertionType.XPATH:
            try:
                from lxml import etree, html
                # 尝试解析为XML
                try:
                    root = etree.fromstring(response.content)
                except:
                    # 如果XML解析失败，尝试HTML解析
                    root = html.fromstring(response.content)

                result = root.xpath(assertion.field_path)
                if result:
                    if isinstance(result[0], str):
                        return result[0]
                    elif hasattr(result[0], 'text'):
                        return result[0].text
                    else:
                        return str(result[0])
                return None
            except Exception as e:
                raise Exception(f"XPath解析错误: {e}")

        elif assertion.assertion_type == AssertionType.DATABASE:
            # 数据库断言在测试引擎中处理
            return None

        return None
    
    @staticmethod
    def _compare_values(actual: Any, expected: str, operator: str) -> bool:
        """比较值"""
        try:
            if operator == "equals":
                return str(actual) == expected
            elif operator == "not_equals":
                return str(actual) != expected
            elif operator == "contains":
                return expected in str(actual)
            elif operator == "not_contains":
                return expected not in str(actual)
            elif operator == "starts_with":
                return str(actual).startswith(expected)
            elif operator == "ends_with":
                return str(actual).endswith(expected)
            elif operator == "greater_than":
                return float(actual) > float(expected)
            elif operator == "less_than":
                return float(actual) < float(expected)
            elif operator == "greater_equal":
                return float(actual) >= float(expected)
            elif operator == "less_equal":
                return float(actual) <= float(expected)
            elif operator == "regex_match":
                pattern = re.compile(expected)
                return bool(pattern.search(str(actual)))
            elif operator == "exists":
                return actual is not None
            elif operator == "not_exists":
                return actual is None
            elif operator == "empty":
                return not actual or str(actual).strip() == ""
            elif operator == "not_empty":
                return actual and str(actual).strip() != ""
            elif operator == "length_equals":
                if hasattr(actual, '__len__'):
                    return len(actual) == int(expected)
                return len(str(actual)) == int(expected)
            elif operator == "length_greater_than":
                if hasattr(actual, '__len__'):
                    return len(actual) > int(expected)
                return len(str(actual)) > int(expected)
            elif operator == "length_less_than":
                if hasattr(actual, '__len__'):
                    return len(actual) < int(expected)
                return len(str(actual)) < int(expected)
            elif operator == "type_equals":
                return type(actual).__name__ == expected
            elif operator == "in_list":
                expected_list = [item.strip() for item in expected.split(',')]
                return str(actual) in expected_list
            elif operator == "not_in_list":
                expected_list = [item.strip() for item in expected.split(',')]
                return str(actual) not in expected_list
            else:
                return False
        except Exception as e:
            print(f"比较值时发生错误: {e}")
            return False


class VariableExtractor:
    """变量提取器"""
    
    @staticmethod
    def extract_variables(extractions: List[VariableExtraction], 
                         response: requests.Response,
                         variable_manager: VariableManager) -> Dict[str, str]:
        """提取变量"""
        extracted = {}
        
        for extraction in extractions:
            if not extraction.enabled:
                continue
            
            try:
                value = VariableExtractor._extract_value(extraction, response)
                if value is not None:
                    extracted[extraction.variable_name] = str(value)
                    variable_manager.set_variable(extraction.variable_name, str(value))
                elif extraction.default_value:
                    extracted[extraction.variable_name] = extraction.default_value
                    variable_manager.set_variable(extraction.variable_name, extraction.default_value)
            except Exception as e:
                print(f"变量提取失败 {extraction.variable_name}: {e}")
                if extraction.default_value:
                    extracted[extraction.variable_name] = extraction.default_value
                    variable_manager.set_variable(extraction.variable_name, extraction.default_value)
        
        return extracted
    
    @staticmethod
    def _extract_value(extraction: VariableExtraction, response: requests.Response) -> Any:
        """提取单个值"""
        if extraction.source == "response_body":
            if extraction.extraction_method == "json_path":
                response_json = response.json()
                jsonpath_expr = jsonpath_ng.parse(extraction.expression)
                matches = jsonpath_expr.find(response_json)
                return matches[0].value if matches else None
            
            elif extraction.extraction_method == "regex":
                pattern = re.compile(extraction.expression)
                match = pattern.search(response.text)
                return match.group(1) if match and match.groups() else (match.group(0) if match else None)
        
        elif extraction.source == "response_header":
            return response.headers.get(extraction.expression, None)
        
        elif extraction.source == "cookie":
            return response.cookies.get(extraction.expression, None)
        
        elif extraction.source == "status_code":
            return response.status_code
        
        return None


class HttpRequestExecutor:
    """HTTP请求执行器"""
    
    def __init__(self, variable_manager: VariableManager):
        self.variable_manager = variable_manager
        self.session = requests.Session()
    
    def execute_request(self, request_config: RequestConfig,
                       environment: Environment = None) -> Tuple[requests.Response, float]:
        """执行HTTP请求"""
        try:
            # 构建完整URL
            url = self._build_url(request_config, environment)

            # 处理请求头
            headers = self._build_headers(request_config, environment)

            # 处理查询参数
            params = self._build_query_params(request_config)

            # 处理请求体
            data, files = self._build_request_body(request_config)

            # 处理认证
            auth = self._build_auth(request_config)

            # 添加认证相关的headers、params、form data
            self._add_auth_headers(headers, request_config)
            self._add_auth_params(params, request_config)
            if isinstance(data, dict):
                self._add_auth_form_data(data, request_config)

            # 设置默认Content-Type（如果没有设置）
            if request_config.body_type == BodyType.JSON and 'Content-Type' not in headers:
                headers['Content-Type'] = 'application/json'
            elif request_config.body_type == BodyType.X_WWW_FORM_URLENCODED and 'Content-Type' not in headers:
                headers['Content-Type'] = 'application/x-www-form-urlencoded'

            # 记录开始时间
            start_time = time.time()

            # 发送请求
            response = self.session.request(
                method=request_config.method.value,
                url=url,
                headers=headers,
                params=params,
                data=data,
                files=files,
                auth=auth,
                timeout=request_config.timeout,
                allow_redirects=request_config.allow_redirects,
                verify=request_config.verify_ssl
            )

            # 计算响应时间
            response_time = (time.time() - start_time) * 1000  # 毫秒

            return response, response_time

        except requests.exceptions.Timeout:
            raise Exception(f"请求超时 (>{request_config.timeout}秒)")
        except requests.exceptions.ConnectionError:
            raise Exception("连接错误，请检查网络或URL")
        except requests.exceptions.HTTPError as e:
            raise Exception(f"HTTP错误: {e}")
        except requests.exceptions.RequestException as e:
            raise Exception(f"请求异常: {e}")
        except Exception as e:
            raise Exception(f"未知错误: {e}")
    
    def _build_url(self, request_config: RequestConfig, environment: Environment = None) -> str:
        """构建完整URL"""
        url = self.variable_manager.replace_variables(request_config.url)
        
        if environment and environment.base_url:
            base_url = self.variable_manager.replace_variables(environment.base_url)
            if not url.startswith(('http://', 'https://')):
                url = urljoin(base_url.rstrip('/') + '/', url.lstrip('/'))
        
        return url
    
    def _build_headers(self, request_config: RequestConfig, 
                      environment: Environment = None) -> Dict[str, str]:
        """构建请求头"""
        headers = {}
        
        # 环境默认头
        if environment:
            for header in environment.headers:
                if header.enabled:
                    headers[header.key] = self.variable_manager.replace_variables(header.value)
        
        # 请求配置头
        for header in request_config.headers:
            if header.enabled:
                headers[header.key] = self.variable_manager.replace_variables(header.value)
        
        return headers
    
    def _build_query_params(self, request_config: RequestConfig) -> Dict[str, str]:
        """构建查询参数"""
        params = {}
        for param in request_config.query_params:
            if param.enabled:
                params[param.key] = self.variable_manager.replace_variables(param.value)
        return params
    
    def _build_request_body(self, request_config: RequestConfig) -> Tuple[Any, Any]:
        """构建请求体"""
        if request_config.body_type == BodyType.NONE:
            return None, None

        elif request_config.body_type == BodyType.JSON:
            if isinstance(request_config.body_data, str):
                body_str = self.variable_manager.replace_variables(request_config.body_data)
                try:
                    # 验证JSON格式
                    json.loads(body_str)
                    return body_str, None
                except json.JSONDecodeError:
                    raise Exception(f"JSON格式错误: {body_str}")
            else:
                return json.dumps(request_config.body_data, ensure_ascii=False), None

        elif request_config.body_type == BodyType.FORM_DATA:
            if isinstance(request_config.body_data, list):
                data = {}
                files = {}
                for item in request_config.body_data:
                    if item.enabled:
                        value = self.variable_manager.replace_variables(item.value)
                        # 检查是否是文件上传
                        if value.startswith('@') and len(value) > 1:
                            file_path = value[1:]  # 去掉@符号
                            if os.path.exists(file_path):
                                files[item.key] = open(file_path, 'rb')
                            else:
                                raise Exception(f"文件不存在: {file_path}")
                        else:
                            data[item.key] = value
                return data, files if files else None
            return request_config.body_data, None

        elif request_config.body_type == BodyType.X_WWW_FORM_URLENCODED:
            if isinstance(request_config.body_data, list):
                data = {}
                for item in request_config.body_data:
                    if item.enabled:
                        data[item.key] = self.variable_manager.replace_variables(item.value)
                return data, None
            elif isinstance(request_config.body_data, str):
                # 解析URL编码的字符串
                body_str = self.variable_manager.replace_variables(request_config.body_data)
                return body_str, None
            return request_config.body_data, None

        elif request_config.body_type == BodyType.XML:
            body_str = self.variable_manager.replace_variables(str(request_config.body_data))
            return body_str, None

        elif request_config.body_type == BodyType.RAW:
            body_str = self.variable_manager.replace_variables(str(request_config.body_data))
            return body_str, None

        elif request_config.body_type == BodyType.BINARY:
            # 二进制数据处理
            if isinstance(request_config.body_data, str) and request_config.body_data.startswith('@'):
                file_path = request_config.body_data[1:]  # 去掉@符号
                if os.path.exists(file_path):
                    with open(file_path, 'rb') as f:
                        return f.read(), None
                else:
                    raise Exception(f"文件不存在: {file_path}")
            return request_config.body_data, None

        return request_config.body_data, None
    
    def _build_auth(self, request_config: RequestConfig) -> Any:
        """构建认证"""
        auth_config = request_config.auth

        if auth_config.auth_type == AuthType.BASIC:
            username = self.variable_manager.replace_variables(auth_config.username)
            password = self.variable_manager.replace_variables(auth_config.password)
            return requests.auth.HTTPBasicAuth(username, password)

        elif auth_config.auth_type == AuthType.BEARER:
            # Bearer token 通过header处理，这里返回None
            return None

        elif auth_config.auth_type == AuthType.API_KEY:
            # API Key 通过header、query或form处理，这里返回None
            return None

        return None

    def _add_auth_headers(self, headers: Dict[str, str], request_config: RequestConfig):
        """添加认证相关的请求头"""
        auth_config = request_config.auth

        if auth_config.auth_type == AuthType.BEARER and auth_config.token:
            token = self.variable_manager.replace_variables(auth_config.token)
            headers['Authorization'] = f'Bearer {token}'

        elif auth_config.auth_type == AuthType.API_KEY and auth_config.api_key:
            api_key = self.variable_manager.replace_variables(auth_config.api_key)

            if auth_config.api_key_location == "header":
                key_name = auth_config.api_key_name or "X-API-Key"
                headers[key_name] = api_key

    def _add_auth_params(self, params: Dict[str, str], request_config: RequestConfig):
        """添加认证相关的查询参数"""
        auth_config = request_config.auth

        if (auth_config.auth_type == AuthType.API_KEY and
            auth_config.api_key_location == "query" and
            auth_config.api_key):

            api_key = self.variable_manager.replace_variables(auth_config.api_key)
            key_name = auth_config.api_key_name or "api_key"
            params[key_name] = api_key

    def _add_auth_form_data(self, data: Dict[str, str], request_config: RequestConfig):
        """添加认证相关的表单数据"""
        auth_config = request_config.auth

        if (auth_config.auth_type == AuthType.API_KEY and
            auth_config.api_key_location == "form" and
            auth_config.api_key):

            api_key = self.variable_manager.replace_variables(auth_config.api_key)
            key_name = auth_config.api_key_name or "api_key"
            data[key_name] = api_key
