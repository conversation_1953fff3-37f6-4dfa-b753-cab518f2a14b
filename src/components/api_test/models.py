#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
接口自动化测试数据模型
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Union
from enum import Enum
import json
import uuid
from datetime import datetime


class RequestMethod(Enum):
    """请求方法枚举"""
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"
    HEAD = "HEAD"
    OPTIONS = "OPTIONS"


class Protocol(Enum):
    """协议类型枚举"""
    HTTP = "HTTP"
    HTTPS = "HTTPS"
    WEBSOCKET = "WebSocket"


class AuthType(Enum):
    """认证类型枚举"""
    NONE = "None"
    BASIC = "Basic Auth"
    BEARER = "Bearer Token"
    API_KEY = "API Key"
    OAUTH2 = "OAuth2.0"
    CUSTOM = "Custom"


class BodyType(Enum):
    """请求体类型枚举"""
    NONE = "None"
    FORM_DATA = "Form Data"
    X_WWW_FORM_URLENCODED = "x-www-form-urlencoded"
    JSON = "JSON"
    XML = "XML"
    RAW = "Raw"
    BINARY = "Binary"


class AssertionType(Enum):
    """断言类型枚举"""
    STATUS_CODE = "Status Code"
    RESPONSE_TIME = "Response Time"
    RESPONSE_HEADER = "Response Header"
    RESPONSE_BODY = "Response Body"
    JSON_PATH = "JSON Path"
    XPATH = "XPath"
    REGEX = "Regex"
    DATABASE = "Database"


class VariableSource(Enum):
    """变量提取数据源"""
    RESPONSE_BODY = "response_body"
    RESPONSE_HEADERS = "response_headers"
    COOKIES = "cookies"
    STATUS_CODE = "status_code"


class ExtractionMethod(Enum):
    """变量提取方法"""
    JSON_PATH = "json_path"
    XPATH = "xpath"
    REGEX = "regex"
    HEADER = "header"


class CaseStatus(Enum):
    """用例状态枚举"""
    ENABLED = "Enabled"
    DISABLED = "Disabled"
    PENDING = "Pending"


class CasePriority(Enum):
    """用例优先级枚举"""
    HIGH = "High"
    MEDIUM = "Medium"
    LOW = "Low"


class ExecutionStatus(Enum):
    """执行状态枚举"""
    NOT_RUN = "Not Run"
    RUNNING = "Running"
    PASSED = "Passed"
    FAILED = "Failed"
    SKIPPED = "Skipped"
    ERROR = "Error"


@dataclass
class KeyValue:
    """键值对数据结构"""
    key: str = ""
    value: str = ""
    enabled: bool = True
    description: str = ""


@dataclass
class AuthConfig:
    """认证配置"""
    auth_type: AuthType = AuthType.NONE
    username: str = ""
    password: str = ""
    token: str = ""
    api_key: str = ""
    api_key_location: str = "header"  # header, query, form
    api_key_name: str = "X-API-Key"
    oauth2_config: Dict[str, Any] = field(default_factory=dict)
    custom_config: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RequestConfig:
    """请求配置"""
    method: RequestMethod = RequestMethod.GET
    url: str = ""
    protocol: Protocol = Protocol.HTTPS
    headers: List[KeyValue] = field(default_factory=list)
    query_params: List[KeyValue] = field(default_factory=list)
    body_type: BodyType = BodyType.NONE
    body_data: Union[str, Dict[str, Any], List[KeyValue]] = ""
    auth: AuthConfig = field(default_factory=AuthConfig)
    timeout: int = 30
    allow_redirects: bool = True
    verify_ssl: bool = True


@dataclass
class Assertion:
    """断言配置"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    assertion_type: AssertionType = AssertionType.STATUS_CODE
    field_path: str = ""  # JSON路径或XPath
    operator: str = "equals"  # equals, not_equals, contains, not_contains, greater_than, less_than, regex_match
    expected_value: str = ""
    enabled: bool = True
    description: str = ""


@dataclass
class VariableExtraction:
    """变量提取配置"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    variable_name: str = ""
    source: VariableSource = VariableSource.RESPONSE_BODY
    extraction_method: ExtractionMethod = ExtractionMethod.JSON_PATH
    expression: str = ""
    default_value: str = ""
    enabled: bool = True
    description: str = ""


@dataclass
class PrePostOperation:
    """前后置操作配置"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    operation_type: str = "http_request"  # http_request, sql_script, custom_script
    config: Dict[str, Any] = field(default_factory=dict)
    enabled: bool = True
    description: str = ""


@dataclass
class TestCase:
    """测试用例"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    project_id: str = ""
    module_id: str = ""
    tags: List[str] = field(default_factory=list)
    priority: CasePriority = CasePriority.MEDIUM
    status: CaseStatus = CaseStatus.ENABLED
    
    # 请求配置
    request: RequestConfig = field(default_factory=RequestConfig)
    
    # 断言配置
    assertions: List[Assertion] = field(default_factory=list)
    
    # 变量提取
    variable_extractions: List[VariableExtraction] = field(default_factory=list)

    # 数据库断言
    database_assertions: List[Dict[str, Any]] = field(default_factory=list)

    # 前后置操作
    pre_operations: List[PrePostOperation] = field(default_factory=list)
    post_operations: List[PrePostOperation] = field(default_factory=list)
    
    # 执行控制
    skip: bool = False
    skip_reason: str = ""
    retry_count: int = 0
    retry_interval: int = 1  # 秒
    
    # 元数据
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    created_by: str = ""
    updated_by: str = ""
    version: int = 1


@dataclass
class TestSuite:
    """测试套件"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    project_id: str = ""
    case_ids: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    
    # 执行配置
    parallel_execution: bool = False
    max_workers: int = 5
    stop_on_failure: bool = False
    
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


@dataclass
class Environment:
    """环境配置"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    base_url: str = ""
    variables: Dict[str, str] = field(default_factory=dict)
    headers: List[KeyValue] = field(default_factory=list)
    timeout: int = 30
    is_default: bool = False
    
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


@dataclass
class ExecutionResult:
    """执行结果"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    case_id: str = ""
    case_name: str = ""
    status: ExecutionStatus = ExecutionStatus.NOT_RUN
    
    # 请求信息
    request_url: str = ""
    request_method: str = ""
    request_headers: Dict[str, str] = field(default_factory=dict)
    request_body: str = ""
    
    # 响应信息
    response_status_code: int = 0
    response_headers: Dict[str, str] = field(default_factory=dict)
    response_body: str = ""
    response_time: float = 0.0
    response_size: int = 0
    
    # 断言结果
    assertion_results: List[Dict[str, Any]] = field(default_factory=list)
    
    # 变量提取结果
    extracted_variables: Dict[str, str] = field(default_factory=dict)
    
    # 错误信息
    error_message: str = ""
    error_traceback: str = ""
    
    # 跳过信息
    skip_reason: str = ""
    
    # 执行时间
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    
    # 重试信息
    retry_count: int = 0
    is_retry: bool = False


@dataclass
class ExecutionReport:
    """执行报告"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    environment_id: str = ""
    environment_name: str = ""
    
    # 执行统计
    total_cases: int = 0
    passed_cases: int = 0
    failed_cases: int = 0
    skipped_cases: int = 0
    error_cases: int = 0
    
    # 时间统计
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    total_duration: float = 0.0
    
    # 执行结果
    case_results: List[ExecutionResult] = field(default_factory=list)
    
    # 元数据
    created_by: str = ""
    tags: List[str] = field(default_factory=list)


@dataclass
class Project:
    """项目"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    created_by: str = ""


@dataclass
class Module:
    """模块"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    project_id: str = ""
    parent_id: Optional[str] = None
    
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
