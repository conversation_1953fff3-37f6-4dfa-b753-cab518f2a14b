#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
接口自动化测试模块初始化
"""

from .models import *
from .dao import ApiTestDAO
from .executor import VariableManager, AssertionEngine, VariableExtractor, HttpRequestExecutor
from .test_engine import TestEngine, execution_manager

__all__ = [
    # 数据模型
    'Project', 'Module', 'Environment', 'TestCase', 'TestSuite',
    'ExecutionResult', 'ExecutionReport', 'KeyValue', 'AuthConfig',
    'RequestConfig', 'Assertion', 'VariableExtraction', 'PrePostOperation',
    
    # 枚举类型
    'RequestMethod', 'Protocol', 'AuthType', 'BodyType', 'AssertionType',
    'CaseStatus', 'CasePriority', 'ExecutionStatus',
    
    # 数据访问
    'ApiTestDAO',
    
    # 执行引擎
    'VariableManager', 'AssertionEngine', 'VariableExtractor', 'HttpRequestExecutor',
    'TestEngine', 'execution_manager'
]


def init_api_test_module():
    """初始化接口测试模块"""
    try:
        # 初始化数据库
        dao = ApiTestDAO()
        
        # 创建默认环境（如果不存在）
        environments = dao.get_environments()
        if not environments:
            default_env = Environment(
                name="默认环境",
                description="系统默认创建的测试环境",
                base_url="https://api.example.com",
                is_default=True,
                variables={
                    "host": "api.example.com",
                    "version": "v1"
                },
                headers=[
                    KeyValue(key="Content-Type", value="application/json", enabled=True),
                    KeyValue(key="Accept", value="application/json", enabled=True)
                ]
            )
            dao.create_environment(default_env)
        
        # 创建示例项目（如果不存在）
        projects = dao.get_projects()
        if not projects:
            sample_project = Project(
                name="示例项目",
                description="系统默认创建的示例项目，用于演示接口测试功能",
                created_by="系统"
            )
            dao.create_project(sample_project)
            
            # 创建示例测试用例
            sample_case = TestCase(
                name="示例接口测试",
                description="这是一个示例测试用例，演示如何测试GET接口",
                project_id=sample_project.id,
                tags=["示例", "GET请求"],
                priority=CasePriority.MEDIUM,
                status=CaseStatus.ENABLED,
                request=RequestConfig(
                    method=RequestMethod.GET,
                    url="/api/v1/health",
                    headers=[
                        KeyValue(key="User-Agent", value="EpayTools-ApiTest/1.0", enabled=True)
                    ]
                ),
                assertions=[
                    Assertion(
                        assertion_type=AssertionType.STATUS_CODE,
                        operator="equals",
                        expected_value="200",
                        enabled=True,
                        description="检查响应状态码为200"
                    ),
                    Assertion(
                        assertion_type=AssertionType.RESPONSE_TIME,
                        operator="less_than",
                        expected_value="1000",
                        enabled=True,
                        description="检查响应时间小于1秒"
                    )
                ],
                created_by="系统"
            )
            dao.create_test_case(sample_case)
        
        return True
        
    except Exception as e:
        print(f"初始化接口测试模块失败: {e}")
        return False


# 自动初始化
init_api_test_module()
