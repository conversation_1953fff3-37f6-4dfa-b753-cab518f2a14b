"""
函数助手
提供内置函数和自定义函数支持，使用沙箱模式确保安全性
"""

import re
import time
import random
import string
import hashlib
import base64
import uuid
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Callable
import os
import importlib.util
import sys


class FunctionHelper:
    """函数助手"""
    
    def __init__(self):
        self.builtin_functions = self._init_builtin_functions()
        self.custom_functions: Dict[str, Callable] = {}
        self.custom_functions_dir = "functions"  # 自定义函数目录
        self._load_custom_functions()
    
    def _init_builtin_functions(self) -> Dict[str, Callable]:
        """初始化内置函数"""
        return {
            # 时间函数
            "current_time": self._current_time,
            "current_timestamp": self._current_timestamp,
            "format_time": self._format_time,
            "add_days": self._add_days,
            "add_hours": self._add_hours,
            
            # 随机函数
            "random_int": self._random_int,
            "random_float": self._random_float,
            "random_string": self._random_string,
            "random_choice": self._random_choice,
            "uuid4": self._uuid4,
            
            # 加密函数
            "md5": self._md5,
            "sha1": self._sha1,
            "sha256": self._sha256,
            "base64_encode": self._base64_encode,
            "base64_decode": self._base64_decode,
            
            # 数据转换函数
            "to_json": self._to_json,
            "from_json": self._from_json,
            "url_encode": self._url_encode,
            "url_decode": self._url_decode,
            
            # 字符串函数
            "upper": self._upper,
            "lower": self._lower,
            "substring": self._substring,
            "replace": self._replace,
            "concat": self._concat,
        }
    
    def process_text(self, text: str, variables: Dict[str, Any] = None) -> str:
        """处理文本中的函数调用和变量替换"""
        if not isinstance(text, str):
            return text
        
        variables = variables or {}
        result = text
        
        # 处理函数调用 ${function_name(args)}
        function_pattern = r'\$\{([^}]+)\}'
        
        def replace_function(match):
            expression = match.group(1)
            return str(self._evaluate_expression(expression, variables))
        
        result = re.sub(function_pattern, replace_function, result)
        
        # 处理简单变量替换 ${variable_name}
        for key, value in variables.items():
            result = result.replace(f"${{{key}}}", str(value))
        
        return result
    
    def _evaluate_expression(self, expression: str, variables: Dict[str, Any]) -> Any:
        """评估表达式（安全模式）"""
        try:
            # 检查是否是函数调用
            if '(' in expression and ')' in expression:
                return self._call_function(expression, variables)
            else:
                # 简单变量引用
                return variables.get(expression, expression)
                
        except Exception as e:
            print(f"表达式评估失败: {expression}, 错误: {e}")
            return expression
    
    def _call_function(self, expression: str, variables: Dict[str, Any]) -> Any:
        """调用函数"""
        # 解析函数名和参数
        match = re.match(r'(\w+)\((.*)\)', expression.strip())
        if not match:
            return expression
        
        func_name = match.group(1)
        args_str = match.group(2)
        
        # 解析参数
        args = self._parse_arguments(args_str, variables)
        
        # 调用函数
        if func_name in self.builtin_functions:
            return self.builtin_functions[func_name](*args)
        elif func_name in self.custom_functions:
            return self.custom_functions[func_name](*args)
        else:
            print(f"未知函数: {func_name}")
            return expression
    
    def _parse_arguments(self, args_str: str, variables: Dict[str, Any]) -> List[Any]:
        """解析函数参数"""
        if not args_str.strip():
            return []
        
        args = []
        # 简单的参数解析（支持字符串、数字、变量）
        for arg in args_str.split(','):
            arg = arg.strip()
            
            if not arg:
                continue
            
            # 字符串参数
            if (arg.startswith('"') and arg.endswith('"')) or (arg.startswith("'") and arg.endswith("'")):
                args.append(arg[1:-1])
            # 数字参数
            elif arg.replace('.', '').replace('-', '').isdigit():
                args.append(float(arg) if '.' in arg else int(arg))
            # 变量参数
            elif arg in variables:
                args.append(variables[arg])
            # 默认作为字符串
            else:
                args.append(arg)
        
        return args
    
    # 内置函数实现
    def _current_time(self, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
        """获取当前时间"""
        return datetime.now().strftime(format_str)
    
    def _current_timestamp(self) -> int:
        """获取当前时间戳"""
        return int(time.time())
    
    def _format_time(self, timestamp: int, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
        """格式化时间戳"""
        return datetime.fromtimestamp(timestamp).strftime(format_str)
    
    def _add_days(self, days: int, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
        """添加天数"""
        target_time = datetime.now() + timedelta(days=days)
        return target_time.strftime(format_str)
    
    def _add_hours(self, hours: int, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
        """添加小时数"""
        target_time = datetime.now() + timedelta(hours=hours)
        return target_time.strftime(format_str)
    
    def _random_int(self, min_val: int = 0, max_val: int = 100) -> int:
        """生成随机整数"""
        return random.randint(min_val, max_val)
    
    def _random_float(self, min_val: float = 0.0, max_val: float = 1.0) -> float:
        """生成随机浮点数"""
        return random.uniform(min_val, max_val)
    
    def _random_string(self, length: int = 8, chars: str = None) -> str:
        """生成随机字符串"""
        if chars is None:
            chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(length))
    
    def _random_choice(self, *choices) -> Any:
        """随机选择"""
        return random.choice(choices)
    
    def _uuid4(self) -> str:
        """生成UUID"""
        return str(uuid.uuid4())
    
    def _md5(self, text: str) -> str:
        """MD5加密"""
        return hashlib.md5(text.encode()).hexdigest()
    
    def _sha1(self, text: str) -> str:
        """SHA1加密"""
        return hashlib.sha1(text.encode()).hexdigest()
    
    def _sha256(self, text: str) -> str:
        """SHA256加密"""
        return hashlib.sha256(text.encode()).hexdigest()
    
    def _base64_encode(self, text: str) -> str:
        """Base64编码"""
        return base64.b64encode(text.encode()).decode()
    
    def _base64_decode(self, text: str) -> str:
        """Base64解码"""
        return base64.b64decode(text.encode()).decode()
    
    def _to_json(self, obj: Any) -> str:
        """转换为JSON"""
        return json.dumps(obj, ensure_ascii=False)
    
    def _from_json(self, text: str) -> Any:
        """从JSON解析"""
        return json.loads(text)
    
    def _url_encode(self, text: str) -> str:
        """URL编码"""
        import urllib.parse
        return urllib.parse.quote(text)
    
    def _url_decode(self, text: str) -> str:
        """URL解码"""
        import urllib.parse
        return urllib.parse.unquote(text)
    
    def _upper(self, text: str) -> str:
        """转大写"""
        return text.upper()
    
    def _lower(self, text: str) -> str:
        """转小写"""
        return text.lower()
    
    def _substring(self, text: str, start: int, end: int = None) -> str:
        """截取子字符串"""
        return text[start:end]
    
    def _replace(self, text: str, old: str, new: str) -> str:
        """替换字符串"""
        return text.replace(old, new)
    
    def _concat(self, *args) -> str:
        """连接字符串"""
        return ''.join(str(arg) for arg in args)
    
    def _load_custom_functions(self):
        """加载自定义函数"""
        try:
            if not os.path.exists(self.custom_functions_dir):
                os.makedirs(self.custom_functions_dir)
                return
            
            for filename in os.listdir(self.custom_functions_dir):
                if filename.endswith('.py') and not filename.startswith('__'):
                    self._load_function_file(filename)
                    
        except Exception as e:
            print(f"加载自定义函数失败: {e}")
    
    def _load_function_file(self, filename: str):
        """加载单个函数文件"""
        try:
            file_path = os.path.join(self.custom_functions_dir, filename)
            spec = importlib.util.spec_from_file_location("custom_functions", file_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 获取模块中的所有函数
            for name in dir(module):
                if not name.startswith('_') and callable(getattr(module, name)):
                    self.custom_functions[name] = getattr(module, name)
                    
        except Exception as e:
            print(f"加载函数文件失败 {filename}: {e}")
    
    def get_available_functions(self) -> Dict[str, List[str]]:
        """获取可用函数列表"""
        return {
            "builtin": list(self.builtin_functions.keys()),
            "custom": list(self.custom_functions.keys())
        }
    
    def create_sample_function_file(self):
        """创建示例函数文件"""
        try:
            if not os.path.exists(self.custom_functions_dir):
                os.makedirs(self.custom_functions_dir)
            
            sample_file = os.path.join(self.custom_functions_dir, "sample_functions.py")
            
            sample_code = '''"""
示例自定义函数
"""

def get_order_id():
    """生成订单ID"""
    import time
    import random
    return f"ORDER_{int(time.time())}_{random.randint(1000, 9999)}"

def encrypt_password(password):
    """加密密码"""
    import hashlib
    return hashlib.sha256(password.encode()).hexdigest()

def generate_phone():
    """生成手机号"""
    import random
    return f"138{random.randint(10000000, 99999999)}"
'''
            
            with open(sample_file, 'w', encoding='utf-8') as f:
                f.write(sample_code)
            
            return True
            
        except Exception as e:
            print(f"创建示例函数文件失败: {e}")
            return False


# 全局函数助手实例
function_helper = FunctionHelper()
