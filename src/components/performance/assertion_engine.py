"""
断言引擎
支持多种断言方式：状态码、响应文本、JSO<PERSON>、XML、响应时间等
"""

import re
import json
import time
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from enum import Enum
import xml.etree.ElementTree as ET


class AssertionType(Enum):
    """断言类型"""
    STATUS_CODE = "status_code"
    RESPONSE_TEXT = "response_text"
    JSON_PATH = "json_path"
    XML_PATH = "xml_path"
    RESPONSE_TIME = "response_time"
    HEADER = "header"
    REGEX = "regex"
    SIZE = "size"


class ComparisonOperator(Enum):
    """比较操作符"""
    EQUALS = "equals"
    NOT_EQUALS = "not_equals"
    CONTAINS = "contains"
    NOT_CONTAINS = "not_contains"
    GREATER_THAN = "greater_than"
    LESS_THAN = "less_than"
    GREATER_EQUAL = "greater_equal"
    LESS_EQUAL = "less_equal"
    STARTS_WITH = "starts_with"
    ENDS_WITH = "ends_with"
    REGEX_MATCH = "regex_match"
    EXISTS = "exists"
    NOT_EXISTS = "not_exists"


@dataclass
class Assertion:
    """断言配置"""
    name: str
    assertion_type: AssertionType
    target: str                           # 断言目标（JSON路径、Header名等）
    operator: ComparisonOperator
    expected_value: Any
    error_message: str = ""
    stop_on_failure: bool = True          # 失败时是否停止用户
    
    def __post_init__(self):
        if not self.error_message:
            self.error_message = f"断言失败: {self.name}"


class AssertionResult:
    """断言结果"""
    
    def __init__(self, assertion: Assertion, success: bool, actual_value: Any = None, error_message: str = ""):
        self.assertion = assertion
        self.success = success
        self.actual_value = actual_value
        self.error_message = error_message or assertion.error_message
        self.timestamp = time.time()


class AssertionEngine:
    """断言引擎"""
    
    def __init__(self):
        self.assertion_results: List[AssertionResult] = []
    
    def execute_assertions(self, response, assertions: List[Assertion], 
                          response_time: float = None) -> List[AssertionResult]:
        """执行断言列表"""
        results = []
        
        for assertion in assertions:
            try:
                result = self._execute_single_assertion(response, assertion, response_time)
                results.append(result)
                self.assertion_results.append(result)
                
                # 如果断言失败且设置了停止标志，抛出异常
                if not result.success and assertion.stop_on_failure:
                    from locust.exception import StopUser
                    raise StopUser(result.error_message)
                    
            except Exception as e:
                error_result = AssertionResult(
                    assertion=assertion,
                    success=False,
                    error_message=f"断言执行异常: {str(e)}"
                )
                results.append(error_result)
                self.assertion_results.append(error_result)
                
                if assertion.stop_on_failure:
                    from locust.exception import StopUser
                    raise StopUser(error_result.error_message)
        
        return results
    
    def _execute_single_assertion(self, response, assertion: Assertion, 
                                 response_time: float = None) -> AssertionResult:
        """执行单个断言"""
        try:
            actual_value = self._get_actual_value(response, assertion, response_time)
            success = self._compare_values(actual_value, assertion.expected_value, assertion.operator)
            
            return AssertionResult(
                assertion=assertion,
                success=success,
                actual_value=actual_value,
                error_message="" if success else f"{assertion.error_message} (期望: {assertion.expected_value}, 实际: {actual_value})"
            )
            
        except Exception as e:
            return AssertionResult(
                assertion=assertion,
                success=False,
                error_message=f"断言执行失败: {str(e)}"
            )
    
    def _get_actual_value(self, response, assertion: Assertion, response_time: float = None) -> Any:
        """获取实际值"""
        if assertion.assertion_type == AssertionType.STATUS_CODE:
            return response.status_code if hasattr(response, 'status_code') else None
        
        elif assertion.assertion_type == AssertionType.RESPONSE_TEXT:
            return response.text if hasattr(response, 'text') else str(response)
        
        elif assertion.assertion_type == AssertionType.JSON_PATH:
            return self._get_json_value(response, assertion.target)
        
        elif assertion.assertion_type == AssertionType.XML_PATH:
            return self._get_xml_value(response, assertion.target)
        
        elif assertion.assertion_type == AssertionType.RESPONSE_TIME:
            return response_time
        
        elif assertion.assertion_type == AssertionType.HEADER:
            return response.headers.get(assertion.target) if hasattr(response, 'headers') else None
        
        elif assertion.assertion_type == AssertionType.REGEX:
            text = response.text if hasattr(response, 'text') else str(response)
            matches = re.findall(assertion.target, text)
            return matches[0] if matches else None
        
        elif assertion.assertion_type == AssertionType.SIZE:
            text = response.text if hasattr(response, 'text') else str(response)
            return len(text)
        
        else:
            return None
    
    def _get_json_value(self, response, json_path: str) -> Any:
        """获取JSON路径值"""
        try:
            if hasattr(response, 'json'):
                data = response.json()
            else:
                data = json.loads(response.text if hasattr(response, 'text') else str(response))
            
            # 简单的JSON路径实现
            if json_path.startswith('$.'):
                json_path = json_path[2:]
            
            current = data
            for key in json_path.split('.'):
                if isinstance(current, dict):
                    current = current.get(key)
                elif isinstance(current, list) and key.isdigit():
                    index = int(key)
                    current = current[index] if 0 <= index < len(current) else None
                else:
                    return None
                
                if current is None:
                    break
            
            return current
            
        except Exception as e:
            print(f"JSON路径解析失败: {e}")
            return None
    
    def _get_xml_value(self, response, xml_path: str) -> Any:
        """获取XML路径值"""
        try:
            text = response.text if hasattr(response, 'text') else str(response)
            root = ET.fromstring(text)
            
            # 简单的XML路径实现
            elements = root.findall(xml_path)
            return elements[0].text if elements else None
            
        except Exception as e:
            print(f"XML路径解析失败: {e}")
            return None
    
    def _compare_values(self, actual: Any, expected: Any, operator: ComparisonOperator) -> bool:
        """比较值"""
        try:
            if operator == ComparisonOperator.EQUALS:
                return actual == expected
            
            elif operator == ComparisonOperator.NOT_EQUALS:
                return actual != expected
            
            elif operator == ComparisonOperator.CONTAINS:
                return str(expected) in str(actual)
            
            elif operator == ComparisonOperator.NOT_CONTAINS:
                return str(expected) not in str(actual)
            
            elif operator == ComparisonOperator.GREATER_THAN:
                return float(actual) > float(expected)
            
            elif operator == ComparisonOperator.LESS_THAN:
                return float(actual) < float(expected)
            
            elif operator == ComparisonOperator.GREATER_EQUAL:
                return float(actual) >= float(expected)
            
            elif operator == ComparisonOperator.LESS_EQUAL:
                return float(actual) <= float(expected)
            
            elif operator == ComparisonOperator.STARTS_WITH:
                return str(actual).startswith(str(expected))
            
            elif operator == ComparisonOperator.ENDS_WITH:
                return str(actual).endswith(str(expected))
            
            elif operator == ComparisonOperator.REGEX_MATCH:
                return bool(re.search(str(expected), str(actual)))
            
            elif operator == ComparisonOperator.EXISTS:
                return actual is not None
            
            elif operator == ComparisonOperator.NOT_EXISTS:
                return actual is None
            
            else:
                return False
                
        except Exception as e:
            print(f"值比较失败: {e}")
            return False
    
    def get_assertion_summary(self) -> Dict[str, Any]:
        """获取断言摘要"""
        total = len(self.assertion_results)
        success = sum(1 for r in self.assertion_results if r.success)
        failed = total - success
        
        return {
            "total": total,
            "success": success,
            "failed": failed,
            "success_rate": (success / total * 100) if total > 0 else 0
        }
    
    def get_failed_assertions(self) -> List[AssertionResult]:
        """获取失败的断言"""
        return [r for r in self.assertion_results if not r.success]
    
    def clear_results(self):
        """清除断言结果"""
        self.assertion_results.clear()


class AssertionBuilder:
    """断言构建器"""
    
    @staticmethod
    def status_code(expected_code: int, stop_on_failure: bool = True) -> Assertion:
        """状态码断言"""
        return Assertion(
            name=f"状态码等于{expected_code}",
            assertion_type=AssertionType.STATUS_CODE,
            target="",
            operator=ComparisonOperator.EQUALS,
            expected_value=expected_code,
            stop_on_failure=stop_on_failure
        )
    
    @staticmethod
    def response_contains(text: str, stop_on_failure: bool = False) -> Assertion:
        """响应包含文本断言"""
        return Assertion(
            name=f"响应包含'{text}'",
            assertion_type=AssertionType.RESPONSE_TEXT,
            target="",
            operator=ComparisonOperator.CONTAINS,
            expected_value=text,
            stop_on_failure=stop_on_failure
        )
    
    @staticmethod
    def json_equals(json_path: str, expected_value: Any, stop_on_failure: bool = False) -> Assertion:
        """JSON字段等于断言"""
        return Assertion(
            name=f"JSON {json_path} 等于 {expected_value}",
            assertion_type=AssertionType.JSON_PATH,
            target=json_path,
            operator=ComparisonOperator.EQUALS,
            expected_value=expected_value,
            stop_on_failure=stop_on_failure
        )
    
    @staticmethod
    def json_exists(json_path: str, stop_on_failure: bool = False) -> Assertion:
        """JSON字段存在断言"""
        return Assertion(
            name=f"JSON {json_path} 存在",
            assertion_type=AssertionType.JSON_PATH,
            target=json_path,
            operator=ComparisonOperator.EXISTS,
            expected_value=None,
            stop_on_failure=stop_on_failure
        )
    
    @staticmethod
    def response_time_less_than(max_time: float, stop_on_failure: bool = False) -> Assertion:
        """响应时间小于断言"""
        return Assertion(
            name=f"响应时间小于{max_time}ms",
            assertion_type=AssertionType.RESPONSE_TIME,
            target="",
            operator=ComparisonOperator.LESS_THAN,
            expected_value=max_time,
            stop_on_failure=stop_on_failure
        )
    
    @staticmethod
    def header_exists(header_name: str, stop_on_failure: bool = False) -> Assertion:
        """响应头存在断言"""
        return Assertion(
            name=f"响应头 {header_name} 存在",
            assertion_type=AssertionType.HEADER,
            target=header_name,
            operator=ComparisonOperator.EXISTS,
            expected_value=None,
            stop_on_failure=stop_on_failure
        )


# 全局断言引擎实例
assertion_engine = AssertionEngine()
