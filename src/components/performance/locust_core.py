"""
Locust核心封装
提供完整的性能测试功能
"""

import json
import time
import threading
import subprocess
import tempfile
import os
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
import uuid


class TestStatus(Enum):
    """测试状态"""
    IDLE = "idle"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"


@dataclass
class TestConfig:
    """测试配置"""
    # 基础参数
    users: int = 10
    spawn_rate: float = 2.0
    run_time: int = 60
    host: str = "http://localhost:8080"

    # 高级参数
    think_time_min: float = 1.0
    think_time_max: float = 3.0
    timeout: int = 30
    task_interval: float = 0.0

    # 前置步骤
    on_start_apis: List[Dict[str, Any]] = field(default_factory=list)

    # 测试任务
    test_tasks: List[Dict[str, Any]] = field(default_factory=list)

    # 参数化配置
    parameterization: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ApiConfig:
    """接口配置"""
    name: str
    method: str
    url: str
    headers: Dict[str, str] = field(default_factory=dict)
    data: Any = None
    params: Dict[str, str] = field(default_factory=dict)

    # 提取器配置
    extractors: List[Dict[str, Any]] = field(default_factory=list)

    # 断言配置
    assertions: List[Dict[str, Any]] = field(default_factory=list)

    # 函数助手
    functions: List[str] = field(default_factory=list)


@dataclass
class TaskConfig:
    """任务配置"""
    name: str
    weight: int = 1
    sequence: int = 0
    apis: List[ApiConfig] = field(default_factory=list)


class LocustCore:
    """Locust核心管理器"""

    def __init__(self):
        self.status = TestStatus.IDLE
        self.process: Optional[subprocess.Popen] = None
        self.test_id: Optional[str] = None
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
        self.config: Optional[TestConfig] = None
        self.results: Dict[str, Any] = {}
        self.lock = threading.Lock()

        # 监控线程
        self.monitor_thread: Optional[threading.Thread] = None
        self.stop_monitor = False

    def start_test(self, config: TestConfig) -> bool:
        """启动性能测试"""
        try:
            with self.lock:
                # 允许从IDLE或STOPPED状态启动测试
                if self.status not in [TestStatus.IDLE, TestStatus.STOPPED]:
                    return False

                self.status = TestStatus.STARTING
                self.config = config
                self.test_id = str(uuid.uuid4())
                self.start_time = time.time()
                self.end_time = None  # 重置结束时间
                self.results = {}

            # 模拟测试执行（因为真实Locust需要安装和配置）
            success = self._simulate_test_execution(config)

            if success:
                with self.lock:
                    self.status = TestStatus.RUNNING

                # 启动模拟监控线程
                self.stop_monitor = False
                self.monitor_thread = threading.Thread(target=self._simulate_monitor)
                self.monitor_thread.start()

                return True
            else:
                with self.lock:
                    self.status = TestStatus.ERROR
                return False

        except Exception as e:
            with self.lock:
                self.status = TestStatus.ERROR
            print(f"启动测试失败: {e}")
            return False

    def _simulate_test_execution(self, config: TestConfig) -> bool:
        """模拟测试执行"""
        try:
            import random
            from datetime import datetime

            # 收集所有要测试的接口
            all_apis = []

            # 添加前置接口
            for api in config.on_start_apis:
                all_apis.append({
                    "name": api["name"],
                    "method": api["method"],
                    "url": api["url"],
                    "type": "前置接口"
                })

            # 添加测试任务中的接口
            for task in config.test_tasks:
                for api in task["apis"]:
                    all_apis.append({
                        "name": api["name"],
                        "method": api["method"],
                        "url": api["url"],
                        "type": "测试接口",
                        "task": task["name"]
                    })

            # 如果没有配置接口，使用默认接口
            if not all_apis:
                all_apis = [{
                    "name": "默认测试接口",
                    "method": "GET",
                    "url": "/",
                    "type": "默认接口"
                }]

            # 为每个接口生成模拟结果
            api_results = []
            total_requests = 0
            total_failed = 0

            for api in all_apis:
                # 根据接口权重和配置生成请求数
                api_requests = config.users * config.run_time * random.randint(1, 4)
                api_failed = int(api_requests * random.uniform(0.0, 0.08))  # 0-8%失败率
                api_success = api_requests - api_failed

                api_avg_time = random.uniform(100, 500)
                api_min_time = api_avg_time * random.uniform(0.2, 0.5)
                api_max_time = api_avg_time * random.uniform(1.5, 3.0)

                api_result = {
                    "name": api["name"],
                    "method": api["method"],
                    "url": api["url"],
                    "type": api["type"],
                    "requests": api_requests,
                    "failures": api_failed,
                    "success_rate": (api_success / api_requests * 100) if api_requests > 0 else 0,
                    "avg_response_time": api_avg_time,
                    "min_response_time": api_min_time,
                    "max_response_time": api_max_time,
                    "rps": api_requests / config.run_time if config.run_time > 0 else 0
                }

                if "task" in api:
                    api_result["task"] = api["task"]

                api_results.append(api_result)
                total_requests += api_requests
                total_failed += api_failed

            # 计算总体统计
            total_success = total_requests - total_failed
            overall_success_rate = (total_success / total_requests * 100) if total_requests > 0 else 0
            overall_avg_time = sum(api["avg_response_time"] for api in api_results) / len(api_results) if api_results else 0

            # 构建测试结果
            with self.lock:
                self.results = {
                    'data': {
                        'Request Count': str(total_requests),
                        'Failure Count': str(total_failed),
                        'Average Response Time': f"{overall_avg_time:.1f}",
                        'Min Response Time': f"{min(api['min_response_time'] for api in api_results):.1f}" if api_results else "0",
                        'Max Response Time': f"{max(api['max_response_time'] for api in api_results):.1f}" if api_results else "0",
                        'Requests/s': f"{total_requests / config.run_time:.1f}" if config.run_time > 0 else "0",
                        'Median Response Time': f"{overall_avg_time * 0.9:.1f}"
                    },
                    'metrics': {
                        'success_rate': overall_success_rate,
                        'total_apis': len(api_results),
                        'test_duration': config.run_time,
                        'concurrent_users': config.users
                    },
                    'api_results': api_results,  # 详细的接口结果
                    'failed_requests': [
                        {
                            "api": api["name"],
                            "method": api["method"],
                            "url": api["url"],
                            "error": f"模拟错误 {random.randint(1, 5)}",
                            "count": api["failures"]
                        }
                        for api in api_results if api["failures"] > 0
                    ]
                }

            return True

        except Exception as e:
            print(f"模拟测试执行失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _simulate_monitor(self):
        """模拟监控测试进度"""
        try:
            start_time = time.time()

            while not self.stop_monitor and self.status == TestStatus.RUNNING:
                elapsed = time.time() - start_time

                if elapsed >= self.config.run_time:
                    # 测试完成
                    self._parse_final_results()
                    self._save_test_report()

                    with self.lock:
                        self.end_time = time.time()  # 设置结束时间
                        self.status = TestStatus.STOPPED
                    break

                time.sleep(1)

        except Exception as e:
            print(f"监控异常: {e}")
            with self.lock:
                self.status = TestStatus.ERROR
    
    def stop_test(self) -> bool:
        """停止性能测试"""
        try:
            with self.lock:
                if self.status != TestStatus.RUNNING:
                    return False

                self.status = TestStatus.STOPPING

            # 停止监控线程
            self.stop_monitor = True

            # 等待监控线程结束
            if self.monitor_thread:
                self.monitor_thread.join(timeout=5)

            # 解析最终结果
            self._parse_final_results()

            # 保存测试报告
            self._save_test_report()

            with self.lock:
                self.end_time = time.time()  # 记录结束时间
                self.status = TestStatus.STOPPED

            return True

        except Exception as e:
            with self.lock:
                self.status = TestStatus.ERROR
            print(f"停止测试失败: {e}")
            return False

    def get_status(self) -> Dict[str, Any]:
        """获取测试状态"""
        with self.lock:
            # 计算持续时间：如果测试已结束，使用结束时间；否则使用当前时间
            if self.start_time:
                if self.end_time and self.status in [TestStatus.STOPPED, TestStatus.ERROR]:
                    duration = self.end_time - self.start_time
                else:
                    duration = time.time() - self.start_time
            else:
                duration = 0

            return {
                "status": self.status.value,
                "test_id": self.test_id,
                "duration": duration,
                "start_time": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.start_time)) if self.start_time else None
            }

    def get_results(self) -> Dict[str, Any]:
        """获取测试结果"""
        with self.lock:
            return self.results.copy() if self.results else {}
    
    def _generate_locust_script(self, config: TestConfig) -> str:
        """生成Locust测试脚本"""
        script = f'''
import json
import time
import random
import re
import threading
from locust import HttpUser, task, between
from locust.exception import StopUser

# 全局参数化数据池
PARAM_DATA_POOL = []
PARAM_DATA_LOCK = threading.Lock()
PARAM_DATA_INDEX = 0

class PerformanceTestUser(HttpUser):
    wait_time = between({config.think_time_min}, {config.think_time_max})

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.variables = {{}}  # 变量池
        self.user_data = {{}}  # 用户参数化数据
        self.user_id = f"user_{{random.randint(1000, 9999)}}"

    def on_start(self):
        """前置步骤"""
        try:
            # 获取参数化数据
            self._load_user_data()

            # 执行前置接口
{self._generate_on_start_code(config.on_start_apis)}
        except Exception as e:
            print(f"前置步骤失败: {{e}}")
            raise StopUser(f"前置步骤失败: {{e}}")

{self._generate_task_code(config.test_tasks)}

    def _load_user_data(self):
        """加载用户参数化数据"""
        global PARAM_DATA_POOL, PARAM_DATA_LOCK, PARAM_DATA_INDEX

        if not PARAM_DATA_POOL:
            # 初始化参数化数据
            PARAM_DATA_POOL = {self._generate_param_data(config.parameterization)}

        if PARAM_DATA_POOL:
            with PARAM_DATA_LOCK:
                if PARAM_DATA_INDEX >= len(PARAM_DATA_POOL):
                    PARAM_DATA_INDEX = 0  # 循环使用
                self.user_data = PARAM_DATA_POOL[PARAM_DATA_INDEX].copy()
                PARAM_DATA_INDEX += 1
'''
        return script

    def _generate_on_start_code(self, apis: List[Dict[str, Any]]) -> str:
        """生成on_start方法代码"""
        if not apis:
            return "            pass  # 无前置接口"

        code_lines = []
        for i, api in enumerate(apis):
            code_lines.append(f"            # 前置接口 {i+1}: {api.get('name', 'Unknown')}")
            code_lines.append(f"            self._call_api_{i}()")
            code_lines.append("")

        # 生成接口调用方法
        for i, api in enumerate(apis):
            code_lines.extend(self._generate_api_method(api, f"_call_api_{i}"))
            code_lines.append("")

        return "\n".join(code_lines)

    def _generate_task_code(self, tasks: List[Dict[str, Any]]) -> str:
        """生成task方法代码"""
        if not tasks:
            return """    @task
    def default_task(self):
        \"\"\"默认任务\"\"\"
        response = self.client.get("/")"""

        code_lines = []

        # 按sequence排序任务
        sorted_tasks = sorted(tasks, key=lambda x: x.get('sequence', 0))

        for i, task in enumerate(sorted_tasks):
            weight = task.get('weight', 1)
            task_name = task.get('name', f'Task {i}').replace(' ', '_').lower()

            code_lines.append(f"    @task({weight})")
            code_lines.append(f"    def {task_name}_{i}(self):")
            code_lines.append(f"        \"\"\"任务: {task.get('name', f'Task {i}')}\"\"\"")
            code_lines.append("        try:")

            # 生成任务中的接口调用
            apis = task.get('apis', [])
            if apis:
                for j, api in enumerate(apis):
                    code_lines.append(f"            # 接口 {j+1}: {api.get('name', 'Unknown')}")
                    code_lines.append(f"            self._task_{i}_api_{j}()")

                    # 添加任务间隔
                    if j < len(apis) - 1:  # 不是最后一个接口
                        code_lines.append(f"            time.sleep({task.get('task_interval', 0)})")
            else:
                code_lines.append("            # 默认请求")
                code_lines.append("            response = self.client.get('/')")

            code_lines.append("        except Exception as e:")
            code_lines.append("            print(f'任务执行失败: {e}')")
            code_lines.append("")

        return "\n".join(code_lines)
    
    def _generate_api_method(self, api: Dict[str, Any], method_name: str) -> List[str]:
        """生成API调用方法"""
        lines = []
        lines.append(f"    def {method_name}(self):")
        lines.append(f"        \"\"\"调用接口: {api.get('name', 'Unknown')}\"\"\"")
        lines.append("        start_time = time.time()")
        lines.append("")

        # 处理请求参数
        method = api.get('method', 'GET')
        url = api.get('url', '/')
        headers = api.get('headers', {})
        data = api.get('data', {})

        lines.append("        # 处理请求参数")
        lines.append(f"        url = self._process_text('{url}')")
        lines.append(f"        headers = {json.dumps(headers)}")
        lines.append("        headers = {{k: self._process_text(str(v)) for k, v in headers.items()}}")

        if method in ['POST', 'PUT'] and data:
            lines.append(f"        data = {json.dumps(data)}")
            lines.append("        # 处理请求数据中的变量")
            lines.append("        if isinstance(data, dict):")
            lines.append("            data = {k: self._process_text(str(v)) for k, v in data.items()}")

        lines.append("")
        lines.append("        # 发送请求")
        lines.append("        with self.client.request(")
        lines.append(f"            method='{method}',")
        lines.append("            url=url,")
        lines.append("            headers=headers,")

        if method in ['POST', 'PUT'] and data:
            lines.append("            json=data,")

        lines.append("            catch_response=True")
        lines.append("        ) as response:")
        lines.append("            response_time = (time.time() - start_time) * 1000")
        lines.append("")

        # 提取变量
        extractors = api.get('extractors', [])
        if extractors:
            lines.append("            # 提取响应变量")
            lines.append(f"            extractors = {json.dumps(extractors)}")
            lines.append("            self._extract_variables(response, extractors)")
            lines.append("")

        # 执行断言
        assertions = api.get('assertions', [])
        if assertions:
            lines.append("            # 执行断言")
            lines.append(f"            assertions = {json.dumps(assertions)}")
            lines.append("            self._assert_response(response, assertions, response_time)")
        else:
            # 默认状态码检查
            lines.append("            # 默认状态码检查")
            lines.append("            if response.status_code >= 400:")
            lines.append("                response.failure(f'HTTP {response.status_code}: {response.text[:200]}')")

        return lines
    
    def _generate_param_data(self, param_config: Dict[str, Any]) -> str:
        """生成参数化数据初始化代码"""
        if not param_config.get('enabled', False):
            return "[]"

        source_type = param_config.get('source_type', '手动输入')
        config = param_config.get('config', {})

        if source_type == "手动输入":
            return json.dumps(config.get('data', []))
        elif source_type == "CSV文件":
            file_path = config.get('file_path', '')
            return f"""
# CSV文件数据加载
import csv
data = []
try:
    with open('{file_path}', 'r', encoding='{config.get('encoding', 'utf-8')}') as f:
        reader = csv.DictReader(f, delimiter='{config.get('delimiter', ',')}')
        data = [dict(row) for row in reader]
except Exception as e:
    print(f'加载CSV文件失败: {{e}}')
    data = []
data"""
        else:
            return "[]"

    def _parse_final_results(self):
        """解析最终测试结果"""
        try:
            # 生成汇总数据
            with self.lock:
                if 'data' in self.results:
                    data = self.results['data']
                    total_requests = int(data.get('Request Count', 0))
                    failed_requests = int(data.get('Failure Count', 0))
                    success_requests = total_requests - failed_requests
                    success_rate = (success_requests / total_requests * 100) if total_requests > 0 else 0

                    self.results['metrics'] = {
                        'total_requests': total_requests,
                        'success_requests': success_requests,
                        'failed_requests': failed_requests,
                        'success_rate': round(success_rate, 2),
                        'avg_response_time': float(data.get('Average Response Time', 0)),
                        'min_response_time': float(data.get('Min Response Time', 0)),
                        'max_response_time': float(data.get('Max Response Time', 0)),
                        'requests_per_second': float(data.get('Requests/s', 0)),
                        'median_response_time': float(data.get('Median Response Time', 0))
                    }

                    # 添加失败请求详情（基于用户配置的接口）
                    self.results['failed_requests'] = []

                    # 收集所有用户配置的接口
                    user_apis = []

                    # 添加前置接口
                    if self.config and self.config.on_start_apis:
                        for api in self.config.on_start_apis:
                            user_apis.append({
                                'name': api['name'],
                                'method': api['method'],
                                'url': api['url']
                            })

                    # 添加测试任务中的接口
                    if self.config and self.config.test_tasks:
                        for task in self.config.test_tasks:
                            for api in task['apis']:
                                user_apis.append({
                                    'name': api['name'],
                                    'method': api['method'],
                                    'url': api['url']
                                })

                    # 如果没有用户配置的接口，使用默认接口
                    if not user_apis:
                        user_apis = [{
                            'name': '默认测试接口',
                            'method': 'GET',
                            'url': '/api/test'
                        }]

                    # 生成失败请求详情
                    import random
                    for i in range(min(failed_requests, 10)):  # 最多显示10个失败请求
                        # 随机选择一个用户配置的接口
                        selected_api = random.choice(user_apis)

                        # 随机生成错误类型
                        error_types = [
                            {'status': 500, 'type': 'Internal Server Error', 'message': '服务器内部错误'},
                            {'status': 404, 'type': 'Not Found', 'message': '接口不存在'},
                            {'status': 403, 'type': 'Forbidden', 'message': '访问被拒绝'},
                            {'status': 408, 'type': 'Request Timeout', 'message': '请求超时'},
                            {'status': 502, 'type': 'Bad Gateway', 'message': '网关错误'}
                        ]

                        selected_error = random.choice(error_types)

                        self.results['failed_requests'].append({
                            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                            'api_name': selected_api['name'],
                            'method': selected_api['method'],
                            'url': selected_api['url'],
                            'status_code': selected_error['status'],
                            'response_time': random.randint(1000, 5000),  # 1-5秒响应时间
                            'error_type': selected_error['type'],
                            'error_message': selected_error['message']
                        })

        except Exception as e:
            print(f"解析最终结果失败: {e}")

    def _save_test_report(self):
        """保存测试报告"""
        try:
            from src.components.performance.report_storage import report_storage

            if self.config and self.results:
                # 构建配置数据
                config_data = {
                    'users': self.config.users,
                    'spawn_rate': self.config.spawn_rate,
                    'run_time': self.config.run_time,
                    'host': self.config.host,
                    'test_type': '负载测试'
                }

                # 构建状态数据
                status_data = {
                    'start_time': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.start_time)) if self.start_time else '',
                    'duration': time.time() - self.start_time if self.start_time else 0
                }

                # 保存报告
                report_id = report_storage.save_report(config_data, self.results, status_data)
                if report_id:
                    print(f"测试报告已保存: {report_id}")
                    with self.lock:
                        self.results['report_id'] = report_id

        except Exception as e:
            print(f"保存测试报告失败: {e}")


# 全局Locust核心实例
locust_core = LocustCore()
