"""
性能测试报告存储系统
"""

import json
import os
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
import uuid


@dataclass
class TestReport:
    """测试报告数据结构"""
    report_id: str
    test_name: str
    test_type: str
    start_time: str
    end_time: str
    duration: float
    config: Dict[str, Any]
    metrics: Dict[str, Any]
    failed_requests: List[Dict[str, Any]]
    created_at: str


class ReportStorage:
    """报告存储管理器"""
    
    def __init__(self, storage_dir: str = "reports"):
        self.storage_dir = storage_dir
        self.reports_file = os.path.join(storage_dir, "test_reports.json")
        self._ensure_storage_dir()
    
    def _ensure_storage_dir(self):
        """确保存储目录存在"""
        if not os.path.exists(self.storage_dir):
            os.makedirs(self.storage_dir)
    
    def save_report(self, config: Dict[str, Any], results: Dict[str, Any], 
                   test_status: Dict[str, Any]) -> str:
        """保存测试报告"""
        try:
            report_id = str(uuid.uuid4())
            
            # 构建完整的metrics数据，包含api_results
            complete_metrics = results.get('metrics', {}).copy()

            # 确保api_results被包含在metrics中
            if 'api_results' in results:
                complete_metrics['api_results'] = results['api_results']

            # 也包含其他重要数据
            if 'data' in results:
                complete_metrics.update(results['data'])

            # 构建报告数据
            report = TestReport(
                report_id=report_id,
                test_name=f"性能测试_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                test_type=config.get('test_type', '负载测试'),
                start_time=test_status.get('start_time', ''),
                end_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                duration=test_status.get('duration', 0),
                config=config,
                metrics=complete_metrics,
                failed_requests=results.get('failed_requests', []),
                created_at=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            )
            
            # 读取现有报告
            reports = self._load_reports()
            
            # 添加新报告
            reports.append(asdict(report))
            
            # 保存到文件
            with open(self.reports_file, 'w', encoding='utf-8') as f:
                json.dump(reports, f, ensure_ascii=False, indent=2)
            
            return report_id
            
        except Exception as e:
            print(f"保存报告失败: {e}")
            return None
    
    def get_report(self, report_id: str) -> Optional[TestReport]:
        """获取指定报告"""
        try:
            reports = self._load_reports()
            
            for report_data in reports:
                if report_data['report_id'] == report_id:
                    return TestReport(**report_data)
            
            return None
            
        except Exception as e:
            print(f"获取报告失败: {e}")
            return None
    
    def list_reports(self) -> List[TestReport]:
        """获取所有报告列表"""
        try:
            reports_data = self._load_reports()
            reports = [TestReport(**data) for data in reports_data]
            
            # 按创建时间倒序排列
            reports.sort(key=lambda x: x.created_at, reverse=True)
            
            return reports
            
        except Exception as e:
            print(f"获取报告列表失败: {e}")
            return []
    
    def delete_report(self, report_id: str) -> bool:
        """删除指定报告"""
        try:
            reports = self._load_reports()
            
            # 过滤掉要删除的报告
            reports = [r for r in reports if r['report_id'] != report_id]
            
            # 保存更新后的报告列表
            with open(self.reports_file, 'w', encoding='utf-8') as f:
                json.dump(reports, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            print(f"删除报告失败: {e}")
            return False
    
    def _load_reports(self) -> List[Dict[str, Any]]:
        """加载报告数据"""
        try:
            if os.path.exists(self.reports_file):
                with open(self.reports_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return []
                
        except Exception as e:
            print(f"加载报告数据失败: {e}")
            return []
    
    def get_latest_report(self) -> Optional[TestReport]:
        """获取最新的报告"""
        reports = self.list_reports()
        return reports[0] if reports else None
    
    def clear_old_reports(self, days: int = 30):
        """清理旧报告"""
        try:
            reports = self._load_reports()
            current_time = time.time()
            cutoff_time = current_time - (days * 24 * 3600)
            
            # 过滤掉过期的报告
            valid_reports = []
            for report in reports:
                created_at = datetime.strptime(report['created_at'], '%Y-%m-%d %H:%M:%S')
                if created_at.timestamp() > cutoff_time:
                    valid_reports.append(report)
            
            # 保存过滤后的报告
            with open(self.reports_file, 'w', encoding='utf-8') as f:
                json.dump(valid_reports, f, ensure_ascii=False, indent=2)
            
            removed_count = len(reports) - len(valid_reports)
            return removed_count
            
        except Exception as e:
            print(f"清理旧报告失败: {e}")
            return 0


# 全局报告存储实例
report_storage = ReportStorage()
