"""
变量池和提取器
支持正则表达式、JSON、XPath、CSS选择器等多种提取方式
"""

import re
import json
import threading
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from enum import Enum
import xml.etree.ElementTree as ET


class ExtractorType(Enum):
    """提取器类型"""
    REGEX = "regex"
    JSON_PATH = "json_path"
    XPATH = "xpath"
    CSS_SELECTOR = "css_selector"
    HEADER = "header"


@dataclass
class Extractor:
    """提取器配置"""
    name: str                    # 变量名
    extractor_type: ExtractorType
    expression: str              # 提取表达式
    match_group: int = 1         # 正则匹配组（仅regex使用）
    default_value: str = ""      # 默认值
    scope: str = "user"          # 作用域：user/global


class VariablePool:
    """变量池"""
    
    def __init__(self):
        self.user_variables: Dict[str, Dict[str, Any]] = {}  # 用户级变量
        self.global_variables: Dict[str, Any] = {}           # 全局变量
        self.lock = threading.Lock()
    
    def set_variable(self, user_id: str, name: str, value: Any, scope: str = "user"):
        """设置变量"""
        with self.lock:
            if scope == "global":
                self.global_variables[name] = value
            else:
                if user_id not in self.user_variables:
                    self.user_variables[user_id] = {}
                self.user_variables[user_id][name] = value
    
    def get_variable(self, user_id: str, name: str, default: Any = None) -> Any:
        """获取变量"""
        with self.lock:
            # 先查找用户级变量
            if user_id in self.user_variables and name in self.user_variables[user_id]:
                return self.user_variables[user_id][name]
            
            # 再查找全局变量
            if name in self.global_variables:
                return self.global_variables[name]
            
            return default
    
    def get_all_variables(self, user_id: str) -> Dict[str, Any]:
        """获取用户的所有变量"""
        with self.lock:
            variables = self.global_variables.copy()
            if user_id in self.user_variables:
                variables.update(self.user_variables[user_id])
            return variables
    
    def clear_user_variables(self, user_id: str):
        """清除用户变量"""
        with self.lock:
            if user_id in self.user_variables:
                del self.user_variables[user_id]
    
    def clear_global_variables(self):
        """清除全局变量"""
        with self.lock:
            self.global_variables.clear()


class ResponseExtractor:
    """响应提取器"""
    
    def __init__(self, variable_pool: VariablePool):
        self.variable_pool = variable_pool
    
    def extract_variables(self, user_id: str, response, extractors: List[Extractor]) -> Dict[str, Any]:
        """从响应中提取变量"""
        extracted = {}
        
        for extractor in extractors:
            try:
                value = self._extract_single_variable(response, extractor)
                if value is not None:
                    extracted[extractor.name] = value
                    self.variable_pool.set_variable(
                        user_id, 
                        extractor.name, 
                        value, 
                        extractor.scope
                    )
                else:
                    # 使用默认值
                    if extractor.default_value:
                        extracted[extractor.name] = extractor.default_value
                        self.variable_pool.set_variable(
                            user_id,
                            extractor.name,
                            extractor.default_value,
                            extractor.scope
                        )
                        
            except Exception as e:
                print(f"提取变量失败 {extractor.name}: {e}")
                if extractor.default_value:
                    extracted[extractor.name] = extractor.default_value
                    self.variable_pool.set_variable(
                        user_id,
                        extractor.name,
                        extractor.default_value,
                        extractor.scope
                    )
        
        return extracted
    
    def _extract_single_variable(self, response, extractor: Extractor) -> Optional[Any]:
        """提取单个变量"""
        if extractor.extractor_type == ExtractorType.REGEX:
            return self._extract_by_regex(response, extractor)
        elif extractor.extractor_type == ExtractorType.JSON_PATH:
            return self._extract_by_json_path(response, extractor)
        elif extractor.extractor_type == ExtractorType.XPATH:
            return self._extract_by_xpath(response, extractor)
        elif extractor.extractor_type == ExtractorType.CSS_SELECTOR:
            return self._extract_by_css_selector(response, extractor)
        elif extractor.extractor_type == ExtractorType.HEADER:
            return self._extract_by_header(response, extractor)
        else:
            return None
    
    def _extract_by_regex(self, response, extractor: Extractor) -> Optional[str]:
        """正则表达式提取"""
        try:
            text = response.text if hasattr(response, 'text') else str(response)
            pattern = extractor.expression
            matches = re.findall(pattern, text)
            
            if matches:
                if isinstance(matches[0], tuple):
                    # 多个捕获组
                    if extractor.match_group <= len(matches[0]):
                        return matches[0][extractor.match_group - 1]
                else:
                    # 单个捕获组
                    if extractor.match_group <= len(matches):
                        return matches[extractor.match_group - 1]
            
            return None
            
        except Exception as e:
            print(f"正则提取失败: {e}")
            return None
    
    def _extract_by_json_path(self, response, extractor: Extractor) -> Optional[Any]:
        """JSON路径提取"""
        try:
            if hasattr(response, 'json'):
                data = response.json()
            else:
                data = json.loads(str(response))
            
            # 简单的JSON路径实现
            path = extractor.expression
            return self._get_json_value(data, path)
            
        except Exception as e:
            print(f"JSON路径提取失败: {e}")
            return None
    
    def _get_json_value(self, data: Any, path: str) -> Optional[Any]:
        """获取JSON路径值"""
        try:
            # 支持简单的JSON路径，如: $.data.user.id 或 data.user.id
            if path.startswith('$.'):
                path = path[2:]
            
            current = data
            for key in path.split('.'):
                if isinstance(current, dict):
                    current = current.get(key)
                elif isinstance(current, list) and key.isdigit():
                    index = int(key)
                    current = current[index] if 0 <= index < len(current) else None
                else:
                    return None
                
                if current is None:
                    break
            
            return current
            
        except Exception as e:
            print(f"JSON路径解析失败: {e}")
            return None
    
    def _extract_by_xpath(self, response, extractor: Extractor) -> Optional[str]:
        """XPath提取"""
        try:
            text = response.text if hasattr(response, 'text') else str(response)
            
            # 解析XML/HTML
            root = ET.fromstring(text)
            
            # 简单的XPath实现（仅支持基本路径）
            xpath = extractor.expression
            elements = self._simple_xpath(root, xpath)
            
            if elements:
                return elements[0].text if hasattr(elements[0], 'text') else str(elements[0])
            
            return None
            
        except Exception as e:
            print(f"XPath提取失败: {e}")
            return None
    
    def _simple_xpath(self, root, xpath: str) -> List:
        """简单的XPath实现"""
        try:
            # 这里实现简单的XPath解析
            # 实际项目中建议使用lxml库
            if xpath.startswith('//'):
                tag = xpath[2:]
                return root.findall(f".//{tag}")
            else:
                return root.findall(xpath)
                
        except Exception as e:
            print(f"XPath解析失败: {e}")
            return []
    
    def _extract_by_css_selector(self, response, extractor: Extractor) -> Optional[str]:
        """CSS选择器提取"""
        try:
            # CSS选择器需要使用BeautifulSoup或类似库
            # 这里提供基本实现
            text = response.text if hasattr(response, 'text') else str(response)
            
            # 简单的CSS选择器实现（仅支持基本选择器）
            selector = extractor.expression
            
            # 这里可以集成BeautifulSoup
            # from bs4 import BeautifulSoup
            # soup = BeautifulSoup(text, 'html.parser')
            # elements = soup.select(selector)
            # return elements[0].get_text() if elements else None
            
            print("CSS选择器需要安装BeautifulSoup库")
            return None
            
        except Exception as e:
            print(f"CSS选择器提取失败: {e}")
            return None
    
    def _extract_by_header(self, response, extractor: Extractor) -> Optional[str]:
        """响应头提取"""
        try:
            if hasattr(response, 'headers'):
                return response.headers.get(extractor.expression)
            return None
            
        except Exception as e:
            print(f"响应头提取失败: {e}")
            return None


class ExtractorBuilder:
    """提取器构建器"""
    
    @staticmethod
    def create_regex_extractor(name: str, pattern: str, match_group: int = 1, 
                              default_value: str = "", scope: str = "user") -> Extractor:
        """创建正则提取器"""
        return Extractor(
            name=name,
            extractor_type=ExtractorType.REGEX,
            expression=pattern,
            match_group=match_group,
            default_value=default_value,
            scope=scope
        )
    
    @staticmethod
    def create_json_extractor(name: str, json_path: str, 
                             default_value: str = "", scope: str = "user") -> Extractor:
        """创建JSON提取器"""
        return Extractor(
            name=name,
            extractor_type=ExtractorType.JSON_PATH,
            expression=json_path,
            default_value=default_value,
            scope=scope
        )
    
    @staticmethod
    def create_header_extractor(name: str, header_name: str,
                               default_value: str = "", scope: str = "user") -> Extractor:
        """创建响应头提取器"""
        return Extractor(
            name=name,
            extractor_type=ExtractorType.HEADER,
            expression=header_name,
            default_value=default_value,
            scope=scope
        )


# 全局变量池和提取器实例
variable_pool = VariablePool()
response_extractor = ResponseExtractor(variable_pool)
