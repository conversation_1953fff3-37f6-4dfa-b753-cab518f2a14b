"""
参数化管理器
支持数据库查询、CSV文件、手动输入等多种参数化方式
"""

import csv
import json
import sqlite3
import threading
from typing import Dict, Any, List, Optional, Iterator
from dataclasses import dataclass
from enum import Enum
import os


class DataSourceType(Enum):
    """数据源类型"""
    MANUAL = "manual"
    CSV_FILE = "csv_file"
    DATABASE = "database"
    JSON_FILE = "json_file"


class DataMode(Enum):
    """数据使用模式"""
    REUSE = "reuse"      # 数据复用
    UNIQUE = "unique"    # 数据独占
    CYCLE = "cycle"      # 数据循环


@dataclass
class DataSource:
    """数据源配置"""
    name: str
    source_type: DataSourceType
    config: Dict[str, Any]
    mode: DataMode = DataMode.CYCLE
    preprocess: List[str] = None  # 数据预处理函数


class ParameterizationManager:
    """参数化管理器"""
    
    def __init__(self):
        self.data_sources: Dict[str, DataSource] = {}
        self.data_pools: Dict[str, List[Dict[str, Any]]] = {}
        self.used_data: Dict[str, set] = {}  # 记录已使用的数据
        self.data_iterators: Dict[str, Iterator] = {}
        self.lock = threading.Lock()
    
    def add_data_source(self, data_source: DataSource) -> bool:
        """添加数据源"""
        try:
            # 加载数据
            data = self._load_data(data_source)
            if not data:
                return False
            
            # 数据预处理
            if data_source.preprocess:
                data = self._preprocess_data(data, data_source.preprocess)
            
            with self.lock:
                self.data_sources[data_source.name] = data_source
                self.data_pools[data_source.name] = data
                self.used_data[data_source.name] = set()
                self.data_iterators[data_source.name] = iter(data)
            
            return True
            
        except Exception as e:
            print(f"添加数据源失败: {e}")
            return False
    
    def get_user_data(self, source_name: str, user_id: str) -> Optional[Dict[str, Any]]:
        """获取用户参数化数据"""
        try:
            with self.lock:
                if source_name not in self.data_pools:
                    return None
                
                data_source = self.data_sources[source_name]
                data_pool = self.data_pools[source_name]
                
                if data_source.mode == DataMode.REUSE:
                    # 数据复用模式：随机选择
                    import random
                    return random.choice(data_pool) if data_pool else None
                
                elif data_source.mode == DataMode.UNIQUE:
                    # 数据独占模式：每个用户使用唯一数据
                    for i, data in enumerate(data_pool):
                        data_key = f"{source_name}_{i}"
                        if data_key not in self.used_data[source_name]:
                            self.used_data[source_name].add(data_key)
                            return data
                    return None  # 没有可用数据
                
                elif data_source.mode == DataMode.CYCLE:
                    # 数据循环模式：循环使用数据
                    try:
                        return next(self.data_iterators[source_name])
                    except StopIteration:
                        # 重新开始循环
                        self.data_iterators[source_name] = iter(data_pool)
                        return next(self.data_iterators[source_name])
                
        except Exception as e:
            print(f"获取用户数据失败: {e}")
            return None
    
    def release_user_data(self, source_name: str, user_id: str, data: Dict[str, Any]):
        """释放用户数据（仅在独占模式下有效）"""
        try:
            with self.lock:
                if source_name not in self.data_sources:
                    return
                
                data_source = self.data_sources[source_name]
                if data_source.mode == DataMode.UNIQUE:
                    # 在独占模式下，标记数据为可用
                    data_pool = self.data_pools[source_name]
                    for i, pool_data in enumerate(data_pool):
                        if pool_data == data:
                            data_key = f"{source_name}_{i}"
                            self.used_data[source_name].discard(data_key)
                            break
                            
        except Exception as e:
            print(f"释放用户数据失败: {e}")
    
    def _load_data(self, data_source: DataSource) -> List[Dict[str, Any]]:
        """加载数据"""
        if data_source.source_type == DataSourceType.MANUAL:
            return self._load_manual_data(data_source.config)
        elif data_source.source_type == DataSourceType.CSV_FILE:
            return self._load_csv_data(data_source.config)
        elif data_source.source_type == DataSourceType.DATABASE:
            return self._load_database_data(data_source.config)
        elif data_source.source_type == DataSourceType.JSON_FILE:
            return self._load_json_data(data_source.config)
        else:
            return []
    
    def _load_manual_data(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """加载手动输入数据"""
        return config.get("data", [])
    
    def _load_csv_data(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """加载CSV文件数据"""
        file_path = config.get("file_path")
        encoding = config.get("encoding", "utf-8")
        delimiter = config.get("delimiter", ",")
        
        if not file_path or not os.path.exists(file_path):
            return []
        
        data = []
        with open(file_path, 'r', encoding=encoding) as f:
            reader = csv.DictReader(f, delimiter=delimiter)
            for row in reader:
                data.append(dict(row))
        
        return data
    
    def _load_database_data(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """加载数据库数据"""
        db_type = config.get("db_type", "sqlite")
        connection_string = config.get("connection_string")
        query = config.get("query")
        
        if not connection_string or not query:
            return []
        
        data = []
        try:
            if db_type == "sqlite":
                conn = sqlite3.connect(connection_string)
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute(query)
                
                for row in cursor.fetchall():
                    data.append(dict(row))
                
                conn.close()
            
            # 可以扩展支持其他数据库类型
            
        except Exception as e:
            print(f"加载数据库数据失败: {e}")
        
        return data
    
    def _load_json_data(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """加载JSON文件数据"""
        file_path = config.get("file_path")
        encoding = config.get("encoding", "utf-8")
        
        if not file_path or not os.path.exists(file_path):
            return []
        
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                data = json.load(f)
                
            if isinstance(data, list):
                return data
            elif isinstance(data, dict):
                return [data]
            else:
                return []
                
        except Exception as e:
            print(f"加载JSON数据失败: {e}")
            return []
    
    def _preprocess_data(self, data: List[Dict[str, Any]], preprocess_functions: List[str]) -> List[Dict[str, Any]]:
        """数据预处理"""
        try:
            # 这里可以实现各种数据预处理函数
            # 例如：密码加密、数据格式转换等
            processed_data = []
            
            for item in data:
                processed_item = item.copy()
                
                for func_name in preprocess_functions:
                    if func_name == "encrypt_password":
                        if "password" in processed_item:
                            # 示例：简单的密码加密
                            import hashlib
                            processed_item["password"] = hashlib.md5(
                                processed_item["password"].encode()
                            ).hexdigest()
                    
                    elif func_name == "add_timestamp":
                        import time
                        processed_item["timestamp"] = int(time.time())
                    
                    # 可以添加更多预处理函数
                
                processed_data.append(processed_item)
            
            return processed_data
            
        except Exception as e:
            print(f"数据预处理失败: {e}")
            return data
    
    def get_data_summary(self) -> Dict[str, Any]:
        """获取数据摘要"""
        with self.lock:
            summary = {}
            for name, data_pool in self.data_pools.items():
                data_source = self.data_sources[name]
                used_count = len(self.used_data[name])
                
                summary[name] = {
                    "source_type": data_source.source_type.value,
                    "mode": data_source.mode.value,
                    "total_count": len(data_pool),
                    "used_count": used_count,
                    "available_count": len(data_pool) - used_count if data_source.mode == DataMode.UNIQUE else len(data_pool)
                }
            
            return summary
    
    def create_sample_csv(self, file_path: str, count: int = 100) -> bool:
        """创建示例CSV文件"""
        try:
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # 写入表头
                writer.writerow(['username', 'password', 'email', 'phone', 'user_type'])
                
                # 写入数据
                for i in range(count):
                    writer.writerow([
                        f"user{i+1:03d}",
                        f"password{i+1:03d}",
                        f"user{i+1:03d}@test.com",
                        f"1380000{i+1:04d}",
                        "normal" if i % 3 != 0 else "vip"
                    ])
            
            return True
            
        except Exception as e:
            print(f"创建示例CSV失败: {e}")
            return False


# 全局参数化管理器实例
param_manager = ParameterizationManager()
