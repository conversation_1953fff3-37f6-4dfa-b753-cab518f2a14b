#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/21
# <AUTHOR> Kai

"""
Mock服务业务逻辑
"""

import json
import re
import time
from typing import Dict, Any, Optional, List
from .models import MockConfig, MockRequest, MockResponse, MockApiCreate, MockApiUpdate
from .database import mock_db

class MockService:
    """Mock服务类"""

    def __init__(self):
        self.db = mock_db
    
    def process_mock_request(self, request: MockRequest) -> MockResponse:
        """处理Mock请求"""
        # 获取匹配的Mock配置
        configs = self.db.get_mock_configs(request.test_case)
        
        if not configs:
            return MockResponse(
                status_code=404,
                body={"error": f"未找到测试案例: {request.test_case}"}
            )
        
        # 查找匹配的API配置
        matched_config = None
        for config in configs:
            if config.api_url == request.api_url:
                # 检查条件匹配
                if self._check_condition(config, request.request_data):
                    matched_config = config
                    break
        
        if not matched_config:
            return MockResponse(
                status_code=404,
                body={"error": f"未找到匹配的API配置: {request.api_url}"}
            )
        
        # 生成Mock响应
        return self._generate_response(matched_config, request.request_data)
    
    def _check_condition(self, config: MockConfig, request_data: Optional[Dict[str, Any]]) -> bool:
        """检查条件是否匹配"""
        if not config.condition or config.condition.strip() == "":
            return True
        
        if not request_data:
            return False
        
        try:
            # 简单的条件匹配逻辑
            # 支持格式: field=value 或 field!=value
            condition = config.condition.strip()
            
            if "=" in condition:
                if "!=" in condition:
                    field, value = condition.split("!=", 1)
                    field = field.strip()
                    value = value.strip().strip('"\'')
                    return str(request_data.get(field, "")) != value
                else:
                    field, value = condition.split("=", 1)
                    field = field.strip()
                    value = value.strip().strip('"\'')
                    return str(request_data.get(field, "")) == value
            
            return True
        except Exception:
            return True
    
    def _generate_response(self, config: MockConfig, request_data: Optional[Dict[str, Any]]) -> MockResponse:
        """生成Mock响应"""
        try:
            if not config.mock_body:
                return MockResponse(
                    status_code=200,
                    body={"message": "Mock响应"}
                )
            
            # 解析Mock响应体
            if isinstance(config.mock_body, str):
                try:
                    response_body = json.loads(config.mock_body)
                except json.JSONDecodeError:
                    response_body = {"data": config.mock_body}
            else:
                response_body = config.mock_body
            
            # 应用Mock规则（如果有）
            if config.mock_rule:
                response_body = self._apply_mock_rule(response_body, config.mock_rule, request_data)
            
            return MockResponse(
                status_code=200,
                body=response_body
            )
        except Exception as e:
            return MockResponse(
                status_code=500,
                body={"error": f"生成Mock响应失败: {str(e)}"}
            )
    
    def _apply_mock_rule(self, response_body: Dict[str, Any], mock_rule: str, request_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """应用Mock规则"""
        try:
            # 简单的规则替换逻辑
            # 支持格式: ${field} 替换为请求中的字段值
            if request_data:
                rule_str = json.dumps(response_body)
                
                # 查找所有 ${field} 模式
                pattern = r'\$\{([^}]+)\}'
                matches = re.findall(pattern, rule_str)
                
                for match in matches:
                    field_name = match.strip()
                    if field_name in request_data:
                        rule_str = rule_str.replace(f"${{{field_name}}}", str(request_data[field_name]))
                
                response_body = json.loads(rule_str)
            
            return response_body
        except Exception:
            return response_body
    
    def add_mock_config(self, config: MockApiCreate) -> bool:
        """添加Mock配置"""
        mock_config = MockConfig(
            test_case=config.test_case,
            api_url=config.api_url,
            description=config.description,
            mock_rule=config.mock_rule,
            mock_body=config.mock_body,
            condition=config.condition
        )
        return self.db.add_mock_config(mock_config)

    def get_mock_configs(self, test_case: str) -> List[MockConfig]:
        """获取Mock配置列表"""
        return self.db.get_mock_configs(test_case)

    def update_mock_config(self, config: MockApiUpdate) -> bool:
        """更新Mock配置"""
        mock_config = MockConfig(
            id=config.id,
            test_case="",  # 更新时不需要修改test_case
            api_url="",    # 更新时不需要修改api_url
            description=config.description,
            mock_rule=config.mock_rule,
            mock_body=config.mock_body,
            condition=config.condition
        )
        return self.db.update_mock_config(mock_config)

    def delete_mock_config(self, config_id: int) -> bool:
        """删除Mock配置"""
        return self.db.delete_mock_config(config_id)

    def list_test_cases(self) -> List[str]:
        """列出所有测试案例"""
        return self.db.list_all_test_cases()

    def log_request(self, test_case: str, api_url: str, method: str,
                   request_data: Dict[str, Any], response_data: Dict[str, Any],
                   status_code: int = 200) -> bool:
        """记录请求日志"""
        try:
            request_str = json.dumps(request_data) if request_data else ""
            response_str = json.dumps(response_data) if response_data else ""
            return self.db.log_mock_request(
                test_case, api_url, method, request_str, response_str, status_code
            )
        except Exception as e:
            print(f"记录日志失败: {e}")
            return False

    def get_logs(self, test_case: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取请求日志"""
        return self.db.get_mock_logs(test_case, limit)

# 全局服务实例
mock_service = MockService()
