#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/21
# <AUTHOR> Kai

"""
Mock服务数据访问对象
"""

import sqlite3
import json
import os
from typing import List, Optional, Dict, Any
from datetime import datetime

class MockDAO:
    """Mock数据访问对象"""

    def __init__(self, db_path: str = "data/mock.db"):
        self.db_path = db_path
        self._init_db()
    
    def _init_db(self):
        """初始化数据库"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建Mock配置表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS mock_configs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    test_case TEXT NOT NULL,
                    api_url TEXT NOT NULL,
                    description TEXT,
                    mock_rule TEXT,
                    mock_body TEXT,
                    condition_rule TEXT,
                    enabled INTEGER DEFAULT 1,
                    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(test_case, api_url)
                )
            ''')

            # 检查并添加enabled字段（兼容旧数据库）
            try:
                cursor.execute("ALTER TABLE mock_configs ADD COLUMN enabled INTEGER DEFAULT 1")
            except sqlite3.OperationalError:
                # 字段已存在，忽略错误
                pass
            
            # 创建Mock日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS mock_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    test_case TEXT NOT NULL,
                    api_url TEXT NOT NULL,
                    method TEXT NOT NULL DEFAULT 'POST',
                    request_data TEXT,
                    response_data TEXT,
                    status_code INTEGER DEFAULT 200,
                    request_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_case ON mock_configs (test_case)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_api_url ON mock_configs (api_url)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_log_test_case ON mock_logs (test_case)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_log_time ON mock_logs (request_time)')
            
            conn.commit()
    
    def insert_mock_config(self, test_case: str, api_url: str, description: str = None,
                          mock_rule: str = None, mock_body: str = None, condition: str = None, enabled: bool = True) -> bool:
        """插入Mock配置"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO mock_configs
                    (test_case, api_url, description, mock_rule, mock_body, condition_rule, enabled, update_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (test_case, api_url, description, mock_rule, mock_body, condition, 1 if enabled else 0))
                conn.commit()
                return True
        except Exception as e:
            print(f"插入Mock配置失败: {e}")
            return False
    
    def get_mock_config(self, test_case: str, api_url: str) -> Optional[Dict[str, Any]]:
        """获取单个Mock配置"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, test_case, api_url, description, mock_rule, mock_body,
                           condition_rule, enabled, create_time, update_time
                    FROM mock_configs
                    WHERE test_case = ? AND api_url = ?
                ''', (test_case, api_url))
                
                row = cursor.fetchone()
                if row:
                    return {
                        'id': row[0],                    # id
                        'test_case': row[1],             # test_case
                        'api_url': row[2],               # api_url
                        'description': row[3],           # description
                        'mock_rule': row[4],             # mock_rule
                        'mock_body': row[5],             # mock_body
                        'condition': row[6],             # condition_rule
                        'enabled': bool(row[7]) if row[7] is not None else True,  # enabled
                        'create_time': row[8],           # create_time
                        'update_time': row[9]            # update_time
                    }
                return None
        except Exception as e:
            print(f"获取Mock配置失败: {e}")
            return None
    
    def get_mock_configs_by_test_case(self, test_case: str) -> List[Dict[str, Any]]:
        """根据测试案例获取Mock配置列表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, test_case, api_url, description, mock_rule, mock_body,
                           condition_rule, enabled, create_time, update_time
                    FROM mock_configs
                    WHERE test_case = ?
                    ORDER BY create_time DESC
                ''', (test_case,))
                
                rows = cursor.fetchall()
                configs = []
                
                for row in rows:
                    # 按照SELECT语句的字段顺序映射
                    configs.append({
                        'id': row[0],                    # id
                        'test_case': row[1],             # test_case
                        'api_url': row[2],               # api_url
                        'description': row[3],           # description
                        'mock_rule': row[4],             # mock_rule
                        'mock_body': row[5],             # mock_body
                        'condition': row[6],             # condition_rule
                        'enabled': bool(row[7]) if row[7] is not None else True,  # enabled
                        'create_time': row[8],           # create_time
                        'update_time': row[9]            # update_time
                    })
                
                return configs
        except Exception as e:
            print(f"获取Mock配置列表失败: {e}")
            return []
    
    def update_mock_config(self, config_id: int, description: str = None,
                          mock_rule: str = None, mock_body: str = None, condition: str = None, enabled: bool = None) -> bool:
        """更新Mock配置"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 构建更新语句
                update_fields = []
                params = []

                if description is not None:
                    update_fields.append("description = ?")
                    params.append(description)

                if mock_rule is not None:
                    update_fields.append("mock_rule = ?")
                    params.append(mock_rule)

                if mock_body is not None:
                    update_fields.append("mock_body = ?")
                    params.append(mock_body)

                if condition is not None:
                    update_fields.append("condition_rule = ?")
                    params.append(condition)

                if enabled is not None:
                    update_fields.append("enabled = ?")
                    params.append(1 if enabled else 0)

                if not update_fields:
                    return True

                update_fields.append("update_time = CURRENT_TIMESTAMP")
                params.append(config_id)

                sql = f"UPDATE mock_configs SET {', '.join(update_fields)} WHERE id = ?"
                cursor.execute(sql, params)
                conn.commit()
                return True
        except Exception as e:
            print(f"更新Mock配置失败: {e}")
            return False

    def toggle_mock_config_status(self, config_id: int) -> bool:
        """切换Mock配置的启用状态"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 获取当前状态
                cursor.execute("SELECT enabled FROM mock_configs WHERE id = ?", (config_id,))
                row = cursor.fetchone()
                if not row:
                    return False

                current_enabled = bool(row[0])
                new_enabled = not current_enabled

                # 更新状态
                cursor.execute('''
                    UPDATE mock_configs
                    SET enabled = ?, update_time = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (1 if new_enabled else 0, config_id))
                conn.commit()
                return True
        except Exception as e:
            print(f"切换Mock配置状态失败: {e}")
            return False
    
    def delete_mock_config(self, config_id: int) -> bool:
        """删除Mock配置"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM mock_configs WHERE id = ?', (config_id,))
                conn.commit()
                return True
        except Exception as e:
            print(f"删除Mock配置失败: {e}")
            return False
    
    def list_test_cases(self) -> List[str]:
        """列出所有测试案例"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT DISTINCT test_case FROM mock_configs ORDER BY test_case')
                rows = cursor.fetchall()
                return [row[0] for row in rows]
        except Exception as e:
            print(f"获取测试案例列表失败: {e}")
            return []
    
    def log_mock_request(self, test_case: str, api_url: str, request_data: str, 
                        response_data: str, status_code: int = 200) -> bool:
        """记录Mock请求日志"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO mock_logs 
                    (test_case, api_url, request_data, response_data, status_code)
                    VALUES (?, ?, ?, ?, ?)
                ''', (test_case, api_url, request_data, response_data, status_code))
                conn.commit()
                return True
        except Exception as e:
            print(f"记录Mock日志失败: {e}")
            return False
    
    def insert_mock_log(self, api_url: str, method: str = "POST",
                       request_data: str = "", response_data: str = "", status_code: int = 200, test_case: str = "") -> bool:
        """插入Mock日志"""
        try:
            # 获取香港时间
            from datetime import datetime, timezone, timedelta
            hk_tz = timezone(timedelta(hours=8))
            hk_time = datetime.now(hk_tz).strftime("%Y-%m-%d %H:%M:%S")

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO mock_logs
                    (test_case, api_url, method, request_data, response_data, status_code, request_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (test_case, api_url, method, request_data, response_data, status_code, hk_time))
                conn.commit()
                return True
        except Exception as e:
            print(f"插入Mock日志失败: {e}")
            return False

    def get_mock_logs(self, test_case: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取Mock请求日志"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if test_case:
                    cursor.execute('''
                        SELECT * FROM mock_logs 
                        WHERE test_case = ?
                        ORDER BY request_time DESC
                        LIMIT ?
                    ''', (test_case, limit))
                else:
                    cursor.execute('''
                        SELECT * FROM mock_logs 
                        ORDER BY request_time DESC
                        LIMIT ?
                    ''', (limit,))
                
                rows = cursor.fetchall()
                logs = []
                
                for row in rows:
                    logs.append({
                        'id': row[0],
                        'test_case': row[1],
                        'api_url': row[2],
                        'method': row[3] if len(row) > 6 else "POST",  # 兼容旧数据
                        'request_data': row[4] if len(row) > 6 else row[3],
                        'response_data': row[5] if len(row) > 6 else row[4],
                        'status_code': row[6] if len(row) > 6 else row[5],
                        'request_time': row[7] if len(row) > 6 else row[6]
                    })
                
                return logs
        except Exception as e:
            print(f"获取Mock日志失败: {e}")
            return []

# 全局DAO实例
mock_dao = MockDAO()
