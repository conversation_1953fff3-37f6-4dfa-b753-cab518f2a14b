#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/21
# <AUTHOR> Kai

"""
Mock服务数据模型
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime

class MockConfig(BaseModel):
    """Mock配置模型"""
    id: Optional[int] = None
    test_case: str = Field(..., description="测试案例名称")
    api_url: str = Field(..., description="API路径")
    description: Optional[str] = Field(None, description="配置描述")
    mock_rule: Optional[str] = Field(None, description="Mock规则")
    mock_body: Optional[str] = Field(None, description="Mock响应体")
    condition: Optional[str] = Field(None, description="匹配条件")
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None

class MockApiCreate(BaseModel):
    """创建Mock API的请求模型"""
    test_case: str = Field(..., description="测试案例名称")
    api_url: str = Field(..., description="API路径")
    description: Optional[str] = Field(None, description="配置描述")
    mock_rule: Optional[str] = Field(None, description="Mock规则")
    mock_body: Optional[str] = Field(None, description="Mock响应体")
    condition: Optional[str] = Field(None, description="匹配条件")

class MockApiUpdate(BaseModel):
    """更新Mock API的请求模型"""
    id: Optional[int] = None
    description: Optional[str] = Field(None, description="配置描述")
    mock_rule: Optional[str] = Field(None, description="Mock规则")
    mock_body: Optional[str] = Field(None, description="Mock响应体")
    condition: Optional[str] = Field(None, description="匹配条件")

class MockRequest(BaseModel):
    """Mock请求模型"""
    test_case: str = Field(..., description="测试案例名称")
    api_url: str = Field(..., description="API路径")
    request_data: Optional[Dict[str, Any]] = Field(None, description="请求数据")

class MockResponse(BaseModel):
    """Mock响应模型"""
    status_code: int = Field(200, description="HTTP状态码")
    headers: Optional[Dict[str, str]] = Field(None, description="响应头")
    body: Optional[Dict[str, Any]] = Field(None, description="响应体")

class MockConfigList(BaseModel):
    """Mock配置列表模型"""
    test_case: str = Field(..., description="测试案例名称")
    mock_config_list: List[MockConfig] = Field(..., description="Mock配置列表")

class ApiResponse(BaseModel):
    """API响应模型"""
    code: int = Field(..., description="响应码")
    message: str = Field(..., description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")

class PaginationParams(BaseModel):
    """分页参数模型"""
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(10, ge=1, le=100, description="每页大小")

class MockLogEntry(BaseModel):
    """Mock日志条目模型"""
    id: Optional[int] = None
    test_case: str = Field(..., description="测试案例")
    api_url: str = Field(..., description="API路径")
    method: str = Field(..., description="HTTP方法")
    request_data: Optional[str] = Field(None, description="请求数据")
    response_data: Optional[str] = Field(None, description="响应数据")
    status_code: int = Field(200, description="状态码")
    request_time: Optional[datetime] = None
