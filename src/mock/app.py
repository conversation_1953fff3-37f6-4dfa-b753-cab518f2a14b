#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/21
# <AUTHOR> Kai

"""
FastAPI Mock服务应用
提供Mock API的HTTP接口
"""

import asyncio
import json
import time
from typing import Dict, Any, Optional
from fastapi import FastAPI, Request, Response, HTTPException, Depends, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import logging

from src.mock.database import init_db
from src.mock.models import (
    MockApiCreate, MockApiUpdate, ApiResponse, PaginationParams
)
from src.mock.service import mock_service

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="EpayTools Mock Server",
    description="Mock API服务器，用于模拟各种API响应",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
request_logs = []
MAX_LOGS = 1000


@app.on_event("startup")
async def startup_event():
    """应用启动时初始化数据库"""
    init_db()
    logger.info("Mock服务器启动完成")


@app.get("/", response_model=ApiResponse)
async def root():
    """根路径"""
    return ApiResponse(
        code=200,
        message="EpayTools Mock Server",
        data={
            "version": "1.0.0",
            "status": "running",
            "timestamp": time.time()
        }
    )


@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "timestamp": time.time()}


@app.get("/api/mock/configs", response_model=ApiResponse)
async def list_mock_configs(
    test_case: Optional[str] = Query(None, description="测试案例"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小")
):
    """获取Mock配置列表"""
    try:
        if test_case:
            configs = mock_service.get_mock_configs(test_case)
        else:
            # 获取所有测试案例的配置
            test_cases = mock_service.list_test_cases()
            configs = []
            for tc in test_cases:
                configs.extend(mock_service.get_mock_configs(tc))

        # 分页处理
        start = (page - 1) * size
        end = start + size
        paginated_configs = configs[start:end]

        return ApiResponse(
            code=200,
            message="获取成功",
            data={
                "configs": [config.__dict__ for config in paginated_configs],
                "total": len(configs),
                "page": page,
                "size": size
            }
        )
    except Exception as e:
        logger.error(f"获取Mock配置失败: {e}")
        return ApiResponse(code=500, message=f"获取失败: {str(e)}")


@app.post("/api/mock/configs", response_model=ApiResponse)
async def create_mock_config(config: MockApiCreate):
    """创建Mock配置"""
    try:
        success = mock_service.add_mock_config(config)
        if success:
            return ApiResponse(code=200, message="创建成功")
        else:
            return ApiResponse(code=500, message="创建失败")
    except Exception as e:
        logger.error(f"创建Mock配置失败: {e}")
        return ApiResponse(code=500, message=f"创建失败: {str(e)}")


@app.put("/api/mock/configs/{config_id}", response_model=ApiResponse)
async def update_mock_config(config_id: int, config: MockApiUpdate):
    """更新Mock配置"""
    try:
        config.id = config_id
        success = mock_service.update_mock_config(config)
        if success:
            return ApiResponse(code=200, message="更新成功")
        else:
            return ApiResponse(code=404, message="配置不存在")
    except Exception as e:
        logger.error(f"更新Mock配置失败: {e}")
        return ApiResponse(code=500, message=f"更新失败: {str(e)}")


@app.delete("/api/mock/configs/{config_id}", response_model=ApiResponse)
async def delete_mock_config(config_id: int):
    """删除Mock配置"""
    try:
        success = mock_service.delete_mock_config(config_id)
        if success:
            return ApiResponse(code=200, message="删除成功")
        else:
            return ApiResponse(code=404, message="配置不存在")
    except Exception as e:
        logger.error(f"删除Mock配置失败: {e}")
        return ApiResponse(code=500, message=f"删除失败: {str(e)}")


@app.get("/api/mock/test-cases", response_model=ApiResponse)
async def list_test_cases():
    """获取所有测试案例"""
    try:
        test_cases = mock_service.list_test_cases()
        return ApiResponse(
            code=200,
            message="获取成功",
            data={"test_cases": test_cases}
        )
    except Exception as e:
        logger.error(f"获取测试案例失败: {e}")
        return ApiResponse(code=500, message=f"获取失败: {str(e)}")


@app.api_route("/mock/{api_path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
async def mock_api_endpoint(api_path: str, request: Request):
    """Mock API端点 - 简化版，不需要test_case前缀"""
    try:
        # 获取请求数据
        request_data = None
        if request.method in ["POST", "PUT", "PATCH"]:
            try:
                request_data = await request.json()
            except:
                request_data = {}

        # 构造完整的API路径
        full_api_path = f"/{api_path}"

        # 查找匹配的Mock配置（在所有test_case中查找）
        from src.mock.dao import mock_dao
        all_test_cases = mock_dao.list_test_cases()

        matched_config = None
        matched_test_case = None

        for test_case in all_test_cases:
            configs = mock_dao.get_mock_configs_by_test_case(test_case)
            for config in configs:
                # 只匹配启用的接口
                if config['api_url'] == full_api_path and config.get('enabled', True):
                    matched_config = config
                    matched_test_case = test_case
                    break
            if matched_config:
                break

        if not matched_config:
            return JSONResponse(
                content={"error": f"未找到匹配的API配置: {full_api_path}"},
                status_code=404
            )

        # 生成Mock响应
        try:
            response_body = json.loads(matched_config['mock_body']) if matched_config.get('mock_body') else {}
        except:
            response_body = {"error": "Invalid mock body format"}

        # 记录请求日志到数据库
        try:
            from src.mock.dao import mock_dao
            mock_dao.insert_mock_log(
                api_url=matched_config['api_url'],
                method=request.method,
                request_data=json.dumps(request_data) if request_data else "",
                response_data=json.dumps(response_body),
                status_code=200
            )
        except Exception as log_error:
            logger.error(f"记录日志失败: {log_error}")

        # 同时记录到内存（用于API接口）
        log_entry = {
            "timestamp": time.time(),
            "test_case": matched_test_case,
            "api_path": api_path,
            "method": request.method,
            "request_data": request_data,
            "response_data": response_body,
            "status_code": 200
        }

        request_logs.append(log_entry)
        if len(request_logs) > MAX_LOGS:
            request_logs.pop(0)

        # 返回Mock响应
        return JSONResponse(
            content=response_body,
            status_code=200
        )

    except Exception as e:
        logger.error(f"Mock API处理失败: {e}")
        return JSONResponse(
            content={"error": f"Mock处理失败: {str(e)}"},
            status_code=500
        )


@app.get("/api/mock/logs", response_model=ApiResponse)
async def get_mock_logs(
    test_case: Optional[str] = Query(None, description="测试案例"),
    limit: int = Query(100, ge=1, le=1000, description="返回数量")
):
    """获取Mock请求日志"""
    try:
        logs = request_logs.copy()

        # 按测试案例过滤
        if test_case:
            logs = [log for log in logs if log["test_case"] == test_case]

        # 按时间倒序排列并限制数量
        logs.sort(key=lambda x: x["timestamp"], reverse=True)
        logs = logs[:limit]

        return ApiResponse(
            code=200,
            message="获取成功",
            data={
                "logs": logs,
                "total": len(logs)
            }
        )
    except Exception as e:
        logger.error(f"获取日志失败: {e}")
        return ApiResponse(code=500, message=f"获取失败: {str(e)}")


def run_server(host: str = "127.0.0.1", port: int = 8001, reload: bool = False):
    """运行服务器"""
    uvicorn.run(
        "src.mock.app:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )


if __name__ == "__main__":
    run_server()
