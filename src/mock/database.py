#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/21
# <AUTHOR> Kai

"""
Mock服务数据库操作
"""

import sqlite3
import json
import os
from typing import List, Optional, Dict, Any
from datetime import datetime
from pathlib import Path
from .models import MockConfig

# 数据库路径
DB_PATH = "data/mock.db"

def init_db():
    """初始化数据库"""
    # 确保数据目录存在
    os.makedirs("data", exist_ok=True)

    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()

        # 创建Mock配置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS mock_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                test_case TEXT NOT NULL,
                api_url TEXT NOT NULL,
                description TEXT,
                mock_rule TEXT,
                mock_body TEXT,
                condition_rule TEXT,
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(test_case, api_url)
            )
        ''')

        # 创建Mock日志表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS mock_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                test_case TEXT NOT NULL,
                api_url TEXT NOT NULL,
                method TEXT NOT NULL DEFAULT 'POST',
                request_data TEXT,
                response_data TEXT,
                status_code INTEGER DEFAULT 200,
                request_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_case ON mock_configs (test_case)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_api_url ON mock_configs (api_url)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_log_test_case ON mock_logs (test_case)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_log_time ON mock_logs (request_time)')

        conn.commit()

class MockDatabase:
    """Mock数据库操作类"""

    def __init__(self, db_path: str = DB_PATH):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """初始化数据库"""
        init_db()

    def add_mock_config(self, config: MockConfig) -> bool:
        """添加Mock配置"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO mock_configs
                    (test_case, api_url, description, mock_rule, mock_body, condition_rule, update_time)
                    VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (
                    config.test_case,
                    config.api_url,
                    config.description,
                    config.mock_rule,
                    config.mock_body,
                    config.condition
                ))
                conn.commit()
                return True
        except Exception as e:
            print(f"添加Mock配置失败: {e}")
            return False

    def get_mock_configs(self, test_case: str) -> List[MockConfig]:
        """获取指定测试案例的Mock配置"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM mock_configs WHERE test_case = ?
                    ORDER BY create_time DESC
                ''', (test_case,))

                rows = cursor.fetchall()
                configs = []

                for row in rows:
                    config = MockConfig(
                        id=row[0],
                        test_case=row[1],
                        api_url=row[2],
                        description=row[3],
                        mock_rule=row[4],
                        mock_body=row[5],
                        condition=row[6],
                        create_time=row[7],
                        update_time=row[8]
                    )
                    configs.append(config)

                return configs
        except Exception as e:
            print(f"获取Mock配置失败: {e}")
            return []

    def update_mock_config(self, config: MockConfig) -> bool:
        """更新Mock配置"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE mock_configs
                    SET description = ?, mock_rule = ?, mock_body = ?,
                        condition_rule = ?, update_time = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (
                    config.description,
                    config.mock_rule,
                    config.mock_body,
                    config.condition,
                    config.id
                ))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"更新Mock配置失败: {e}")
            return False

    def delete_mock_config(self, config_id: int) -> bool:
        """删除Mock配置"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM mock_configs WHERE id = ?', (config_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"删除Mock配置失败: {e}")
            return False

    def list_all_test_cases(self) -> List[str]:
        """列出所有测试案例"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT DISTINCT test_case FROM mock_configs ORDER BY test_case')
                rows = cursor.fetchall()
                return [row[0] for row in rows]
        except Exception as e:
            print(f"获取测试案例列表失败: {e}")
            return []

    def log_mock_request(self, test_case: str, api_url: str, method: str,
                        request_data: str, response_data: str, status_code: int = 200) -> bool:
        """记录Mock请求日志"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO mock_logs
                    (test_case, api_url, method, request_data, response_data, status_code)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (test_case, api_url, method, request_data, response_data, status_code))
                conn.commit()
                return True
        except Exception as e:
            print(f"记录Mock日志失败: {e}")
            return False

    def get_mock_logs(self, test_case: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取Mock请求日志"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                if test_case:
                    cursor.execute('''
                        SELECT * FROM mock_logs
                        WHERE test_case = ?
                        ORDER BY request_time DESC
                        LIMIT ?
                    ''', (test_case, limit))
                else:
                    cursor.execute('''
                        SELECT * FROM mock_logs
                        ORDER BY request_time DESC
                        LIMIT ?
                    ''', (limit,))

                rows = cursor.fetchall()
                logs = []

                for row in rows:
                    logs.append({
                        'id': row[0],
                        'test_case': row[1],
                        'api_url': row[2],
                        'method': row[3],
                        'request_data': row[4],
                        'response_data': row[5],
                        'status_code': row[6],
                        'request_time': row[7]
                    })

                return logs
        except Exception as e:
            print(f"获取Mock日志失败: {e}")
            return []

# 全局数据库实例
mock_db = MockDatabase()
