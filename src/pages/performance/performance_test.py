"""
性能测试页面
提供完整的Locust性能测试功能
"""

import streamlit as st
import json
import time
from typing import Dict, Any, List
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from src.components.performance.locust_core import locust_core, TestConfig
from src.components.performance.parameterization import param_manager
from src.components.performance.function_helper import function_helper


def render_basic_config() -> Dict[str, Any]:
    """渲染基础配置"""
    st.markdown("### 📋 基础配置")

    col1, col2 = st.columns(2)

    with col1:
        users = st.number_input(
            "并发用户数",
            min_value=1,
            max_value=1000,
            value=10,
            help="同时运行的虚拟用户数量"
        )

        spawn_rate = st.number_input(
            "启动速率",
            min_value=0.1,
            max_value=100.0,
            value=2.0,
            step=0.1,
            help="每秒启动的用户数"
        )

        run_time = st.number_input(
            "测试时长(秒)",
            min_value=10,
            max_value=3600,
            value=60,
            help="测试运行的总时间"
        )

    with col2:
        host = st.text_input(
            "目标主机",
            value="http://localhost:8080",
            help="被测试的服务器地址"
        )

        think_time_min = st.number_input(
            "最小思考时间(秒)",
            min_value=0.1,
            max_value=10.0,
            value=1.0,
            step=0.1,
            help="用户请求间的最小等待时间"
        )

        think_time_max = st.number_input(
            "最大思考时间(秒)",
            min_value=0.1,
            max_value=10.0,
            value=3.0,
            step=0.1,
            help="用户请求间的最大等待时间"
        )

    return {
        "users": users,
        "spawn_rate": spawn_rate,
        "run_time": run_time,
        "host": host,
        "think_time_min": think_time_min,
        "think_time_max": think_time_max,
        "timeout": 30,
        "task_interval": 0.0
    }


def render_parameterization_config() -> Dict[str, Any]:
    """渲染参数化配置"""
    with st.expander("🔧 参数化配置", expanded=False):
        enabled = st.checkbox("启用参数化", value=False)

        if not enabled:
            return {"enabled": False}

        source_type = st.selectbox(
            "数据源类型",
            ["手动输入", "CSV文件", "数据库查询"],
            help="选择参数化数据的来源"
        )

        config = {}

        if source_type == "手动输入":
            st.markdown("**手动输入数据**")
            data_text = st.text_area(
                "JSON格式数据",
                value='[{"username": "user1", "password": "pass1"}, {"username": "user2", "password": "pass2"}]',
                height=100,
                help="输入JSON格式的测试数据"
            )

            try:
                data = json.loads(data_text)
                config = {"data": data}
                st.success(f"✅ 解析成功，共 {len(data)} 条数据")
            except Exception as e:
                st.error(f"❌ JSON格式错误: {e}")
                config = {"data": []}

        elif source_type == "CSV文件":
            st.markdown("**CSV文件配置**")

            col1, col2 = st.columns(2)
            with col1:
                file_path = st.text_input("文件路径", value="test_data.csv")
                encoding = st.selectbox("文件编码", ["utf-8", "gbk", "gb2312"])

            with col2:
                delimiter = st.text_input("分隔符", value=",")

                if st.button("📄 创建示例CSV"):
                    success = param_manager.create_sample_csv(file_path, 10)
                    if success:
                        st.success(f"✅ 示例文件已创建: {file_path}")
                    else:
                        st.error("❌ 创建文件失败")

            config = {
                "file_path": file_path,
                "encoding": encoding,
                "delimiter": delimiter
            }

        else:  # 数据库查询
            st.markdown("**数据库配置**")

            col1, col2 = st.columns(2)
            with col1:
                db_type = st.selectbox("数据库类型", ["MySQL", "PostgreSQL", "SQLite"])
                host = st.text_input("主机", value="localhost")
                port = st.number_input("端口", value=3306)

            with col2:
                database = st.text_input("数据库名", value="test_db")
                username = st.text_input("用户名", value="root")
                password = st.text_input("密码", type="password")

            query = st.text_area(
                "SQL查询",
                value="SELECT username, password FROM users LIMIT 100",
                help="查询测试数据的SQL语句"
            )

            config = {
                "db_type": db_type,
                "host": host,
                "port": port,
                "database": database,
                "username": username,
                "password": password,
                "query": query
            }

        # 数据使用模式
        st.markdown("**数据使用模式**")
        data_mode = st.selectbox(
            "数据模式",
            ["数据循环", "数据独占", "数据复用"],
            help="选择参数化数据的使用方式"
        )

        return {
            "enabled": True,
            "source_type": source_type,
            "config": config,
            "data_mode": data_mode
        }


def render_on_start_apis() -> List[Dict[str, Any]]:
    """渲染前置步骤配置"""
    with st.expander("🚀 前置步骤", expanded=False):
        st.markdown("配置测试开始前需要执行的接口（如登录）")

        if "on_start_apis" not in st.session_state:
            st.session_state.on_start_apis = []

        # 添加新接口
        if st.button("➕ 添加前置接口"):
            st.session_state.on_start_apis.append({
                "name": f"前置接口 {len(st.session_state.on_start_apis) + 1}",
                "method": "GET",
                "url": "/api/login",
                "headers": {},
                "data": {},
                "extractors": [],
                "assertions": []
            })

        # 显示和编辑接口
        for i, api in enumerate(st.session_state.on_start_apis):
            with st.container():
                st.markdown(f"**接口 {i+1}**")

                col1, col2, col3 = st.columns([3, 1, 1])

                with col1:
                    api["name"] = st.text_input(f"接口名称", value=api["name"], key=f"on_start_name_{i}")

                with col2:
                    api["method"] = st.selectbox("方法", ["GET", "POST", "PUT", "DELETE"],
                                               index=["GET", "POST", "PUT", "DELETE"].index(api["method"]),
                                               key=f"on_start_method_{i}")

                with col3:
                    if st.button("🗑️", key=f"on_start_delete_{i}", help="删除接口"):
                        st.session_state.on_start_apis.pop(i)
                        st.rerun()

                api["url"] = st.text_input("URL", value=api["url"], key=f"on_start_url_{i}")

                # 请求头和数据
                col1, col2 = st.columns(2)

                with col1:
                    headers_text = st.text_area("请求头(JSON)", value=json.dumps(api["headers"], indent=2),
                                              key=f"on_start_headers_{i}", height=100)
                    try:
                        api["headers"] = json.loads(headers_text) if headers_text.strip() else {}
                    except:
                        st.error("请求头JSON格式错误")

                with col2:
                    data_text = st.text_area("请求数据(JSON)", value=json.dumps(api["data"], indent=2),
                                           key=f"on_start_data_{i}", height=100)
                    try:
                        api["data"] = json.loads(data_text) if data_text.strip() else {}
                    except:
                        st.error("请求数据JSON格式错误")

                st.markdown("---")

    return st.session_state.on_start_apis


def render_test_tasks() -> List[Dict[str, Any]]:
    """渲染测试任务配置"""
    with st.expander("🎯 测试任务", expanded=True):
        st.markdown("配置性能测试的主要任务")

        if "test_tasks" not in st.session_state:
            st.session_state.test_tasks = []

        # 添加新任务
        if st.button("➕ 添加测试任务"):
            st.session_state.test_tasks.append({
                "name": f"测试任务 {len(st.session_state.test_tasks) + 1}",
                "weight": 1,
                "sequence": len(st.session_state.test_tasks) + 1,
                "apis": []
            })

        # 显示和编辑任务
        for i, task in enumerate(st.session_state.test_tasks):
            with st.container():
                st.markdown(f"**任务 {i+1}**")

                col1, col2, col3, col4 = st.columns([2, 1, 1, 1])

                with col1:
                    task["name"] = st.text_input("任务名称", value=task["name"], key=f"task_name_{i}")

                with col2:
                    task["weight"] = st.number_input("权重", min_value=1, max_value=10, value=task["weight"], key=f"task_weight_{i}")

                with col3:
                    task["sequence"] = st.number_input("执行顺序", min_value=1, value=task["sequence"], key=f"task_sequence_{i}")

                with col4:
                    if st.button("🗑️", key=f"task_delete_{i}", help="删除任务"):
                        st.session_state.test_tasks.pop(i)
                        st.rerun()

                # 任务中的接口
                st.markdown("**任务接口**")

                if st.button(f"➕ 添加接口", key=f"add_api_{i}"):
                    task["apis"].append({
                        "name": f"接口 {len(task['apis']) + 1}",
                        "method": "GET",
                        "url": "/api/test",
                        "headers": {},
                        "data": {},
                        "extractors": [],
                        "assertions": []
                    })

                for j, api in enumerate(task["apis"]):
                    with st.container():
                        st.markdown(f"*接口 {j+1}*")

                        col1, col2, col3 = st.columns([3, 1, 1])

                        with col1:
                            api["name"] = st.text_input("接口名称", value=api["name"], key=f"api_name_{i}_{j}")

                        with col2:
                            api["method"] = st.selectbox("方法", ["GET", "POST", "PUT", "DELETE"],
                                                       index=["GET", "POST", "PUT", "DELETE"].index(api["method"]),
                                                       key=f"api_method_{i}_{j}")

                        with col3:
                            if st.button("🗑️", key=f"api_delete_{i}_{j}", help="删除接口"):
                                task["apis"].pop(j)
                                st.rerun()

                        api["url"] = st.text_input("URL", value=api["url"], key=f"api_url_{i}_{j}")

                st.markdown("---")

    return st.session_state.test_tasks


def render_test_status():
    """渲染测试状态"""
    st.markdown("### 📊 测试状态")

    status = locust_core.get_status()

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if status["status"] == "running":
            st.metric("状态", "🟢 运行中")
        elif status["status"] == "stopped":
            st.metric("状态", "✅ 已完成")
        elif status["status"] == "error":
            st.metric("状态", "❌ 错误")
        else:
            st.metric("状态", "⚪ 空闲")

    with col2:
        st.metric("测试ID", status["test_id"][:8] if status["test_id"] else "无")

    with col3:
        st.metric("运行时长", f"{status['duration']:.0f}秒")

    with col4:
        st.metric("开始时间", status["start_time"] or "未开始")


def render_test_controls(test_config: Dict[str, Any], param_config: Dict[str, Any],
                        on_start_apis: List[Dict[str, Any]], test_tasks: List[Dict[str, Any]]):
    """渲染测试控制按钮"""
    st.markdown("### 🎮 测试控制")

    status = locust_core.get_status()
    is_running = status["status"] == "running"

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if st.button("🚀 开始测试", disabled=is_running, use_container_width=True):
            # 构建测试配置
            config = TestConfig(
                users=test_config["users"],
                spawn_rate=test_config["spawn_rate"],
                run_time=test_config["run_time"],
                host=test_config["host"],
                think_time_min=test_config["think_time_min"],
                think_time_max=test_config["think_time_max"],
                timeout=test_config["timeout"],
                task_interval=test_config["task_interval"],
                on_start_apis=on_start_apis,
                test_tasks=test_tasks,
                parameterization=param_config
            )

            if locust_core.start_test(config):
                st.success("✅ 测试已启动")
                st.rerun()
            else:
                st.error("❌ 启动测试失败")

    with col2:
        if st.button("⏹️ 停止测试", disabled=not is_running, use_container_width=True):
            if locust_core.stop_test():
                st.success("✅ 测试已停止")
                st.rerun()
            else:
                st.error("❌ 停止测试失败")

    with col3:
        if st.button("🔄 刷新状态", use_container_width=True):
            st.rerun()

    with col4:
        if st.button("📊 查看报告", use_container_width=True):
            st.switch_page("src/pages/performance/performance_report.py")


def render():
    """主渲染函数"""
    st.title("🚀 性能测试")
    st.markdown("基于Locust的完整性能测试平台")
    st.markdown("---")

    # 基础配置
    test_config = render_basic_config()

    # 参数化配置
    param_config = render_parameterization_config()

    # 前置步骤
    on_start_apis = render_on_start_apis()

    # 测试任务
    test_tasks = render_test_tasks()

    st.markdown("---")

    # 测试状态
    render_test_status()

    st.markdown("---")

    # 测试控制
    render_test_controls(test_config, param_config, on_start_apis, test_tasks)

    # 函数助手
    with st.expander("🔧 函数助手", expanded=False):
        st.markdown("**内置函数**")
        try:
            functions = function_helper.get_available_functions()

            for category, funcs in functions.items():
                st.markdown(f"**{category}**")
                for func in funcs:
                    if isinstance(func, dict):
                        name = func.get('name', 'unknown')
                        params = func.get('params', '')
                        description = func.get('description', '')
                        st.code(f"${{{name}({params})}}", language="text")
                        st.caption(description)
                    else:
                        # 如果func是字符串，直接显示
                        st.code(f"${{{func}()}}", language="text")
        except Exception as e:
            st.error(f"函数助手加载失败: {e}")
            st.markdown("**常用函数示例:**")
            st.code("${current_time()}", language="text")
            st.caption("获取当前时间")
            st.code("${random_int(1, 100)}", language="text")
            st.caption("生成随机整数")
            st.code("${uuid4()}", language="text")
            st.caption("生成UUID")

    # 使用说明
    with st.expander("📖 使用说明", expanded=False):
        st.markdown("""
        ### 🎯 完整功能
        1. **基础配置**: 并发用户数、启动速率、测试时长、目标主机等
        2. **参数化配置**: 支持CSV文件、数据库查询、手动输入等多种数据源
        3. **前置步骤**: 配置登录等前置接口，支持变量提取和断言
        4. **测试任务**: 配置主要的性能测试任务，支持权重和执行顺序
        5. **函数助手**: 内置时间、随机数、加密等常用函数
        6. **变量系统**: 支持接口间的变量传递和数据提取
        7. **断言验证**: 多种断言方式验证响应结果

        ### 📝 高级特性
        - **动态Locust脚本生成**: 根据配置自动生成完整的Locust测试脚本
        - **完整的参数化支持**: 支持多种数据源和使用模式
        - **专业的变量提取**: JSON路径、正则表达式、响应头提取
        - **灵活的断言系统**: 状态码、响应文本、JSON路径、响应时间断言
        - **详细的测试报告**: 完整的性能指标分析和失败详情
        """)


if __name__ == "__main__":
    render()
