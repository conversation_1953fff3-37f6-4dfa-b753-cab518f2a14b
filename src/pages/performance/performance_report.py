"""
性能测试报告页面
提供详细的性能测试报告和分析功能
"""

import streamlit as st
import json
import pandas as pd
from typing import Dict, Any, List
import sys
import os

# 添加项目根目录到Python路径
current_file = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_file))))
sys.path.insert(0, project_root)

# 导入性能测试模块
try:
    from src.components.performance.locust_core import locust_core
    from src.components.performance.assertion_engine import assertion_engine
    from src.components.performance.report_storage import report_storage
except ImportError as e:
    st.error(f"❌ 无法导入性能测试模块: {e}")
    st.error(f"项目根路径: {project_root}")
    st.error(f"当前文件路径: {current_file}")
    st.stop()


def render_test_overview(report):
    """渲染测试概览"""
    st.markdown("**测试概览**")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("测试名称", report.test_name)

    with col2:
        st.metric("测试类型", report.test_type)

    with col3:
        st.metric("运行时长", f"{report.duration:.0f}秒")

    with col4:
        st.metric("开始时间", report.start_time)


def render_performance_metrics(report):
    """渲染性能指标"""
    st.markdown("**性能指标**")

    metrics = report.metrics

    if not metrics:
        st.info("📊 暂无性能指标数据")
        return

    # 显示性能指标
    metrics_data = {
        "指标": ["总请求数", "成功请求", "失败请求", "成功率", "平均响应时间", "最小响应时间", "最大响应时间", "QPS"],
        "数值": [
            f"{metrics.get('total_requests', 0):,}",
            f"{metrics.get('success_requests', 0):,}",
            f"{metrics.get('failed_requests', 0):,}",
            f"{metrics.get('success_rate', 0):.1f}%",
            f"{metrics.get('avg_response_time', 0):.1f}ms",
            f"{metrics.get('min_response_time', 0):.1f}ms",
            f"{metrics.get('max_response_time', 0):.1f}ms",
            f"{metrics.get('requests_per_second', 0):.1f}"
        ]
    }

    df = pd.DataFrame(metrics_data)
    st.dataframe(df, use_container_width=True, hide_index=True)


def render_response_time_distribution(report):
    """渲染响应时间分布"""
    st.markdown("**响应时间分布**")

    metrics = report.metrics

    if not metrics:
        st.info("📈 暂无响应时间数据")
        return

    # 显示响应时间分布
    avg_time = metrics.get('avg_response_time', 0)
    min_time = metrics.get('min_response_time', 0)
    max_time = metrics.get('max_response_time', 0)
    median_time = metrics.get('median_response_time', avg_time)

    if avg_time > 0:
        percentile_data = {
            "百分位": ["最小值", "50%", "平均值", "最大值"],
            "响应时间(ms)": [f"{min_time:.1f}", f"{median_time:.1f}", f"{avg_time:.1f}", f"{max_time:.1f}"]
        }

        df = pd.DataFrame(percentile_data)
        st.dataframe(df, use_container_width=True, hide_index=True)
    else:
        st.info("📈 响应时间数据不完整")


def render_failure_analysis(report):
    """渲染失败分析"""
    st.markdown("**失败分析**")

    metrics = report.metrics
    failed_requests = report.failed_requests

    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("总请求数", f"{metrics.get('total_requests', 0):,}")

    with col2:
        st.metric("失败请求数", f"{metrics.get('failed_requests', 0):,}")

    with col3:
        st.metric("成功率", f"{metrics.get('success_rate', 0):.1f}%")

    # 显示失败请求详情
    if failed_requests:
        st.markdown("**失败请求详情**")

        failure_data = []
        for req in failed_requests:
            failure_data.append({
                "时间": req.get('timestamp', ''),
                "方法": req.get('method', ''),
                "URL": req.get('url', ''),
                "状态码": req.get('status_code', ''),
                "响应时间": f"{req.get('response_time', 0)}ms",
                "错误类型": req.get('error_type', ''),
                "错误信息": req.get('error_message', '')
            })

        df = pd.DataFrame(failure_data)
        st.dataframe(df, use_container_width=True, hide_index=True)
    else:
        st.info("✅ 暂无失败请求")


def render_performance_recommendations(report):
    """渲染性能分析"""
    st.markdown("**性能分析**")

    metrics = report.metrics

    if not metrics:
        st.info("📊 暂无性能数据用于分析")
        return

    # 基于实际数据生成分析
    avg_time = metrics.get('avg_response_time', 0)
    failure_count = metrics.get('failed_requests', 0)
    total_requests = metrics.get('total_requests', 0)
    success_rate = metrics.get('success_rate', 0)

    analysis = []

    if avg_time > 1000:
        analysis.append("⚠️ 平均响应时间较长，建议优化接口性能")
    elif avg_time > 500:
        analysis.append("💡 响应时间适中，可考虑进一步优化")
    else:
        analysis.append("✅ 响应时间表现良好")

    if failure_count > 0:
        failure_rate = 100 - success_rate
        if failure_rate > 5:
            analysis.append("🔴 失败率较高，需要检查系统稳定性")
        elif failure_rate > 1:
            analysis.append("⚠️ 存在少量失败请求，建议关注")
        else:
            analysis.append("✅ 失败率在可接受范围内")
    else:
        analysis.append("✅ 无失败请求，系统稳定")

    for item in analysis:
        st.write(item)


def render_test_comparison():
    """渲染测试历史"""
    st.markdown("**测试历史**")

    # 这里可以实现真实的测试历史记录功能
    # 目前暂时显示提示信息
    st.info("📊 测试历史功能开发中，将支持多轮测试结果对比分析")


def render_export_options():
    """渲染导出选项"""
    st.markdown("**操作**")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("🔄 刷新报告", use_container_width=True):
            st.rerun()

    with col2:
        if st.button("🔙 返回测试", use_container_width=True):
            try:
                st.switch_page("src/pages/performance/performance_test.py")
            except Exception as e:
                st.error(f"页面跳转失败: {e}")
                st.info("💡 请手动选择菜单: 📊性能测试 -> 性能测试")


def render_report_selector():
    """渲染报告选择器"""
    st.markdown("**报告管理**")

    # 获取所有报告
    reports = report_storage.list_reports()

    if not reports:
        st.info("📊 暂无测试报告，请先执行性能测试")
        return None

    # 筛选功能
    col1, col2 = st.columns([2, 2])

    with col1:
        # 按测试类型筛选
        test_types = list(set([r.test_type for r in reports]))
        selected_type = st.selectbox(
            "筛选测试类型",
            ["全部"] + test_types,
            key="filter_type"
        )

    with col2:
        # 按日期筛选
        date_options = ["全部", "今天", "最近7天", "最近30天"]
        selected_date = st.selectbox(
            "筛选时间范围",
            date_options,
            key="filter_date"
        )

    # 应用筛选
    filtered_reports = reports

    if selected_type != "全部":
        filtered_reports = [r for r in filtered_reports if r.test_type == selected_type]

    if selected_date != "全部":
        from datetime import datetime, timedelta
        now = datetime.now()

        if selected_date == "今天":
            cutoff = now.replace(hour=0, minute=0, second=0, microsecond=0)
        elif selected_date == "最近7天":
            cutoff = now - timedelta(days=7)
        elif selected_date == "最近30天":
            cutoff = now - timedelta(days=30)
        else:
            cutoff = None

        if cutoff:
            filtered_reports = [
                r for r in filtered_reports
                if datetime.strptime(r.created_at, '%Y-%m-%d %H:%M:%S') >= cutoff
            ]

    if not filtered_reports:
        st.warning("⚠️ 没有符合筛选条件的报告")
        return None

    # 报告选择和操作
    col1, col2, col3 = st.columns([3, 1, 1])

    with col1:
        # 报告选择下拉框
        report_options = []
        for report in filtered_reports:
            option_text = f"{report.test_name} | {report.test_type} | {report.created_at}"
            report_options.append(option_text)

        if report_options:
            selected_index = st.selectbox(
                f"选择报告 (共{len(filtered_reports)}个)",
                range(len(report_options)),
                format_func=lambda x: report_options[x],
                key="selected_report"
            )

            return filtered_reports[selected_index]

    with col2:
        if st.button("🔄 刷新", use_container_width=True):
            st.rerun()

    with col3:
        if filtered_reports and st.button("🗑️ 删除", use_container_width=True):
            if "selected_report" in st.session_state and st.session_state.selected_report < len(filtered_reports):
                selected_report = filtered_reports[st.session_state.selected_report]
                if report_storage.delete_report(selected_report.report_id):
                    st.success("✅ 报告已删除")
                    st.rerun()
                else:
                    st.error("❌ 删除失败")

    return None


def render():
    """渲染性能报告页面"""
    st.title("📊 性能报告")

    # 报告选择器
    selected_report = render_report_selector()

    if not selected_report:
        return

    st.markdown("---")

    # 测试概览
    render_test_overview(selected_report)

    st.markdown("---")

    # 性能指标
    render_performance_metrics(selected_report)

    st.markdown("---")

    # 响应时间分布
    render_response_time_distribution(selected_report)

    st.markdown("---")

    # 失败分析
    render_failure_analysis(selected_report)

    st.markdown("---")

    # 性能分析
    render_performance_recommendations(selected_report)

    st.markdown("---")

    # 导出选项
    render_export_options()


if __name__ == "__main__":
    render()
