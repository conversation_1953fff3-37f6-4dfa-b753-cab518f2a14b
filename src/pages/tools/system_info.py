#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/21
# <AUTHOR> Kai

import streamlit as st
import platform
import psutil
import socket
import datetime
import sys
import os
from pathlib import Path


def render():
    """渲染系统信息页面"""
    st.header("📊 系统信息")
    
    # 系统基本信息
    render_system_basic_info()
    
    st.divider()
    
    # 硬件信息
    render_hardware_info()
    
    st.divider()
    
    # 网络信息
    render_network_info()
    
    st.divider()
    
    # Python环境信息
    render_python_info()


def render_system_basic_info():
    """渲染系统基本信息"""
    st.subheader("🖥️ 系统基本信息")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 操作系统")
        system_info = {
            "系统": platform.system(),
            "版本": platform.version(),
            "发行版": platform.release(),
            "架构": platform.architecture()[0],
            "处理器": platform.processor(),
            "主机名": platform.node(),
            "用户": os.getlogin() if hasattr(os, 'getlogin') else "Unknown"
        }
        
        for key, value in system_info.items():
            st.metric(key, value)
    
    with col2:
        st.markdown("### 系统时间")
        now = datetime.datetime.now()
        time_info = {
            "当前时间": now.strftime("%Y-%m-%d %H:%M:%S"),
            "时区": str(now.astimezone().tzinfo),
            "启动时间": datetime.datetime.fromtimestamp(psutil.boot_time()).strftime("%Y-%m-%d %H:%M:%S"),
            "运行时长": str(datetime.timedelta(seconds=int((now - datetime.datetime.fromtimestamp(psutil.boot_time())).total_seconds())))
        }
        
        for key, value in time_info.items():
            st.metric(key, value)


def render_hardware_info():
    """渲染硬件信息"""
    st.subheader("🔧 硬件信息")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("### CPU信息")
        cpu_info = {
            "CPU核心数": psutil.cpu_count(logical=False),
            "逻辑处理器": psutil.cpu_count(logical=True),
            "CPU使用率": f"{psutil.cpu_percent(interval=1):.1f}%",
            "CPU频率": f"{psutil.cpu_freq().current:.0f} MHz" if psutil.cpu_freq() else "Unknown"
        }
        
        for key, value in cpu_info.items():
            st.metric(key, value)
    
    with col2:
        st.markdown("### 内存信息")
        memory = psutil.virtual_memory()
        memory_info = {
            "总内存": f"{memory.total / (1024**3):.1f} GB",
            "可用内存": f"{memory.available / (1024**3):.1f} GB",
            "已用内存": f"{memory.used / (1024**3):.1f} GB",
            "内存使用率": f"{memory.percent:.1f}%"
        }
        
        for key, value in memory_info.items():
            st.metric(key, value)
        
        # 内存使用率进度条
        st.progress(memory.percent / 100)
    
    with col3:
        st.markdown("### 磁盘信息")
        disk = psutil.disk_usage('/')
        disk_info = {
            "总空间": f"{disk.total / (1024**3):.1f} GB",
            "可用空间": f"{disk.free / (1024**3):.1f} GB",
            "已用空间": f"{disk.used / (1024**3):.1f} GB",
            "磁盘使用率": f"{disk.percent:.1f}%"
        }
        
        for key, value in disk_info.items():
            st.metric(key, value)
        
        # 磁盘使用率进度条
        st.progress(disk.percent / 100)


def render_network_info():
    """渲染网络信息"""
    st.subheader("🌐 网络信息")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 网络接口")
        try:
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            
            # 获取所有网络接口
            interfaces = psutil.net_if_addrs()
            
            network_info = {
                "主机名": hostname,
                "本地IP": local_ip
            }
            
            for key, value in network_info.items():
                st.metric(key, value)
            
            # 显示网络接口详情
            with st.expander("网络接口详情", expanded=False):
                for interface, addresses in interfaces.items():
                    st.write(f"**{interface}:**")
                    for addr in addresses:
                        if addr.family == socket.AF_INET:
                            st.write(f"  - IPv4: {addr.address}")
                        elif addr.family == socket.AF_INET6:
                            st.write(f"  - IPv6: {addr.address}")
        
        except Exception as e:
            st.error(f"获取网络信息失败: {e}")
    
    with col2:
        st.markdown("### 网络统计")
        try:
            net_io = psutil.net_io_counters()
            network_stats = {
                "发送字节": f"{net_io.bytes_sent / (1024**2):.1f} MB",
                "接收字节": f"{net_io.bytes_recv / (1024**2):.1f} MB",
                "发送包数": f"{net_io.packets_sent:,}",
                "接收包数": f"{net_io.packets_recv:,}"
            }
            
            for key, value in network_stats.items():
                st.metric(key, value)
        
        except Exception as e:
            st.error(f"获取网络统计失败: {e}")


def render_python_info():
    """渲染Python环境信息"""
    st.subheader("🐍 Python环境信息")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### Python基本信息")
        python_info = {
            "Python版本": platform.python_version(),
            "Python实现": platform.python_implementation(),
            "编译器": platform.python_compiler(),
            "执行路径": sys.executable,
            "工作目录": os.getcwd()
        }
        
        for key, value in python_info.items():
            st.metric(key, value)
    
    with col2:
        st.markdown("### 环境变量")
        important_env_vars = [
            "PATH", "PYTHONPATH", "HOME", "USER", "SHELL"
        ]
        
        env_info = {}
        for var in important_env_vars:
            value = os.environ.get(var, "未设置")
            if len(value) > 50:
                value = value[:47] + "..."
            env_info[var] = value
        
        for key, value in env_info.items():
            st.metric(key, value)
        
        # 显示所有环境变量
        with st.expander("所有环境变量", expanded=False):
            for key, value in sorted(os.environ.items()):
                st.write(f"**{key}:** {value}")
    
    # 已安装的包信息
    st.markdown("### 📦 重要Python包版本")
    
    important_packages = [
        "streamlit", "pandas", "numpy", "requests", 
        "fastapi", "uvicorn", "aiosqlite", "cryptography"
    ]
    
    col1, col2, col3, col4 = st.columns(4)
    columns = [col1, col2, col3, col4]
    
    for i, package in enumerate(important_packages):
        try:
            import importlib
            module = importlib.import_module(package)
            version = getattr(module, '__version__', 'Unknown')
            columns[i % 4].metric(package, version)
        except ImportError:
            columns[i % 4].metric(package, "未安装")
        except Exception:
            columns[i % 4].metric(package, "Unknown")
    
    # 刷新按钮
    if st.button("🔄 刷新系统信息", use_container_width=True):
        st.rerun()


if __name__ == "__main__":
    render()
