"""
文本对比工具
"""

import streamlit as st
import difflib
import re
from datetime import datetime
from typing import List, Tu<PERSON>, Dict, Any
import html


class TextDiffer:
    """文本对比核心类"""

    @staticmethod
    def normalize_text(text: str, ignore_whitespace: bool = False, ignore_case: bool = False) -> str:
        """标准化文本"""
        if ignore_case:
            text = text.lower()
        if ignore_whitespace:
            text = re.sub(r'\s+', ' ', text.strip())
        return text

    @staticmethod
    def get_char_level_diff(line1: str, line2: str) -> Tuple[str, str]:
        """获取字符级别的差异，返回带标记的HTML"""
        matcher = difflib.SequenceMatcher(None, line1, line2)

        result1_parts = []
        result2_parts = []

        for tag, i1, i2, j1, j2 in matcher.get_opcodes():
            text1_part = html.escape(line1[i1:i2])
            text2_part = html.escape(line2[j1:j2])

            if tag == 'equal':
                result1_parts.append(text1_part)
                result2_parts.append(text2_part)
            elif tag == 'delete':
                result1_parts.append(f'<span style="background-color: #ffcdd2; color: #b71c1c; text-decoration: line-through; font-weight: bold; padding: 1px 2px; border-radius: 2px;">{text1_part}</span>')
                # 删除的部分在text2中不显示
            elif tag == 'insert':
                # 插入的部分在text1中不显示
                result2_parts.append(f'<span style="background-color: #c8e6c9; color: #1b5e20; font-weight: bold; padding: 1px 2px; border-radius: 2px; border: 1px solid #4caf50;">{text2_part}</span>')
            elif tag == 'replace':
                result1_parts.append(f'<span style="background-color: #ffcdd2; color: #b71c1c; text-decoration: line-through; font-weight: bold; padding: 1px 2px; border-radius: 2px;">{text1_part}</span>')
                result2_parts.append(f'<span style="background-color: #c8e6c9; color: #1b5e20; font-weight: bold; padding: 1px 2px; border-radius: 2px; border: 1px solid #4caf50;">{text2_part}</span>')

        return ''.join(result1_parts), ''.join(result2_parts)

    @staticmethod
    def get_word_level_diff(line1: str, line2: str) -> Tuple[str, str]:
        """获取单词级别的差异"""
        words1 = line1.split()
        words2 = line2.split()

        matcher = difflib.SequenceMatcher(None, words1, words2)

        result1_parts = []
        result2_parts = []

        for tag, i1, i2, j1, j2 in matcher.get_opcodes():
            if tag == 'equal':
                result1_parts.extend(words1[i1:i2])
                result2_parts.extend(words2[j1:j2])
            elif tag == 'delete':
                for word in words1[i1:i2]:
                    result1_parts.append(f'<span style="background-color: #ffcdd2; color: #b71c1c; text-decoration: line-through; font-weight: bold; padding: 1px 3px; margin: 0 1px; border-radius: 3px;">{html.escape(word)}</span>')
            elif tag == 'insert':
                for word in words2[j1:j2]:
                    result2_parts.append(f'<span style="background-color: #c8e6c9; color: #1b5e20; font-weight: bold; padding: 1px 3px; margin: 0 1px; border-radius: 3px; border: 1px solid #4caf50;">{html.escape(word)}</span>')
            elif tag == 'replace':
                for word in words1[i1:i2]:
                    result1_parts.append(f'<span style="background-color: #ffcdd2; color: #b71c1c; text-decoration: line-through; font-weight: bold; padding: 1px 3px; margin: 0 1px; border-radius: 3px;">{html.escape(word)}</span>')
                for word in words2[j1:j2]:
                    result2_parts.append(f'<span style="background-color: #c8e6c9; color: #1b5e20; font-weight: bold; padding: 1px 3px; margin: 0 1px; border-radius: 3px; border: 1px solid #4caf50;">{html.escape(word)}</span>')

        return ' '.join(result1_parts), ' '.join(result2_parts)

    @staticmethod
    def get_line_diff(text1: str, text2: str, ignore_whitespace: bool = False, ignore_case: bool = False) -> List[str]:
        """获取逐行差异"""
        lines1 = TextDiffer.normalize_text(text1, ignore_whitespace, ignore_case).splitlines()
        lines2 = TextDiffer.normalize_text(text2, ignore_whitespace, ignore_case).splitlines()

        differ = difflib.unified_diff(
            lines1, lines2,
            fromfile='文本1', tofile='文本2',
            lineterm='', n=3
        )
        return list(differ)

    @staticmethod
    def get_html_diff(text1: str, text2: str, ignore_whitespace: bool = False, ignore_case: bool = False) -> str:
        """获取HTML格式的差异对比"""
        lines1 = TextDiffer.normalize_text(text1, ignore_whitespace, ignore_case).splitlines()
        lines2 = TextDiffer.normalize_text(text2, ignore_whitespace, ignore_case).splitlines()

        differ = difflib.HtmlDiff(wrapcolumn=80)
        return differ.make_table(lines1, lines2, '文本1', '文本2', context=True, numlines=2)

    @staticmethod
    def calculate_similarity(text1: str, text2: str, ignore_whitespace: bool = False,
                             ignore_case: bool = False) -> float:
        """计算文本相似度"""
        norm_text1 = TextDiffer.normalize_text(text1, ignore_whitespace, ignore_case)
        norm_text2 = TextDiffer.normalize_text(text2, ignore_whitespace, ignore_case)

        return difflib.SequenceMatcher(None, norm_text1, norm_text2).ratio()


def render_diff_options() -> Tuple[bool, bool, str]:
    """渲染对比选项"""
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        ignore_whitespace = st.checkbox("忽略空白字符", help="忽略空格、制表符等空白字符的差异")

    with col2:
        ignore_case = st.checkbox("忽略大小写", help="忽略字母大小写的差异")

    with col3:
        diff_mode = st.selectbox(
            "对比精度",
            ["字符级别", "单词级别", "行级别"],
            index=0,
            help="选择差异对比的精度级别"
        )

    with col4:
        if st.button("🔄 交换文本", help="交换左右两个文本的位置"):
            if 'text1' in st.session_state and 'text2' in st.session_state:
                st.session_state.text1, st.session_state.text2 = st.session_state.text2, st.session_state.text1
                st.rerun()

    return ignore_whitespace, ignore_case, diff_mode


def render_text_inputs_with_diff(ignore_whitespace: bool, ignore_case: bool, diff_mode: str) -> Tuple[str, str]:
    """渲染带差异显示的文本输入区域"""
    col1, col2 = st.columns(2)

    # 获取文本输入
    with col1:
        st.markdown("**📝文本1**")
        text1 = st.text_area(
            "输入第一个文本",
            height=200,
            label_visibility="collapsed",
            key="text1"
        )

    with col2:
        st.markdown("**📝文本2**")
        text2 = st.text_area(
            "输入第二个文本",
            height=200,
            label_visibility="collapsed",
            key="text2"
        )

    # 显示精确的差异对比
    if text1 and text2:

        if diff_mode == "字符级别":
            render_char_level_diff(text1, text2, ignore_whitespace, ignore_case)
        elif diff_mode == "单词级别":
            render_word_level_diff(text1, text2, ignore_whitespace, ignore_case)
        else:  # 行级别
            render_line_level_diff(text1, text2, ignore_whitespace, ignore_case)

    return text1, text2


def render_char_level_diff(text1: str, text2: str, ignore_whitespace: bool, ignore_case: bool):
    """渲染字符级别的差异对比"""
    norm_text1 = TextDiffer.normalize_text(text1, ignore_whitespace, ignore_case)
    norm_text2 = TextDiffer.normalize_text(text2, ignore_whitespace, ignore_case)

    lines1 = norm_text1.splitlines()
    lines2 = norm_text2.splitlines()

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**📝 文本1**")
        diff_html1 = ['<div style="font-family: \'Courier New\', monospace; font-size: 13px; line-height: 1.6; background-color: #ffffff; color: #333333; border: 1px solid #d0d0d0; border-radius: 4px; padding: 12px; max-height: 400px; overflow-y: auto; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">']

        max_lines = max(len(lines1), len(lines2))
        for i in range(max_lines):
            line1 = lines1[i] if i < len(lines1) else ""
            line2 = lines2[i] if i < len(lines2) else ""

            if line1 == line2:
                diff_html1.append(f'<div style="margin: 2px 0; padding: 4px 6px; border-radius: 2px;"><span style="color: #666; margin-right: 10px; user-select: none; font-weight: bold;">{i+1:3d}</span><span style="color: #333;">{html.escape(line1)}</span></div>')
            else:
                diff1, _ = TextDiffer.get_char_level_diff(line1, line2)
                diff_html1.append(f'<div style="margin: 2px 0; padding: 4px 6px; background-color: #fef5f5; border-left: 3px solid #f44336; border-radius: 2px;"><span style="color: #666; margin-right: 10px; user-select: none; font-weight: bold;">{i+1:3d}</span><span style="color: #333;">{diff1}</span></div>')

        diff_html1.append('</div>')
        st.markdown(''.join(diff_html1), unsafe_allow_html=True)

    with col2:
        st.markdown("**📝 文本2**")
        diff_html2 = ['<div style="font-family: \'Courier New\', monospace; font-size: 13px; line-height: 1.6; background-color: #ffffff; color: #333333; border: 1px solid #d0d0d0; border-radius: 4px; padding: 12px; max-height: 400px; overflow-y: auto; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">']

        max_lines = max(len(lines1), len(lines2))
        for i in range(max_lines):
            line1 = lines1[i] if i < len(lines1) else ""
            line2 = lines2[i] if i < len(lines2) else ""

            if line1 == line2:
                diff_html2.append(f'<div style="margin: 2px 0; padding: 4px 6px; border-radius: 2px;"><span style="color: #666; margin-right: 10px; user-select: none; font-weight: bold;">{i+1:3d}</span><span style="color: #333;">{html.escape(line2)}</span></div>')
            else:
                _, diff2 = TextDiffer.get_char_level_diff(line1, line2)
                diff_html2.append(f'<div style="margin: 2px 0; padding: 4px 6px; background-color: #f5fef5; border-left: 3px solid #4caf50; border-radius: 2px;"><span style="color: #666; margin-right: 10px; user-select: none; font-weight: bold;">{i+1:3d}</span><span style="color: #333;">{diff2}</span></div>')

        diff_html2.append('</div>')
        st.markdown(''.join(diff_html2), unsafe_allow_html=True)


def render_word_level_diff(text1: str, text2: str, ignore_whitespace: bool, ignore_case: bool):
    """渲染单词级别的差异对比"""
    norm_text1 = TextDiffer.normalize_text(text1, ignore_whitespace, ignore_case)
    norm_text2 = TextDiffer.normalize_text(text2, ignore_whitespace, ignore_case)

    lines1 = norm_text1.splitlines()
    lines2 = norm_text2.splitlines()

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**📄 文本1**")
        diff_html1 = ['<div style="font-family: \'Courier New\', monospace; font-size: 13px; line-height: 1.6; background-color: #ffffff; color: #333333; border: 1px solid #d0d0d0; border-radius: 4px; padding: 12px; max-height: 400px; overflow-y: auto; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">']

        max_lines = max(len(lines1), len(lines2))
        for i in range(max_lines):
            line1 = lines1[i] if i < len(lines1) else ""
            line2 = lines2[i] if i < len(lines2) else ""

            if line1 == line2:
                diff_html1.append(f'<div style="margin: 2px 0; padding: 4px 6px; border-radius: 2px;"><span style="color: #666; margin-right: 10px; user-select: none; font-weight: bold;">{i+1:3d}</span><span style="color: #333;">{html.escape(line1)}</span></div>')
            else:
                diff1, _ = TextDiffer.get_word_level_diff(line1, line2)
                diff_html1.append(f'<div style="margin: 2px 0; padding: 4px 6px; background-color: #fef5f5; border-left: 3px solid #f44336; border-radius: 2px;"><span style="color: #666; margin-right: 10px; user-select: none; font-weight: bold;">{i+1:3d}</span><span style="color: #333;">{diff1}</span></div>')

        diff_html1.append('</div>')
        st.markdown(''.join(diff_html1), unsafe_allow_html=True)

    with col2:
        st.markdown("**📄 文本2**")
        diff_html2 = ['<div style="font-family: \'Courier New\', monospace; font-size: 13px; line-height: 1.6; background-color: #ffffff; color: #333333; border: 1px solid #d0d0d0; border-radius: 4px; padding: 12px; max-height: 400px; overflow-y: auto; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">']

        max_lines = max(len(lines1), len(lines2))
        for i in range(max_lines):
            line1 = lines1[i] if i < len(lines1) else ""
            line2 = lines2[i] if i < len(lines2) else ""

            if line1 == line2:
                diff_html2.append(f'<div style="margin: 2px 0; padding: 4px 6px; border-radius: 2px;"><span style="color: #666; margin-right: 10px; user-select: none; font-weight: bold;">{i+1:3d}</span><span style="color: #333;">{html.escape(line2)}</span></div>')
            else:
                _, diff2 = TextDiffer.get_word_level_diff(line1, line2)
                diff_html2.append(f'<div style="margin: 2px 0; padding: 4px 6px; background-color: #f5fef5; border-left: 3px solid #4caf50; border-radius: 2px;"><span style="color: #666; margin-right: 10px; user-select: none; font-weight: bold;">{i+1:3d}</span><span style="color: #333;">{diff2}</span></div>')

        diff_html2.append('</div>')
        st.markdown(''.join(diff_html2), unsafe_allow_html=True)


def render_line_level_diff(text1: str, text2: str, ignore_whitespace: bool, ignore_case: bool):
    """渲染行级别的差异对比"""
    norm_text1 = TextDiffer.normalize_text(text1, ignore_whitespace, ignore_case)
    norm_text2 = TextDiffer.normalize_text(text2, ignore_whitespace, ignore_case)

    lines1 = norm_text1.splitlines()
    lines2 = norm_text2.splitlines()

    matcher = difflib.SequenceMatcher(None, lines1, lines2)

    st.markdown("**📄 行级别差异对比**")
    diff_html = ['<div style="font-family: \'Courier New\', monospace; font-size: 13px; line-height: 1.6; background-color: #ffffff; color: #333333; border: 1px solid #d0d0d0; border-radius: 4px; padding: 12px; max-height: 500px; overflow-y: auto; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">']

    line_num1 = 1
    line_num2 = 1

    for tag, i1, i2, j1, j2 in matcher.get_opcodes():
        if tag == 'equal':
            for i in range(i1, i2):
                escaped_line = html.escape(lines1[i])
                diff_html.append(f'<div style="margin: 1px 0; padding: 6px 10px; border-radius: 2px;"><span style="color: #666; margin-right: 10px; user-select: none; font-weight: bold;">{line_num1:3d}</span><span style="color: #666; margin-right: 10px; user-select: none; font-weight: bold;">{line_num2:3d}</span><span style="color: #333;">{escaped_line}</span></div>')
                line_num1 += 1
                line_num2 += 1
        elif tag == 'delete':
            for i in range(i1, i2):
                escaped_line = html.escape(lines1[i])
                diff_html.append(f'<div style="margin: 1px 0; padding: 6px 10px; background-color: #ffebee; border-left: 4px solid #f44336; border-radius: 2px;"><span style="color: #666; margin-right: 10px; user-select: none; font-weight: bold;">{line_num1:3d}</span><span style="color: #666; margin-right: 10px; user-select: none; font-weight: bold;">---</span><span style="color: #d32f2f; text-decoration: line-through; font-weight: bold;">- {escaped_line}</span></div>')
                line_num1 += 1
        elif tag == 'insert':
            for j in range(j1, j2):
                escaped_line = html.escape(lines2[j])
                diff_html.append(f'<div style="margin: 1px 0; padding: 6px 10px; background-color: #e8f5e8; border-left: 4px solid #4caf50; border-radius: 2px;"><span style="color: #666; margin-right: 10px; user-select: none; font-weight: bold;">---</span><span style="color: #666; margin-right: 10px; user-select: none; font-weight: bold;">{line_num2:3d}</span><span style="color: #388e3c; font-weight: bold;">+ {escaped_line}</span></div>')
                line_num2 += 1
        elif tag == 'replace':
            for i in range(i1, i2):
                escaped_line = html.escape(lines1[i])
                diff_html.append(f'<div style="margin: 1px 0; padding: 6px 10px; background-color: #fff3e0; border-left: 4px solid #ff9800; border-radius: 2px;"><span style="color: #666; margin-right: 10px; user-select: none; font-weight: bold;">{line_num1:3d}</span><span style="color: #666; margin-right: 10px; user-select: none; font-weight: bold;">---</span><span style="color: #f57c00; text-decoration: line-through; font-weight: bold;">- {escaped_line}</span></div>')
                line_num1 += 1
            for j in range(j1, j2):
                escaped_line = html.escape(lines2[j])
                diff_html.append(f'<div style="margin: 1px 0; padding: 6px 10px; background-color: #fff3e0; border-left: 4px solid #ff9800; border-radius: 2px;"><span style="color: #666; margin-right: 10px; user-select: none; font-weight: bold;">---</span><span style="color: #666; margin-right: 10px; user-select: none; font-weight: bold;">{line_num2:3d}</span><span style="color: #f57c00; font-weight: bold;">+ {escaped_line}</span></div>')
                line_num2 += 1

    diff_html.append('</div>')
    st.markdown(''.join(diff_html), unsafe_allow_html=True)


def render():
    """主渲染函数"""
    st.title("📝 文本对比工具")

    # 渲染对比选项
    ignore_whitespace, ignore_case, diff_mode = render_diff_options()

    # 渲染文本输入和差异显示
    render_text_inputs_with_diff(ignore_whitespace, ignore_case, diff_mode)


if __name__ == "__main__":
    render()
