#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/21
# <AUTHOR> Kai

import streamlit as st
import hashlib
import base64
import hmac
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


def render():
    """渲染加密解密工具页面"""
    st.header("🔐 加密解密工具")
    
    # 选择加密类型
    encryption_type = st.selectbox(
        "选择加密类型",
        ["MD5", "SHA1", "SHA256", "SHA512", "Base64", "AES对称加密", "HMAC"]
    )
    
    if encryption_type in ["MD5", "SHA1", "SHA256", "SHA512"]:
        render_hash_encryption(encryption_type)
    elif encryption_type == "Base64":
        render_base64_encryption()
    elif encryption_type == "AES对称加密":
        render_aes_encryption()
    elif encryption_type == "HMAC":
        render_hmac_encryption()


def render_hash_encryption(hash_type):
    """渲染哈希加密"""
    st.subheader(f"{hash_type} 哈希加密")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 输入")
        input_text = st.text_area("请输入要加密的文本", height=150)
        
        if st.button("🔒 加密", use_container_width=True):
            if input_text:
                try:
                    if hash_type == "MD5":
                        result = hashlib.md5(input_text.encode()).hexdigest()
                    elif hash_type == "SHA1":
                        result = hashlib.sha1(input_text.encode()).hexdigest()
                    elif hash_type == "SHA256":
                        result = hashlib.sha256(input_text.encode()).hexdigest()
                    elif hash_type == "SHA512":
                        result = hashlib.sha512(input_text.encode()).hexdigest()
                    
                    st.session_state.encryption_result = result
                    st.success("加密成功！")
                except Exception as e:
                    st.error(f"加密失败: {e}")
            else:
                st.warning("请输入要加密的文本")
    
    with col2:
        st.markdown("### 输出")
        if hasattr(st.session_state, 'encryption_result'):
            st.text_area("加密结果", value=st.session_state.encryption_result, height=150)
            if st.button("📋 复制结果"):
                st.success("结果已复制到剪贴板！")
        else:
            st.text_area("加密结果", value="", height=150, disabled=True)


def render_base64_encryption():
    """渲染Base64编码解码"""
    st.subheader("Base64 编码/解码")
    
    operation = st.radio("选择操作", ["编码", "解码"], horizontal=True)
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 输入")
        input_text = st.text_area("请输入文本", height=150)
        
        if st.button(f"🔄 {operation}", use_container_width=True):
            if input_text:
                try:
                    if operation == "编码":
                        result = base64.b64encode(input_text.encode()).decode()
                    else:  # 解码
                        result = base64.b64decode(input_text.encode()).decode()
                    
                    st.session_state.base64_result = result
                    st.success(f"{operation}成功！")
                except Exception as e:
                    st.error(f"{operation}失败: {e}")
            else:
                st.warning("请输入文本")
    
    with col2:
        st.markdown("### 输出")
        if hasattr(st.session_state, 'base64_result'):
            st.text_area("结果", value=st.session_state.base64_result, height=150)
            if st.button("📋 复制结果"):
                st.success("结果已复制到剪贴板！")
        else:
            st.text_area("结果", value="", height=150, disabled=True)


def render_aes_encryption():
    """渲染AES对称加密"""
    st.subheader("AES 对称加密/解密")
    
    operation = st.radio("选择操作", ["加密", "解密"], horizontal=True)
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 输入")
        password = st.text_input("密码", type="password", help="用于生成加密密钥")
        input_text = st.text_area("请输入文本", height=120)
        
        if st.button(f"🔐 {operation}", use_container_width=True):
            if input_text and password:
                try:
                    # 生成密钥
                    password_bytes = password.encode()
                    salt = b'salt_1234567890'  # 在实际应用中应该使用随机salt
                    kdf = PBKDF2HMAC(
                        algorithm=hashes.SHA256(),
                        length=32,
                        salt=salt,
                        iterations=100000,
                    )
                    key = base64.urlsafe_b64encode(kdf.derive(password_bytes))
                    fernet = Fernet(key)
                    
                    if operation == "加密":
                        result = fernet.encrypt(input_text.encode()).decode()
                    else:  # 解密
                        result = fernet.decrypt(input_text.encode()).decode()
                    
                    st.session_state.aes_result = result
                    st.success(f"{operation}成功！")
                except Exception as e:
                    st.error(f"{operation}失败: {e}")
            else:
                st.warning("请输入密码和文本")
    
    with col2:
        st.markdown("### 输出")
        if hasattr(st.session_state, 'aes_result'):
            st.text_area("结果", value=st.session_state.aes_result, height=150)
            if st.button("📋 复制结果"):
                st.success("结果已复制到剪贴板！")
        else:
            st.text_area("结果", value="", height=150, disabled=True)


def render_hmac_encryption():
    """渲染HMAC加密"""
    st.subheader("HMAC 消息认证码")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 输入")
        secret_key = st.text_input("密钥", type="password")
        message = st.text_area("消息内容", height=120)
        hash_algorithm = st.selectbox("哈希算法", ["SHA256", "SHA1", "MD5"])
        
        if st.button("🔒 生成HMAC", use_container_width=True):
            if secret_key and message:
                try:
                    key_bytes = secret_key.encode()
                    message_bytes = message.encode()
                    
                    if hash_algorithm == "SHA256":
                        result = hmac.new(key_bytes, message_bytes, hashlib.sha256).hexdigest()
                    elif hash_algorithm == "SHA1":
                        result = hmac.new(key_bytes, message_bytes, hashlib.sha1).hexdigest()
                    elif hash_algorithm == "MD5":
                        result = hmac.new(key_bytes, message_bytes, hashlib.md5).hexdigest()
                    
                    st.session_state.hmac_result = result
                    st.success("HMAC生成成功！")
                except Exception as e:
                    st.error(f"HMAC生成失败: {e}")
            else:
                st.warning("请输入密钥和消息内容")
    
    with col2:
        st.markdown("### 输出")
        if hasattr(st.session_state, 'hmac_result'):
            st.text_area("HMAC结果", value=st.session_state.hmac_result, height=150)
            if st.button("📋 复制结果"):
                st.success("结果已复制到剪贴板！")
        else:
            st.text_area("HMAC结果", value="", height=150, disabled=True)
    
    # HMAC验证
    st.markdown("### HMAC验证")
    col3, col4 = st.columns(2)
    
    with col3:
        verify_hmac = st.text_input("待验证的HMAC")
    
    with col4:
        if st.button("🔍 验证HMAC", use_container_width=True):
            if verify_hmac and hasattr(st.session_state, 'hmac_result'):
                if verify_hmac.lower() == st.session_state.hmac_result.lower():
                    st.success("✅ HMAC验证通过！")
                else:
                    st.error("❌ HMAC验证失败！")
            else:
                st.warning("请先生成HMAC并输入待验证的值")


if __name__ == "__main__":
    render()
