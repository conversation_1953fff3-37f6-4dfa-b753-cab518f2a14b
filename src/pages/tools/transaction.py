#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/21
# <AUTHOR> Kai


import streamlit as st
import math


def render():
    # 功能切换选项卡
    tab1, tab2, tab3, tab4 = st.tabs(["提现", "充值", "代付", "代收"])

    with tab1:
        # 前置配置
        st.write("前置配置")
        pre_cols = st.columns(4)
        with pre_cols[0]:
            channel_exchange_rate = st.number_input(
                "渠道汇率 ⭐",
                min_value=0.0001,
                step=0.0001,
                format="%.4f",
                value=1.0,  # 默认1.0
                key="channel_rate"
            )
        with pre_cols[1]:
            channel_loss_rate = st.number_input(
                "渠道规则汇损(%)",
                min_value=0.0,
                step=0.01,
                format="%.2f",
                value=0.0,  # 默认0%
                key="channel_loss"
            ) / 100  # 转换为小数
        with pre_cols[2]:
            epay_exchange_rate = st.number_input(
                "EPAY业务币种汇率 ⭐",
                min_value=0.0001,
                step=0.0001,
                format="%.4f",
                value=1.0,  # 默认1.0
                key="epay_rate"
            )
        with pre_cols[3]:
            business_loss_rate = st.number_input(
                "业务币种汇损(%)",
                min_value=0.0,
                step=0.01,
                format="%.2f",
                value=0.0,  # 默认0%
                key="business_loss"
            ) / 100  # 转换为小数

        # st.divider()

        # 手续费配置（总手续费和渠道手续费在同一行）
        fee_row = st.columns(3)  # 总手续费和渠道手续费各占一列

        # 总手续费配置（百分比+固定金额）
        with fee_row[0]:
            st.write("总手续费")
            total_fee_cols = st.columns(2)
            with total_fee_cols[0]:
                total_fee_rate = st.number_input(
                    "百分比(%)",
                    min_value=0.0,
                    step=0.01,
                    format="%.2f",
                    value=0.0,  # 默认0
                    key="total_fee_rate"
                ) / 100
            with total_fee_cols[1]:
                total_fee_fixed = st.number_input(
                    "固定金额",
                    min_value=0.0,
                    step=0.01,
                    format="%.2f",
                    value=0.0,  # 默认0
                    key="total_fee_fixed"
                )

        # 渠道手续费配置（百分比+固定金额）
        with fee_row[1]:
            st.write("渠道手续费")
            channel_fee_cols = st.columns(2)
            with channel_fee_cols[0]:
                channel_fee_rate = st.number_input(
                    "百分比(%)",
                    min_value=0.0,
                    step=0.01,
                    format="%.2f",
                    value=0.0,  # 默认0
                    key="channel_fee_rate"
                ) / 100
            with channel_fee_cols[1]:
                channel_fee_fixed = st.number_input(
                    "固定金额",
                    min_value=0.0,
                    step=0.01,
                    format="%.2f",
                    value=0.0,  # 默认0
                    key="channel_fee_fixed"
                )

        with fee_row[2]:
            st.write("扣费模式")
            select_box_col = st.columns(2)
            with select_box_col[0]:
                deduct_mode = st.selectbox(
                    "扣费模式",
                    ["内扣"],
                    disabled=False,
                    key="deduct_mode"
                )

        # st.divider()

        # 其他配置和金额输入
        other_amount_cols = st.columns(3)
        with other_amount_cols[0]:
            withdrawal_amount = st.number_input(
                "提现金额 ⭐",
                step=0.01,
                format="%.2f",
                key="withdrawal_amount"
            )

        with other_amount_cols[1]:
            target_amount = st.number_input(
                "目标金额 ⭐",
                step=0.01,
                format="%.2f",
                key="target_amount"
            )

        # st.divider()

        channel_to_settlement_base = 1 / channel_exchange_rate
        channel_to_settlement_loss = channel_to_settlement_base * (1 + channel_loss_rate)
        settlement_to_business_loss = channel_to_settlement_base * (1 + business_loss_rate)
        user_exchange_rate = channel_to_settlement_base * (1 + channel_loss_rate)

        # 计算提现金额相关结果
        total_fee = withdrawal_amount * total_fee_rate + total_fee_fixed
        settlement_amount = (
                                    withdrawal_amount - total_fee) / settlement_to_business_loss if settlement_to_business_loss != 0 else 0
        channel_fee = settlement_amount * channel_fee_rate + channel_fee_fixed
        platform_fee = total_fee - channel_fee * settlement_to_business_loss
        deduction_amount = withdrawal_amount
        real_settlement_amount = target_amount * channel_exchange_rate
        receipt_amount = (withdrawal_amount - total_fee) / user_exchange_rate if user_exchange_rate != 0 else 0
        receipt_amount = math.floor(receipt_amount * 100) / 100  # 舍位处理
        business_currency_gain = (
                                         withdrawal_amount - platform_fee) / epay_exchange_rate * business_loss_rate if epay_exchange_rate != 0 else 0

        # 计算目标金额相关结果
        if 1 - total_fee_rate == 0:
            target_withdrawal_amount = 0.0
        else:
            target_withdrawal_amount = (target_amount * user_exchange_rate + total_fee_fixed) / (
                    1 - total_fee_rate)

        target_total_fee = target_withdrawal_amount * total_fee_rate + total_fee_fixed
        target_settlement_amount = (
                                           target_withdrawal_amount - target_total_fee) / settlement_to_business_loss if settlement_to_business_loss != 0 else 0
        target_channel_fee = target_settlement_amount * channel_fee_rate + channel_fee_fixed
        target_platform_fee = target_total_fee - target_channel_fee * settlement_to_business_loss
        target_real_settlement = target_amount * channel_exchange_rate
        target_business_gain = (
                                       target_withdrawal_amount - target_platform_fee) / epay_exchange_rate * business_loss_rate if epay_exchange_rate != 0 else 0

        # 汇率结果（同一行）
        st.write("汇率计算结果")
        rate_cols = st.columns(4)
        with rate_cols[0]:
            st.text_input("渠道到结算原始汇率", f"{channel_to_settlement_base:.6f}", disabled=True, key="r1")
        with rate_cols[1]:
            st.text_input("渠道到结算汇损汇率", f"{channel_to_settlement_loss:.6f}", disabled=True, key="r2")
        with rate_cols[2]:
            st.text_input("结算到业务汇损汇率", f"{settlement_to_business_loss:.6f}", disabled=True, key="r3")
        with rate_cols[3]:
            st.text_input("用户汇率", f"{user_exchange_rate:.6f}", disabled=True, key="r4")

        # 提现金额计算结果
        st.write("订单提现金额计算结果")
        withdraw_cols_1 = st.columns(4)
        with withdraw_cols_1[0]:
            st.text_input("收款金额", f"{receipt_amount:.2f}", disabled=True, key="w1")
        with withdraw_cols_1[1]:
            st.text_input("总手续费", f"{total_fee:.2f}", disabled=True, key="w2")
        with withdraw_cols_1[2]:
            st.text_input("渠道手续费", f"{channel_fee:.2f}", disabled=True, key="w3")
        with withdraw_cols_1[3]:
            st.text_input("平台手续费", f"{platform_fee:.2f}", disabled=True, key="w4")

        withdraw_cols_2 = st.columns(4)
        with withdraw_cols_2[0]:
            st.text_input("扣款用户账户金额", f"{deduction_amount:.2f}", disabled=True, key="w5")
        with withdraw_cols_2[1]:
            st.text_input("结算金额", f"{settlement_amount:.2f}", disabled=True, key="w6")
        with withdraw_cols_2[2]:
            st.text_input("真实结算金额", f"{real_settlement_amount:.2f}", disabled=True, key="w7")
        with withdraw_cols_2[3]:
            st.text_input("业务币种汇损收益", f"{business_currency_gain:.2f}", disabled=True, key="w8")

        # 目标金额计算结果
        st.write("订单目标金额计算结果")
        target_cols = st.columns(4)
        with target_cols[0]:
            st.text_input("提现金额", f"{target_withdrawal_amount:.2f}", disabled=True, key="t1")
            st.text_input("付款金额", f"{target_amount:.2f}", disabled=True, key="t5")
        with target_cols[1]:
            st.text_input("总手续费", f"{target_total_fee:.2f}", disabled=True, key="t2")
            st.text_input("结算金额", f"{target_settlement_amount:.2f}", disabled=True, key="t6")
        with target_cols[2]:
            st.text_input("渠道手续费", f"{target_channel_fee:.2f}", disabled=True, key="t3")
            st.text_input("真实结算金额", f"{target_real_settlement:.2f}", disabled=True, key="t7")
        with target_cols[3]:
            st.text_input("平台手续费", f"{target_platform_fee:.2f}", disabled=True, key="t4")
            st.text_input("业务币种汇损收益", f"{target_business_gain:.2f}", disabled=True, key="t8")

    # 其他功能标签页
    with tab2:
        st.write("充值金额计算")
        st.info("充值功能即将上线，敬请期待...")

    with tab3:
        st.write("代付金额计算")
        st.info("代付功能即将上线，敬请期待...")

    with tab4:
        st.write("代收金额计算")
        st.info("代收功能即将上线，敬请期待...")


# 运行应用
if __name__ == "__main__":
    render()
