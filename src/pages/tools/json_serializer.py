"""
JSON序列化工具
支持JSON格式化、压缩、验证、转换等功能
"""

import streamlit as st
import json
import yaml
import xml.etree.ElementTree as ET
from xml.dom import minidom
from typing import Dict, Any, Tuple, Optional
import re


class JSONProcessor:
    """JSON处理器"""

    @staticmethod
    def format_json(json_str: str, indent: int = 2) -> Tuple[bool, str, str]:
        """格式化JSON字符串"""
        try:
            # 解析JSON
            parsed = json.loads(json_str)
            # 格式化输出
            formatted = json.dumps(parsed, indent=indent, ensure_ascii=False, sort_keys=True)
            return True, formatted, "JSON格式化成功"
        except json.JSONDecodeError as e:
            return False, "", f"JSON格式错误: {str(e)}"
        except Exception as e:
            return False, "", f"处理错误: {str(e)}"

    @staticmethod
    def compress_json(json_str: str) -> <PERSON><PERSON>[bool, str, str]:
        """压缩JSON字符串"""
        try:
            parsed = json.loads(json_str)
            compressed = json.dumps(parsed, separators=(',', ':'), ensure_ascii=False)
            return True, compressed, "JSON压缩成功"
        except json.JSONDecodeError as e:
            return False, "", f"JSON格式错误: {str(e)}"
        except Exception as e:
            return False, "", f"处理错误: {str(e)}"

    @staticmethod
    def validate_json(json_str: str) -> Tuple[bool, str, Dict[str, Any]]:
        """验证JSON格式"""
        try:
            parsed = json.loads(json_str)

            # 统计信息
            stats = JSONProcessor._get_json_stats(parsed)

            return True, "JSON格式正确", stats
        except json.JSONDecodeError as e:
            # 解析错误信息
            error_info = JSONProcessor._parse_json_error(str(e), json_str)
            return False, f"JSON格式错误: {str(e)}", error_info
        except Exception as e:
            return False, f"验证错误: {str(e)}", {}

    @staticmethod
    def _parse_json_error(error_msg: str, json_str: str) -> Dict[str, Any]:
        """解析JSON错误信息"""
        import re

        # 提取行号和列号
        line_match = re.search(r'line (\d+)', error_msg)
        col_match = re.search(r'column (\d+)', error_msg)

        error_info = {
            'has_error': True,
            'error_line': int(line_match.group(1)) if line_match else None,
            'error_column': int(col_match.group(1)) if col_match else None,
            'error_message': error_msg
        }

        # 如果有行号信息，找到错误位置
        if error_info['error_line']:
            lines = json_str.split('\n')
            if error_info['error_line'] <= len(lines):
                error_info['error_line_content'] = lines[error_info['error_line'] - 1]

        return error_info

    @staticmethod
    def _get_json_stats(obj: Any) -> Dict[str, Any]:
        """获取JSON统计信息"""

        def count_items(obj, stats):
            if isinstance(obj, dict):
                stats['objects'] += 1
                stats['keys'] += len(obj)
                for value in obj.values():
                    count_items(value, stats)
            elif isinstance(obj, list):
                stats['arrays'] += 1
                stats['array_items'] += len(obj)
                for item in obj:
                    count_items(item, stats)
            elif isinstance(obj, str):
                stats['strings'] += 1
            elif isinstance(obj, (int, float)):
                stats['numbers'] += 1
            elif isinstance(obj, bool):
                stats['booleans'] += 1
            elif obj is None:
                stats['nulls'] += 1

        stats = {
            'objects': 0, 'arrays': 0, 'keys': 0, 'array_items': 0,
            'strings': 0, 'numbers': 0, 'booleans': 0, 'nulls': 0
        }
        count_items(obj, stats)
        return stats


class JSONConverter:
    """JSON转换器"""

    @staticmethod
    def json_to_yaml(json_str: str) -> Tuple[bool, str, str]:
        """JSON转YAML"""
        try:
            parsed = json.loads(json_str)
            yaml_str = yaml.dump(parsed, default_flow_style=False, allow_unicode=True, indent=2)
            return True, yaml_str, "转换为YAML成功"
        except json.JSONDecodeError as e:
            return False, "", f"JSON格式错误: {str(e)}"
        except Exception as e:
            return False, "", f"转换错误: {str(e)}"

    @staticmethod
    def json_to_xml(json_str: str) -> Tuple[bool, str, str]:
        """JSON转XML"""
        try:
            parsed = json.loads(json_str)
            xml_str = JSONConverter._dict_to_xml(parsed, "root")
            return True, xml_str, "转换为XML成功"
        except json.JSONDecodeError as e:
            return False, "", f"JSON格式错误: {str(e)}"
        except Exception as e:
            return False, "", f"转换错误: {str(e)}"

    @staticmethod
    def _dict_to_xml(obj: Any, root_name: str = "root") -> str:
        """将字典转换为XML"""

        def build_xml(obj, parent):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    elem = ET.SubElement(parent, str(key))
                    build_xml(value, elem)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    elem = ET.SubElement(parent, f"item_{i}")
                    build_xml(item, elem)
            else:
                parent.text = str(obj)

        root = ET.Element(root_name)
        build_xml(obj, root)

        # 格式化XML
        rough_string = ET.tostring(root, encoding='unicode')
        reparsed = minidom.parseString(rough_string)
        return reparsed.toprettyxml(indent="  ")


def on_input_change():
    """输入变化时的回调函数"""
    # 强制重新渲染
    pass


def render_input_panel():
    # 输入框 - 使用on_change实现实时响应
    json_input = st.text_area(
        "📝 JSON字符串",
        value=st.session_state.get('json_input', ''),
        height=350,
        key="json_input",
        on_change=on_input_change
    )

    return json_input


def render_output_panel(json_input: str, operation: str, indent: int):
    """渲染输出面板"""

    # 如果没有输入，显示空的结果区域
    if not json_input.strip():
        st.text_area(
            "📤 序列化结果",
            value="",
            height=350,
            disabled=False
        )
        return

    # 首先验证JSON格式
    is_valid, validation_msg, validation_info = JSONProcessor.validate_json(json_input)

    # 如果JSON无效，显示错误信息和原始数据
    if not is_valid:
        render_error_result(json_input, validation_msg, validation_info)
        return

    # 处理有效的JSON - 实时动态展示
    if operation == "格式化":
        render_format_result_realtime(json_input, indent)
    elif operation == "压缩":
        render_compress_result_realtime(json_input)
    elif operation == "验证":
        render_validate_result_realtime(json_input)
    elif operation == "转换为YAML":
        render_yaml_result_realtime(json_input)
    elif operation == "转换为XML":
        render_xml_result_realtime(json_input)


def render_error_result(json_input: str, error_msg: str, error_info: Dict[str, Any]):
    """渲染错误结果"""
    st.error(f"❌ {error_msg}")

    # 显示原始数据，并高亮错误位置
    st.markdown("**📋 原始数据（错误位置已标记）**")

    if error_info.get('has_error') and error_info.get('error_line'):
        # 按行分割文本
        lines = json_input.split('\n')
        error_line_num = error_info.get('error_line', 1)
        error_col_num = error_info.get('error_column', 1)

        # 构建带错误高亮的HTML
        highlighted_html = [
            '<div style="font-family: monospace; font-size: 13px; line-height: 1.4; background-color: #fafafa; border: 1px solid #e0e0e0; border-radius: 4px; padding: 10px; max-height: 300px; overflow-y: auto;">']

        for i, line in enumerate(lines, 1):
            line_escaped = line.replace('<', '&lt;').replace('>', '&gt;').replace('&', '&amp;')

            if i == error_line_num:
                # 错误行，高亮显示
                if error_col_num and error_col_num <= len(line):
                    # 高亮具体错误位置
                    before_error = line_escaped[:error_col_num - 1]
                    error_char = line_escaped[error_col_num - 1:error_col_num] if error_col_num <= len(
                        line_escaped) else ''
                    after_error = line_escaped[error_col_num:]

                    highlighted_line = f'{before_error}<span style="background-color: #ff6b6b; color: white; padding: 1px 2px; border-radius: 2px;">{error_char}</span>{after_error}'
                else:
                    highlighted_line = line_escaped

                highlighted_html.append(
                    f'<div style="background-color: #ffebee; padding: 4px 8px; margin: 1px 0; border-left: 4px solid #f44336;"><span style="color: #666; margin-right: 10px; user-select: none;">{i:3d}</span><span style="color: #d32f2f;">{highlighted_line}</span></div>')
            else:
                # 普通行
                highlighted_html.append(
                    f'<div style="padding: 4px 8px; margin: 1px 0;"><span style="color: #666; margin-right: 10px; user-select: none;">{i:3d}</span><span>{line_escaped}</span></div>')

        highlighted_html.append('</div>')
        st.markdown(''.join(highlighted_html), unsafe_allow_html=True)

        # 显示错误详情
        if error_info.get('error_line_content'):
            st.markdown("**🎯 错误详情**")
            col1, col2 = st.columns(2)
            with col1:
                st.text(f"错误行: {error_line_num}")
                st.text(f"错误列: {error_col_num}")
            with col2:
                st.text("错误行内容:")
                st.code(error_info['error_line_content'], language='text')
    else:
        # 没有具体位置信息，直接显示原始数据
        st.text_area(
            "原始数据",
            value=json_input,
            height=300,
            label_visibility="collapsed"
        )

    # 提供修复建议
    render_fix_suggestions(error_msg)


def render_fix_suggestions(error_msg: str):
    """渲染修复建议"""
    st.markdown("**💡 修复建议**")

    suggestions = []
    error_lower = error_msg.lower()

    if "expecting ',' delimiter" in error_lower:
        suggestions.append("• 检查对象或数组中是否缺少逗号分隔符")
        suggestions.append("• 确保最后一个元素后面没有多余的逗号")

    if "expecting ':' delimiter" in error_lower:
        suggestions.append("• 检查对象中键值对是否缺少冒号")
        suggestions.append("• 确保键名使用双引号包围")

    if "expecting property name" in error_lower:
        suggestions.append("• 检查对象的键名是否使用双引号")
        suggestions.append("• 确保没有多余的逗号")

    if "unterminated string" in error_lower:
        suggestions.append("• 检查字符串是否缺少结束的双引号")
        suggestions.append("• 检查字符串中的转义字符是否正确")

    if "expecting value" in error_lower:
        suggestions.append("• 检查是否有空的键值对")
        suggestions.append("• 确保数组或对象中的值格式正确")

    if not suggestions:
        suggestions.append("• 检查JSON语法是否符合标准格式")
        suggestions.append("• 确保使用双引号而不是单引号")
        suggestions.append("• 检查括号、方括号是否配对")

    for suggestion in suggestions:
        st.markdown(suggestion)


def render_format_result_realtime(json_input: str, indent: int):
    """实时渲染格式化结果"""
    success, result, message = JSONProcessor.format_json(json_input, indent)

    if success:
        # 直接显示结果，不显示成功消息
        st.text_area(
            "📤 格式化结果",
            value=result,
            height=350
        )
    else:
        # 这种情况不应该发生，因为已经在上层验证过了
        st.text_area(
            "📤 格式化结果",
            value="格式化失败",
            height=350
        )


def render_compress_result_realtime(json_input: str):
    """实时渲染压缩结果"""
    success, result, message = JSONProcessor.compress_json(json_input)

    if success:
        # 显示压缩统计
        original_size = len(json_input)
        compressed_size = len(result)
        compression_ratio = (1 - compressed_size / original_size) * 100 if original_size > 0 else 0

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("原始大小", f"{original_size} 字符")
        with col2:
            st.metric("压缩后大小", f"{compressed_size} 字符")
        with col3:
            st.metric("压缩率", f"{compression_ratio:.1f}%")

        # 显示结果
        st.text_area(
            "📤 压缩结果",
            value=result,
            height=255,
            disabled=False
        )
    else:
        st.text_area(
            "压缩结果",
            value="压缩失败",
            height=350,
            label_visibility="collapsed",
            disabled=False
        )


def render_validate_result_realtime(json_input: str):
    """实时渲染验证结果"""
    success, message, stats = JSONProcessor.validate_json(json_input)

    if success:
        # 显示统计信息
        if stats:
            st.markdown("**📊 JSON结构统计**")
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("对象数", stats.get('objects', 0))
                st.metric("数组数", stats.get('arrays', 0))

            with col2:
                st.metric("键数量", stats.get('keys', 0))
                st.metric("数组项", stats.get('array_items', 0))

            with col3:
                st.metric("字符串", stats.get('strings', 0))
                st.metric("数字", stats.get('numbers', 0))

            with col4:
                st.metric("布尔值", stats.get('booleans', 0))
                st.metric("空值", stats.get('nulls', 0))

        # 显示验证通过的原始JSON
        st.success(
            "✅ JSON格式正确",
        )


def render_yaml_result_realtime(json_input: str):
    """实时渲染YAML转换结果"""
    success, result, message = JSONConverter.json_to_yaml(json_input)

    if success:
        st.text_area(
            "📤 YAML结果",
            value=result,
            height=350,
            disabled=False
        )
    else:
        st.text_area(
            "YAML结果",
            value="转换失败",
            height=350,
            disabled=False
        )


def render_xml_result_realtime(json_input: str):
    """实时渲染XML转换结果"""
    success, result, message = JSONConverter.json_to_xml(json_input)

    if success:
        st.text_area(
            "📤 XML结果",
            value=result,
            height=350,
            disabled=False
        )
    else:
        st.text_area(
            "XML结果",
            value="转换失败",
            height=350,
            disabled=False
        )


def render():
    """渲染JSON序列化工具页面"""
    st.title("🔧 JSON序列化工具")

    """渲染输入面板"""
    # 操作选项和缩进设置 - 紧凑布局
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        operation = st.selectbox(
            "选择操作",
            ["格式化", "压缩", "验证", "转换为YAML", "转换为XML"],
            key="json_operation",
            on_change=on_input_change
        )

    with col2:
        indent = st.slider(
            "缩进空格数",
            1, 8, 4,
            key="format_indent",
            on_change=on_input_change
        )

    # 左右分栏布局
    col_left, col_right = st.columns(2)

    with col_left:
        json_input = render_input_panel()

    with col_right:
        render_output_panel(json_input, operation, indent)


if __name__ == "__main__":
    render()
