#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/21
# <AUTHOR> Kai


import streamlit as st
from datetime import datetime


def update_url_params():
    """更新URL参数以保持页面状态"""
    params = {"main": st.session_state.current_main}
    if st.session_state.current_sub:
        params["sub"] = st.session_state.current_sub
    st.query_params.update(params)


def create_quick_access_button(label: str, main_menu: str, sub_menu: str = None):
    """创建快速访问按钮的通用函数"""
    if st.button(label, use_container_width=True):
        st.session_state.current_main = main_menu
        st.session_state.current_sub = sub_menu
        update_url_params()
        st.rerun()


def render():
    """渲染首页"""
    # 自定义CSS样式
    st.markdown("""
    <style>
    .main-header {
        text-align: center;
        padding: 2rem 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        margin-bottom: 2rem;
        color: white;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }
    
    .main-title {
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }
    
    .main-subtitle {
        font-size: 1.3rem;
        opacity: 0.9;
        margin-bottom: 1rem;
    }
    
    .feature-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid #f0f0f0;
        margin-bottom: 1rem;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.12);
    }
    
    .feature-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        text-align: center;
    }
    
    .feature-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        text-align: center;
    }
    
    .feature-desc {
        color: #7f8c8d;
        text-align: center;
        line-height: 1.6;
    }
    
    .stats-container {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        padding: 2rem;
        border-radius: 15px;
        margin: 2rem 0;
        color: white;
    }
    
    .stat-item {
        text-align: center;
        padding: 1rem;
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        font-size: 1rem;
        opacity: 0.9;
    }
    
    .footer {
        text-align: center;
        padding: 2rem 0;
        margin-top: 3rem;
        border-top: 1px solid #eee;
        color: #7f8c8d;
    }
    
    .welcome-animation {
        animation: fadeInUp 1s ease-out;
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .quick-start {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        padding: 2rem;
        border-radius: 15px;
        margin: 2rem 0;
        color: white;
        text-align: center;
    }
    
    .time-display {
        background: rgba(255,255,255,0.1);
        padding: 1rem;
        border-radius: 10px;
        margin: 1rem 0;
        text-align: center;
        backdrop-filter: blur(10px);
    }
    </style>
    """, unsafe_allow_html=True)
    
    # 主标题区域
    st.markdown("""
    <div class="main-header welcome-animation">
        <div class="main-title">EpayTools</div>
        <div class="main-subtitle">欢迎使用易派工具平台</div>
        <div class="main-subtitle">平台愿景</div>
        <p>测试标准化 效率提升 质量监控 探活告警</p>
        <div class="time-display">
            <strong>当前时间：{}</strong>
        </div>
    </div>
    """.format(datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")), unsafe_allow_html=True)
    
    # 功能特色区域
    st.markdown("## 🌟 平台特色")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.markdown("""
        <div class="feature-card">
            <div class="feature-icon">💰</div>
            <div class="feature-title">金额计算</div>
            <div class="feature-desc">精确的金额计算工具，支持多种货币格式转换</div>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("""
        <div class="feature-card">
            <div class="feature-icon">🔐</div>
            <div class="feature-title">加密解密</div>
            <div class="feature-desc">多种加密算法支持，保障数据安全传输</div>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        st.markdown("""
        <div class="feature-card">
            <div class="feature-icon">🎭</div>
            <div class="feature-title">Mock服务</div>
            <div class="feature-desc">智能字段编辑、高级JSON编辑、自动备份恢复</div>
        </div>
        """, unsafe_allow_html=True)

    with col4:
        st.markdown("""
        <div class="feature-card">
            <div class="feature-icon">💹</div>
            <div class="feature-title">数据工厂</div>
            <div class="feature-desc">假身份数据生成、银行卡生成、测试数据批量生成</div>
        </div>
        """, unsafe_allow_html=True)
    
    # 统计信息区域
    st.markdown("""
    <div class="stats-container">
        <h3 style="text-align: center; margin-bottom: 2rem;">📊 平台数据</h3>
        <div style="display: flex; justify-content: space-around;">
            <div class="stat-item">
                <div class="stat-number">15+</div>
                <div class="stat-label">实用工具</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">24/7</div>
                <div class="stat-label">在线服务</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">100%</div>
                <div class="stat-label">开源免费</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">∞</div>
                <div class="stat-label">使用次数</div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # 快速开始区域
    st.markdown("""
    <div class="quick-start">
        <h3>🎯 快速开始</h3>
        <p>选择左侧菜单中的工具开始使用，或者点击下方按钮快速访问常用功能</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 快速访问按钮
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        create_quick_access_button("💰 金额计算", "⚒️常用工具", "金额计算")

    with col2:
        create_quick_access_button("🔐 加密解密", "⚒️常用工具", "加密解密")

    with col3:
        create_quick_access_button("✏️ Mock编辑", "🎭Mock服务(开发)", "mock编辑")

    with col4:
        create_quick_access_button("🗄️ 备份管理", "🎭Mock服务(开发)", "备份管理")

    # 第二行快速访问按钮
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        create_quick_access_button("🎭 Mock管理", "🎭Mock服务", "Mock管理")

    with col2:
        create_quick_access_button("🧪 Mock测试", "🎭Mock服务", "Mock测试")

    with col3:
        create_quick_access_button("📊 系统信息", "⚒️常用工具", "系统信息")

    with col4:
        create_quick_access_button("🎭 假身份数据", "💹数据工厂", "假身份数据")

    # 第三行快速访问按钮
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        create_quick_access_button("🚀 接口自动化", "🚀接口自动化", "接口列表")

    with col2:
        create_quick_access_button("📋 请求日志", "🎭Mock服务", "请求日志")

    with col3:
        create_quick_access_button("🔧 服务管理", "🎭Mock服务", "服务管理")

    with col4:
        create_quick_access_button("💹 动账查询", "💹数据工厂", "动账查询")
    
    # 更新说明
    with st.expander("📝 最新更新", expanded=False):
        st.markdown("""
        ### v1.2.0 (2025-07-25)
        - 🎭 新增假身份数据生成器（支持12家银行卡生成）
        - ⚙️ Mock编辑新增高级JSON编辑模式
        - 🗄️ 优化备份管理界面和列顺序
        - 🔧 Mock服务管理功能简化优化
        - 🕐 修复香港时间显示问题
        - 📊 完善数据工厂功能模块

        ### v1.0.0 (2025-07-24)
        - ✨ 新增Mock配置编辑功能
        - 🗄️ 新增备份管理系统
        - 🔧 优化金额计算工具
        - 🎨 全新UI设计
        - 🚀 性能优化提升
        - 📱 响应式布局适配
        """)
    
    # 页脚
    st.markdown("""
    <div class="footer">
        <p><strong>© 2025 深圳易派支付科技有限公司</strong></p>
    </div>
    """, unsafe_allow_html=True)

    # 添加一些动态效果
    if st.button("🎉 点击获取惊喜"):
        st.balloons()
        st.success("🎊 欢迎使用EpayTools！祝您工作愉快！")


if __name__ == "__main__":
    render()
