#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
用例管理页面
"""

import streamlit as st
import json
from datetime import datetime

from src.components.api_test.models import *
from src.components.api_test.dao import ApiTestDAO


def render():
    """渲染用例管理页面"""
    st.title("📝 用例管理")
    st.markdown("创建和管理接口测试用例")
    
    # 初始化DAO
    dao = ApiTestDAO()
    
    # 检查是否有项目
    projects = dao.get_projects()
    if not projects:
        st.warning("⚠️ 请先创建项目，然后再创建测试用例")
        if st.button("🔗 前往项目管理"):
            st.switch_page("src/pages/api_auto_test/project_management.py")
        return
    
    # 主要功能选项卡
    tab1, tab2 = st.tabs(["📋 用例列表", "➕ 创建用例"])
    
    with tab1:
        render_case_list(dao, projects)
    
    with tab2:
        render_create_case(dao, projects)


def render_case_list(dao: ApiTestDAO, projects: List[Project]):
    """渲染用例列表"""
    st.markdown("### 📋 测试用例列表")
    
    # 项目筛选
    project_options = {"": "全部项目"}
    project_options.update({p.id: p.name for p in projects})
    
    selected_project_id = st.selectbox(
        "📁 选择项目",
        options=list(project_options.keys()),
        format_func=lambda x: project_options[x],
        key="case_list_project_filter"
    )
    
    # 获取测试用例
    test_cases = dao.get_test_cases(
        project_id=selected_project_id if selected_project_id else None
    )
    
    if not test_cases:
        st.info("📝 暂无测试用例，请创建第一个用例")
        return
    
    # 搜索和筛选
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        search_term = st.text_input(
            "🔍 搜索用例",
            placeholder="输入用例名称或描述进行搜索",
            key="case_search"
        )
    
    with col2:
        status_filter = st.selectbox(
            "状态筛选",
            options=["全部", "启用", "禁用", "待审核"],
            key="case_status_filter"
        )
    
    with col3:
        priority_filter = st.selectbox(
            "优先级筛选",
            options=["全部", "高", "中", "低"],
            key="case_priority_filter"
        )
    
    # 过滤用例
    filtered_cases = test_cases
    
    if search_term:
        filtered_cases = [
            case for case in filtered_cases
            if search_term.lower() in case.name.lower() or
               search_term.lower() in (case.description or "").lower()
        ]
    
    if status_filter != "全部":
        status_map = {"启用": CaseStatus.ENABLED, "禁用": CaseStatus.DISABLED, "待审核": CaseStatus.PENDING}
        filtered_cases = [
            case for case in filtered_cases
            if case.status == status_map[status_filter]
        ]
    
    if priority_filter != "全部":
        priority_map = {"高": CasePriority.HIGH, "中": CasePriority.MEDIUM, "低": CasePriority.LOW}
        filtered_cases = [
            case for case in filtered_cases
            if case.priority == priority_map[priority_filter]
        ]
    
    if not filtered_cases:
        st.warning("🔍 没有找到符合条件的测试用例")
        return
    
    # 显示统计
    st.markdown(f"**找到 {len(filtered_cases)} 个测试用例**")
    
    # 用例列表
    for i, case in enumerate(filtered_cases):
        with st.expander(
            f"{'🔴' if case.status == CaseStatus.DISABLED else '🟢'} "
            f"{'🔥' if case.priority == CasePriority.HIGH else '🔸' if case.priority == CasePriority.MEDIUM else '🔹'} "
            f"{case.name}",
            expanded=False
        ):
            col1, col2 = st.columns([3, 1])
            
            with col1:
                st.markdown(f"**描述**: {case.description or '无描述'}")
                st.markdown(f"**请求方法**: `{case.request.method.value if hasattr(case.request.method, 'value') else case.request.method}`")
                st.markdown(f"**请求URL**: `{case.request.url}`")
                st.markdown(f"**优先级**: {case.priority.value if hasattr(case.priority, 'value') else case.priority}")
                st.markdown(f"**状态**: {case.status.value if hasattr(case.status, 'value') else case.status}")
                st.markdown(f"**标签**: {', '.join(case.tags) if case.tags else '无标签'}")
                st.markdown(f"**断言数量**: {len(case.assertions)}")
                st.markdown(f"**变量提取**: {len(case.variable_extractions)}")

                # 显示变量提取详情
                if case.variable_extractions:
                    with st.expander("🔧 变量提取详情", expanded=False):
                        for extraction in case.variable_extractions:
                            if extraction.enabled:
                                st.markdown(f"- **{extraction.variable_name}**: {extraction.extraction_method.value if hasattr(extraction.extraction_method, 'value') else extraction.extraction_method} `{extraction.expression}`")
                                st.markdown(f"  - 数据源: {extraction.source.value if hasattr(extraction.source, 'value') else extraction.source}")
                                if hasattr(extraction, 'description') and extraction.description:
                                    st.markdown(f"  - 说明: {extraction.description}")

                st.markdown(f"**创建时间**: {case.created_at.strftime('%Y-%m-%d %H:%M:%S')}")

                if case.skip:
                    st.warning(f"⏭️ **跳过原因**: {case.skip_reason}")
            
            with col2:
                # 操作按钮
                if st.button("✏️ 编辑", key=f"edit_case_{i}"):
                    st.session_state.api_test_edit_case_id = case.id
                    st.rerun()
                
                if st.button("🧪 测试", key=f"test_case_{i}"):
                    st.session_state.api_test_single_case_id = case.id
                    st.rerun()
                
                if st.button("📋 复制", key=f"copy_case_{i}"):
                    st.session_state.api_test_copy_case_id = case.id
                    st.rerun()
                
                if st.button("🗑️ 删除", key=f"delete_case_{i}"):
                    if st.session_state.get(f"confirm_delete_case_{i}", False):
                        if dao.delete_test_case(case.id):
                            st.success("✅ 用例删除成功")
                            st.rerun()
                        else:
                            st.error("❌ 用例删除失败")
                    else:
                        st.session_state[f"confirm_delete_case_{i}"] = True
                        st.warning("⚠️ 再次点击确认删除")
    
    # 处理编辑用例
    if st.session_state.get("api_test_edit_case_id"):
        render_edit_case_modal(dao, st.session_state.api_test_edit_case_id, projects)
    
    # 处理单个用例测试
    if st.session_state.get("api_test_single_case_id"):
        render_single_case_test(dao, st.session_state.api_test_single_case_id)


def render_create_case(dao: ApiTestDAO, projects: List[Project]):
    """渲染创建用例"""
    st.markdown("### ➕ 创建新测试用例")
    
    with st.form("create_case_form"):
        # 基本信息
        col1, col2 = st.columns(2)
        
        with col1:
            name = st.text_input(
                "用例名称 *",
                placeholder="请输入用例名称"
            )
            
            project_options = {p.id: p.name for p in projects}
            project_id = st.selectbox(
                "所属项目 *",
                options=list(project_options.keys()),
                format_func=lambda x: project_options[x]
            )
        
        with col2:
            priority = st.selectbox(
                "优先级",
                options=[p.value for p in CasePriority],
                index=1  # 默认中等优先级
            )
            
            status = st.selectbox(
                "状态",
                options=[s.value for s in CaseStatus],
                index=0  # 默认启用
            )
        
        description = st.text_area(
            "用例描述",
            placeholder="请输入用例描述",
            height=80
        )
        
        tags_input = st.text_input(
            "标签",
            placeholder="请输入标签，用逗号分隔",
            help="如: 登录,核心功能,冒烟测试"
        )
        
        # 请求配置
        st.markdown("**请求配置**")
        
        col_req1, col_req2 = st.columns(2)
        
        with col_req1:
            method = st.selectbox(
                "请求方法",
                options=[m.value for m in RequestMethod],
                index=0  # 默认GET
            )
        
        with col_req2:
            protocol = st.selectbox(
                "协议",
                options=[p.value for p in Protocol],
                index=1  # 默认HTTPS
            )
        
        url = st.text_input(
            "请求URL *",
            placeholder="如: /api/v1/users 或 https://api.example.com/users",
            help="可以是相对路径（配合环境基础URL）或完整URL"
        )
        
        # 请求头
        st.markdown("**请求头 (JSON格式)**")
        headers_json = st.text_area(
            "请求头配置",
            placeholder='[{"key": "Content-Type", "value": "application/json", "enabled": true}]',
            height=100,
            help="格式: [{\"key\": \"header名\", \"value\": \"header值\", \"enabled\": true}]"
        )
        
        # 查询参数
        st.markdown("**查询参数 (JSON格式)**")
        params_json = st.text_area(
            "查询参数配置",
            placeholder='[{"key": "page", "value": "1", "enabled": true}]',
            height=80,
            help="格式: [{\"key\": \"参数名\", \"value\": \"参数值\", \"enabled\": true}]"
        )
        
        # 请求体
        st.markdown("**请求体配置**")
        body_type = st.selectbox(
            "请求体类型",
            options=[b.value for b in BodyType],
            index=0  # 默认None
        )
        
        if body_type != BodyType.NONE.value:
            body_data = st.text_area(
                "请求体内容",
                placeholder='{"username": "test", "password": "123456"}',
                height=120,
                help="根据请求体类型输入相应格式的内容"
            )
        else:
            body_data = ""
        
        # 断言配置
        st.markdown("**断言配置 (JSON格式)**")
        assertions_json = st.text_area(
            "断言规则",
            placeholder='[{"assertion_type": "Status Code", "operator": "equals", "expected_value": "200", "enabled": true}]',
            height=100,
            help="格式: [{\"assertion_type\": \"断言类型\", \"operator\": \"操作符\", \"expected_value\": \"期望值\", \"enabled\": true}]"
        )

        # 变量提取配置
        st.markdown("**变量提取配置**")

        with st.expander("🔧 变量提取设置", expanded=False):
            st.markdown("从响应中提取变量供后续用例使用")

            # 简化的变量提取配置
            col_var1, col_var2 = st.columns(2)

            with col_var1:
                var_name = st.text_input(
                    "变量名称",
                    placeholder="如: user_id, access_token",
                    help="提取的变量名称，可在后续用例中使用 {{变量名}} 引用"
                )

                extraction_method = st.selectbox(
                    "提取方法",
                    options=["json_path", "regex", "header"],
                    format_func=lambda x: {
                        "json_path": "JSONPath表达式",
                        "regex": "正则表达式",
                        "header": "响应头"
                    }.get(x, x)
                )

            with col_var2:
                var_source = st.selectbox(
                    "数据源",
                    options=["response_body", "response_headers"],
                    format_func=lambda x: {
                        "response_body": "响应体",
                        "response_headers": "响应头"
                    }.get(x, x)
                )

                var_expression = st.text_input(
                    "提取表达式",
                    placeholder="如: $.data.user_id 或 \\d+",
                    help="JSONPath: $.data.user_id | 正则: \\d+ | 响应头: Content-Type"
                )

            # 生成JSON格式的变量提取配置
            if var_name and var_expression:
                extractions_json = f'[{{"variable_name": "{var_name}", "source": "{var_source}", "extraction_method": "{extraction_method}", "expression": "{var_expression}", "enabled": true}}]'
            else:
                extractions_json = ""

            # 高级配置 - JSON格式
            st.markdown("**高级配置 (JSON格式)**")
            extractions_json = st.text_area(
                "变量提取规则 (JSON)",
                value=extractions_json,
                placeholder='[{"variable_name": "user_id", "source": "response_body", "extraction_method": "json_path", "expression": "$.data.user_id", "enabled": true}]',
                height=80,
                help="支持配置多个变量提取规则"
            )

        # 数据库断言配置
        st.markdown("**数据库断言配置**")

        with st.expander("🗄️ 数据库断言设置", expanded=False):
            st.markdown("验证接口操作后的数据库数据变化")

            # 获取数据库配置列表
            db_configs = dao.get_database_configs()

            if not db_configs:
                st.warning("⚠️ 请先在数据库管理中添加数据库配置")
                db_assertions_json = ""
            else:
                # 简化的数据库断言配置
                col_db1, col_db2 = st.columns(2)

                with col_db1:
                    db_config_options = {db.id: f"{db.name} ({db.database_type})" for db in db_configs}
                    selected_db_id = st.selectbox(
                        "选择数据库",
                        options=list(db_config_options.keys()),
                        format_func=lambda x: db_config_options[x],
                        help="选择要执行断言的数据库"
                    )

                    assertion_type = st.selectbox(
                        "断言类型",
                        options=["record_exists", "record_count", "field_value"],
                        format_func=lambda x: {
                            "record_exists": "记录存在性",
                            "record_count": "记录数量",
                            "field_value": "字段值验证"
                        }.get(x, x)
                    )

                with col_db2:
                    sql_query = st.text_area(
                        "SQL查询语句",
                        placeholder="SELECT COUNT(*) as count FROM users WHERE email = ?",
                        height=60,
                        help="支持参数化查询，使用?作为占位符"
                    )

                    query_params = st.text_input(
                        "查询参数",
                        placeholder="<EMAIL>,{{user_id}}",
                        help="用逗号分隔多个参数，支持变量引用"
                    )

                # 断言条件
                col_assert1, col_assert2 = st.columns(2)

                with col_assert1:
                    field_name = st.text_input(
                        "字段名称",
                        placeholder="count",
                        help="要验证的字段名称"
                    )

                    operator = st.selectbox(
                        "比较操作符",
                        options=["equals", "greater_than", "less_than", "not_equals"],
                        format_func=lambda x: {
                            "equals": "等于",
                            "greater_than": "大于",
                            "less_than": "小于",
                            "not_equals": "不等于"
                        }.get(x, x)
                    )

                with col_assert2:
                    expected_value = st.text_input(
                        "期望值",
                        placeholder="1",
                        help="期望的字段值"
                    )

                    assertion_description = st.text_input(
                        "断言描述",
                        placeholder="验证用户记录已创建",
                        help="断言的说明文字"
                    )

                # 生成JSON格式的数据库断言配置
                if selected_db_id and sql_query and field_name and expected_value:
                    params_list = [p.strip() for p in query_params.split(",") if p.strip()] if query_params else []
                    db_assertions_json = f'''[{{
    "database_id": "{selected_db_id}",
    "assertion_type": "{assertion_type}",
    "sql_query": "{sql_query}",
    "query_params": {json.dumps(params_list)},
    "field_name": "{field_name}",
    "operator": "{operator}",
    "expected_value": "{expected_value}",
    "description": "{assertion_description}",
    "enabled": true
}}]'''
                else:
                    db_assertions_json = ""

                # 高级配置 - JSON格式
                st.markdown("**高级配置 (JSON格式)**")
                db_assertions_json = st.text_area(
                    "数据库断言规则 (JSON)",
                    value=db_assertions_json,
                    placeholder='[{"database_id": "db_id", "sql_query": "SELECT COUNT(*) as count FROM users WHERE id = ?", "query_params": ["{{user_id}}"], "field_name": "count", "operator": "equals", "expected_value": "1", "enabled": true}]',
                    height=100,
                    help="支持配置多个数据库断言规则"
                )
        
        # 提交按钮
        col_btn1, col_btn2 = st.columns([1, 3])
        
        with col_btn1:
            submitted = st.form_submit_button(
                "✅ 创建用例",
                type="primary",
                use_container_width=True
            )
        
        if submitted:
            if name.strip() and url.strip():
                try:
                    # 解析标签
                    tags = [tag.strip() for tag in tags_input.split(",") if tag.strip()] if tags_input else []
                    
                    # 解析请求头
                    headers = []
                    if headers_json.strip():
                        headers_data = json.loads(headers_json)
                        headers = [KeyValue(**h) for h in headers_data]
                    
                    # 解析查询参数
                    query_params = []
                    if params_json.strip():
                        params_data = json.loads(params_json)
                        query_params = [KeyValue(**p) for p in params_data]
                    
                    # 解析断言
                    assertions = []
                    if assertions_json.strip():
                        assertions_data = json.loads(assertions_json)
                        for a in assertions_data:
                            assertion = Assertion(
                                assertion_type=AssertionType(a.get("assertion_type", "Status Code")),
                                operator=a.get("operator", "equals"),
                                expected_value=a.get("expected_value", ""),
                                enabled=a.get("enabled", True),
                                description=a.get("description", "")
                            )
                            assertions.append(assertion)

                    # 解析变量提取
                    variable_extractions = []
                    if extractions_json.strip():
                        extractions_data = json.loads(extractions_json)
                        for e in extractions_data:
                            extraction = VariableExtraction(
                                variable_name=e.get("variable_name", ""),
                                source=VariableSource(e.get("source", "response_body")),
                                extraction_method=ExtractionMethod(e.get("extraction_method", "json_path")),
                                expression=e.get("expression", ""),
                                enabled=e.get("enabled", True),
                                description=e.get("description", "")
                            )
                            variable_extractions.append(extraction)

                    # 解析数据库断言
                    database_assertions = []
                    if db_assertions_json.strip():
                        db_assertions_data = json.loads(db_assertions_json)
                        for db_assert in db_assertions_data:
                            database_assertions.append({
                                "database_id": db_assert.get("database_id", ""),
                                "assertion_type": db_assert.get("assertion_type", "record_exists"),
                                "sql_query": db_assert.get("sql_query", ""),
                                "query_params": db_assert.get("query_params", []),
                                "field_name": db_assert.get("field_name", ""),
                                "operator": db_assert.get("operator", "equals"),
                                "expected_value": db_assert.get("expected_value", ""),
                                "description": db_assert.get("description", ""),
                                "enabled": db_assert.get("enabled", True)
                            })
                    
                    # 创建请求配置
                    request_config = RequestConfig(
                        method=RequestMethod(method),
                        url=url.strip(),
                        protocol=Protocol(protocol),
                        headers=headers,
                        query_params=query_params,
                        body_type=BodyType(body_type),
                        body_data=body_data.strip() if body_data else ""
                    )
                    
                    # 创建测试用例
                    test_case = TestCase(
                        name=name.strip(),
                        description=description.strip(),
                        project_id=project_id,
                        tags=tags,
                        priority=CasePriority(priority),
                        status=CaseStatus(status),
                        request=request_config,
                        assertions=assertions,
                        variable_extractions=variable_extractions,
                        database_assertions=database_assertions,
                        created_by="系统用户"  # 这里可以从session中获取当前用户
                    )
                    
                    # 保存到数据库
                    if dao.create_test_case(test_case):
                        st.success("✅ 测试用例创建成功")
                        st.rerun()
                    else:
                        st.error("❌ 测试用例创建失败，请重试")
                
                except json.JSONDecodeError as e:
                    st.error(f"❌ JSON格式错误: {e}")
                except Exception as e:
                    st.error(f"❌ 创建失败: {e}")
            else:
                st.error("❌ 用例名称和请求URL不能为空")


def render_edit_case_modal(dao: ApiTestDAO, case_id: str, projects: List[Project]):
    """渲染编辑用例模态框"""
    case = dao.get_test_case_by_id(case_id)
    if not case:
        st.error("❌ 测试用例不存在")
        st.session_state.api_test_edit_case_id = None
        return

    st.markdown("---")
    st.markdown("### ✏️ 编辑测试用例")

    with st.form("edit_case_form"):
        # 基本信息
        col1, col2 = st.columns(2)

        with col1:
            name = st.text_input("用例名称 *", value=case.name)

            project_options = {p.id: p.name for p in projects}
            project_id = st.selectbox(
                "所属项目 *",
                options=list(project_options.keys()),
                format_func=lambda x: project_options[x],
                index=list(project_options.keys()).index(case.project_id) if case.project_id in project_options else 0
            )

        with col2:
            priority = st.selectbox(
                "优先级",
                options=[p.value for p in CasePriority],
                index=[p.value for p in CasePriority].index(case.priority.value)
            )

            status = st.selectbox(
                "状态",
                options=[s.value for s in CaseStatus],
                index=[s.value for s in CaseStatus].index(case.status.value)
            )

        description = st.text_area("用例描述", value=case.description, height=80)

        tags_input = st.text_input(
            "标签",
            value=", ".join(case.tags),
            help="用逗号分隔多个标签"
        )

        # 请求配置
        st.markdown("**请求配置**")

        col_req1, col_req2 = st.columns(2)

        with col_req1:
            method = st.selectbox(
                "请求方法",
                options=[m.value for m in RequestMethod],
                index=[m.value for m in RequestMethod].index(case.request.method.value)
            )

        with col_req2:
            protocol = st.selectbox(
                "协议",
                options=[p.value for p in Protocol],
                index=[p.value for p in Protocol].index(case.request.protocol.value)
            )

        url = st.text_input("请求URL *", value=case.request.url)

        # 提交按钮
        col_btn1, col_btn2, col_btn3 = st.columns([1, 1, 2])

        with col_btn1:
            submitted = st.form_submit_button("💾 保存", type="primary")

        with col_btn2:
            cancelled = st.form_submit_button("❌ 取消")

        if submitted:
            if name.strip() and url.strip():
                try:
                    # 更新用例信息
                    case.name = name.strip()
                    case.description = description.strip()
                    case.project_id = project_id
                    case.tags = [tag.strip() for tag in tags_input.split(",") if tag.strip()]
                    case.priority = CasePriority(priority)
                    case.status = CaseStatus(status)
                    case.request.method = RequestMethod(method)
                    case.request.protocol = Protocol(protocol)
                    case.request.url = url.strip()
                    case.updated_by = "系统用户"  # 这里可以从session中获取当前用户

                    # 保存到数据库
                    if dao.update_test_case(case):
                        st.success("✅ 测试用例更新成功")
                        st.session_state.api_test_edit_case_id = None
                        st.rerun()
                    else:
                        st.error("❌ 测试用例更新失败，请重试")

                except Exception as e:
                    st.error(f"❌ 更新失败: {e}")
            else:
                st.error("❌ 用例名称和请求URL不能为空")

        if cancelled:
            st.session_state.api_test_edit_case_id = None
            st.rerun()


def render_single_case_test(dao: ApiTestDAO, case_id: str):
    """渲染单个用例测试"""
    case = dao.get_test_case_by_id(case_id)
    if not case:
        st.error("❌ 测试用例不存在")
        st.session_state.api_test_single_case_id = None
        return

    st.markdown("---")
    st.markdown("### 🧪 单用例测试")
    st.markdown(f"**用例名称**: {case.name}")
    st.markdown(f"**请求方法**: {case.request.method.value if hasattr(case.request.method, 'value') else case.request.method}")
    st.markdown(f"**请求URL**: {case.request.url}")

    # 选择执行环境
    environments = dao.get_environments()
    if environments:
        env_options = {env.id: f"{'🌟 ' if env.is_default else ''}{env.name}" for env in environments}
        selected_env_id = st.selectbox(
            "选择执行环境",
            options=list(env_options.keys()),
            format_func=lambda x: env_options[x],
            key="single_test_env"
        )
        selected_env = next((env for env in environments if env.id == selected_env_id), None)
    else:
        st.warning("⚠️ 请先创建测试环境")
        selected_env = None

    col1, col2 = st.columns([1, 3])

    with col1:
        if st.button("🚀 执行测试", type="primary", disabled=not selected_env):
            execute_single_case(dao, case, selected_env)

    with col2:
        if st.button("❌ 关闭测试"):
            st.session_state.api_test_single_case_id = None
            st.rerun()


def execute_single_case(dao: ApiTestDAO, case: TestCase, environment: Environment):
    """执行单个测试用例"""
    from src.components.api_test.test_engine import TestEngine

    with st.spinner("🔄 正在执行测试用例..."):
        try:
            # 创建执行引擎
            engine = TestEngine()
            engine.set_environment(environment)

            # 执行用例
            result = engine.execute_single_case(case, environment)

            # 显示执行结果
            st.markdown("#### 📊 执行结果")

            if result.status == ExecutionStatus.PASSED:
                st.success(f"✅ 测试通过")
            elif result.status == ExecutionStatus.FAILED:
                st.error(f"❌ 测试失败")
            elif result.status == ExecutionStatus.SKIPPED:
                st.warning(f"⏭️ 测试跳过: {result.skip_reason}")
            elif result.status == ExecutionStatus.ERROR:
                st.error(f"💥 执行错误: {result.error_message}")

            # 显示详细信息
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**请求信息**")
                st.markdown(f"- URL: `{result.request_url}`")
                st.markdown(f"- 方法: `{result.request_method}`")
                if result.request_headers:
                    st.markdown("- 请求头:")
                    for key, value in result.request_headers.items():
                        st.markdown(f"  - `{key}`: {value}")

            with col2:
                st.markdown("**响应信息**")
                st.markdown(f"- 状态码: `{result.response_status_code}`")
                st.markdown(f"- 响应时间: `{result.response_time:.2f}ms`")
                st.markdown(f"- 响应大小: `{result.response_size}字节`")

            # 显示断言结果
            if result.assertion_results:
                st.markdown("#### 🎯 断言结果")
                for assertion_result in result.assertion_results:
                    if assertion_result["passed"]:
                        st.success(f"✅ {assertion_result['description']}")
                    else:
                        st.error(f"❌ {assertion_result['description']}: {assertion_result['error_message']}")

            # 显示变量提取结果
            if result.extracted_variables:
                st.markdown("#### 📋 变量提取结果")
                for var_name, var_value in result.extracted_variables.items():
                    st.markdown(f"- `{var_name}`: {var_value}")

            # 显示响应体
            if result.response_body:
                with st.expander("📄 响应体", expanded=False):
                    try:
                        # 尝试格式化JSON
                        import json
                        formatted_json = json.dumps(json.loads(result.response_body), indent=2, ensure_ascii=False)
                        st.code(formatted_json, language="json")
                    except:
                        # 如果不是JSON，直接显示
                        st.code(result.response_body)

        except Exception as e:
            st.error(f"❌ 执行失败: {e}")
            import traceback
            with st.expander("🔍 错误详情", expanded=False):
                st.code(traceback.format_exc())


if __name__ == "__main__":
    render()
