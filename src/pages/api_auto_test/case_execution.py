#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
用例执行页面
"""

import streamlit as st
import time
from datetime import datetime
from typing import List

from src.components.api_test.models import *
from src.components.api_test.dao import ApiTestDAO
from src.components.api_test.test_engine import execution_manager


def render():
    """渲染用例执行页面"""
    st.title("🚀 用例执行")
    st.markdown("执行接口测试用例，查看实时结果")
    
    # 初始化DAO
    dao = ApiTestDAO()
    
    # 检查基础数据
    projects = dao.get_projects()
    environments = dao.get_environments()
    
    if not projects:
        st.warning("⚠️ 请先创建项目和测试用例")
        if st.button("🔗 前往项目管理"):
            st.switch_page("src/pages/api_auto_test/project_management.py")
        return
    
    if not environments:
        st.warning("⚠️ 请先创建测试环境")
        if st.button("🔗 前往环境管理"):
            st.switch_page("src/pages/api_auto_test/environment_management.py")
        return
    
    # 主要功能选项卡
    tab1, tab2, tab3 = st.tabs(["🎯 执行配置", "📊 执行监控", "📋 执行历史"])
    
    with tab1:
        render_execution_config(dao, projects, environments)
    
    with tab2:
        render_execution_monitor()
    
    with tab3:
        render_execution_history(dao)


def render_execution_config(dao: ApiTestDAO, projects: List[Project], environments: List[Environment]):
    """渲染执行配置"""
    st.markdown("### 🎯 执行配置")
    
    # 环境选择
    env_options = {env.id: f"{'🌟 ' if env.is_default else ''}{env.name}" for env in environments}
    selected_env_id = st.selectbox(
        "🌐 选择执行环境",
        options=list(env_options.keys()),
        format_func=lambda x: env_options[x],
        key="execution_env_selector"
    )
    
    selected_env = next((env for env in environments if env.id == selected_env_id), None)
    if selected_env:
        st.info(f"📍 基础URL: `{selected_env.base_url or '未设置'}`")
    
    # 用例选择方式
    selection_mode = st.radio(
        "📝 用例选择方式",
        options=["按项目选择", "按标签选择", "手动选择"],
        horizontal=True,
        key="case_selection_mode"
    )
    
    selected_cases = []
    
    if selection_mode == "按项目选择":
        # 项目选择
        project_options = {p.id: p.name for p in projects}
        selected_project_id = st.selectbox(
            "📁 选择项目",
            options=list(project_options.keys()),
            format_func=lambda x: project_options[x],
            key="execution_project_selector"
        )
        
        # 获取项目下的用例
        project_cases = dao.get_test_cases(project_id=selected_project_id)

        # 调试信息
        st.info(f"🔍 调试信息: 项目ID={selected_project_id}, 找到用例数={len(project_cases)}")

        if project_cases:
            # 状态筛选
            status_filter = st.multiselect(
                "状态筛选",
                options=[s.value for s in CaseStatus],
                default=[CaseStatus.ENABLED.value],
                key="execution_status_filter"
            )
            
            # 优先级筛选
            priority_filter = st.multiselect(
                "优先级筛选",
                options=[p.value for p in CasePriority],
                default=[p.value for p in CasePriority],
                key="execution_priority_filter"
            )
            
            # 过滤用例
            filtered_cases = []
            for case in project_cases:
                # 安全地获取枚举值
                case_status = case.status.value if hasattr(case.status, 'value') else case.status
                case_priority = case.priority.value if hasattr(case.priority, 'value') else case.priority

                if case_status in status_filter and case_priority in priority_filter:
                    filtered_cases.append(case)
            
            selected_cases = filtered_cases
            st.info(f"📊 筛选后共 {len(selected_cases)} 个用例")
        else:
            st.warning("📝 该项目下暂无测试用例")
    
    elif selection_mode == "按标签选择":
        # 获取所有标签
        all_cases = dao.get_test_cases()
        all_tags = set()
        for case in all_cases:
            all_tags.update(case.tags)
        
        if all_tags:
            selected_tags = st.multiselect(
                "🏷️ 选择标签",
                options=sorted(list(all_tags)),
                key="execution_tag_selector"
            )
            
            if selected_tags:
                # 根据标签筛选用例
                tagged_cases = [
                    case for case in all_cases
                    if any(tag in case.tags for tag in selected_tags) and case.status == CaseStatus.ENABLED
                ]
                selected_cases = tagged_cases
                st.info(f"📊 标签筛选后共 {len(selected_cases)} 个用例")
        else:
            st.warning("📝 暂无标签可选择")
    
    else:  # 手动选择
        # 获取所有用例
        all_cases = dao.get_test_cases()
        
        if all_cases:
            case_options = {}
            for case in all_cases:
                project_name = next((p.name for p in projects if p.id == case.project_id), "未知项目")
                case_options[case.id] = f"[{project_name}] {case.name}"
            
            selected_case_ids = st.multiselect(
                "📝 手动选择用例",
                options=list(case_options.keys()),
                format_func=lambda x: case_options[x],
                key="execution_manual_selector"
            )
            
            selected_cases = [case for case in all_cases if case.id in selected_case_ids]
        else:
            st.warning("📝 暂无测试用例")
    
    # 执行配置
    if selected_cases:
        st.markdown("---")
        st.markdown("### ⚙️ 执行配置")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            parallel_execution = st.checkbox(
                "🔄 并行执行",
                value=False,
                help="是否并行执行多个用例"
            )
        
        with col2:
            if parallel_execution:
                max_workers = st.number_input(
                    "最大并发数",
                    min_value=1,
                    max_value=10,
                    value=3,
                    help="并行执行时的最大线程数"
                )
            else:
                max_workers = 1
        
        with col3:
            stop_on_failure = st.checkbox(
                "🛑 失败时停止",
                value=False,
                help="遇到失败用例时是否停止执行"
            )
        
        # 执行按钮
        st.markdown("---")
        col_exec1, col_exec2, col_exec3 = st.columns([1, 1, 2])
        
        with col_exec1:
            if st.button(
                "🚀 开始执行",
                type="primary",
                use_container_width=True,
                disabled=st.session_state.get("api_test_execution_running", False)
            ):
                # 开始执行
                start_execution(
                    dao, selected_cases, selected_env,
                    parallel_execution, max_workers, stop_on_failure
                )
        
        with col_exec2:
            if st.button(
                "🛑 停止执行",
                use_container_width=True,
                disabled=not st.session_state.get("api_test_execution_running", False)
            ):
                # 停止执行
                stop_execution()
        
        # 显示选中的用例
        with st.expander(f"📋 已选择的用例 ({len(selected_cases)}个)", expanded=False):
            for i, case in enumerate(selected_cases):
                st.markdown(f"{i+1}. **{case.name}** - {case.request.method.value} `{case.request.url}`")


def render_execution_monitor():
    """渲染执行监控"""
    st.markdown("### 📊 执行监控")
    
    # 检查是否有正在执行的任务
    if not st.session_state.get("api_test_execution_running", False):
        st.info("💡 当前没有正在执行的任务")
        return
    
    # 显示执行进度
    execution_state = st.session_state.get("api_test_execution_state", {})
    
    if execution_state:
        # 进度条
        progress = execution_state.get("progress", 0)
        total = execution_state.get("total", 1)
        progress_percent = progress / total if total > 0 else 0
        
        st.progress(progress_percent, text=f"执行进度: {progress}/{total} ({progress_percent:.1%})")
        
        # 统计信息
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("总用例数", total)
        
        with col2:
            passed = execution_state.get("passed", 0)
            st.metric("通过", passed, delta=None)
        
        with col3:
            failed = execution_state.get("failed", 0)
            st.metric("失败", failed, delta=None)
        
        with col4:
            skipped = execution_state.get("skipped", 0)
            st.metric("跳过", skipped, delta=None)
        
        # 实时日志
        st.markdown("### 📝 执行日志")
        logs = execution_state.get("logs", [])
        
        if logs:
            # 显示最新的10条日志
            log_container = st.container()
            with log_container:
                for log in logs[-10:]:
                    timestamp = log.get("timestamp", "")
                    level = log.get("level", "INFO")
                    message = log.get("message", "")
                    
                    if level == "ERROR":
                        st.error(f"[{timestamp}] {message}")
                    elif level == "WARNING":
                        st.warning(f"[{timestamp}] {message}")
                    elif level == "SUCCESS":
                        st.success(f"[{timestamp}] {message}")
                    else:
                        st.info(f"[{timestamp}] {message}")
        
        # 自动刷新
        time.sleep(1)
        st.rerun()


def render_execution_history(dao: ApiTestDAO):
    """渲染执行历史"""
    st.markdown("### 📋 执行历史")

    # 获取执行历史
    reports = dao.get_execution_reports(20)  # 获取最近20个报告

    if not reports:
        st.info("📝 暂无执行历史")
        return

    # 显示执行历史列表
    for i, report in enumerate(reports):
        pass_rate = (report.passed_cases / report.total_cases * 100) if report.total_cases > 0 else 0

        with st.expander(
            f"📊 {report.name} - 通过率 {pass_rate:.1f}%",
            expanded=False
        ):
            col1, col2, col3 = st.columns([2, 1, 1])

            with col1:
                st.markdown(f"**环境**: {report.environment_name}")
                st.markdown(f"**执行时间**: {report.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
                st.markdown(f"**执行时长**: {report.total_duration:.1f}秒")

            with col2:
                st.metric("总用例数", report.total_cases)
                st.metric("通过数", report.passed_cases)

            with col3:
                st.metric("失败数", report.failed_cases)
                st.metric("跳过数", report.skipped_cases)

            # 操作按钮
            if st.button("📊 查看详情", key=f"view_history_{i}"):
                st.session_state.api_test_selected_report_id = report.id
                st.switch_page("src/pages/api_auto_test/execution_reports.py")


def start_execution(dao: ApiTestDAO, test_cases: List[TestCase], environment: Environment,
                   parallel: bool, max_workers: int, stop_on_failure: bool):
    """开始执行测试"""
    import threading

    try:
        # 设置执行状态
        st.session_state.api_test_execution_running = True
        st.session_state.api_test_execution_state = {
            "progress": 0,
            "total": len(test_cases),
            "passed": 0,
            "failed": 0,
            "skipped": 0,
            "error": 0,
            "logs": [],
            "current_case": "",
            "start_time": datetime.now().isoformat()
        }

        # 添加开始日志
        add_execution_log("INFO", f"开始执行 {len(test_cases)} 个测试用例")

        # 获取或创建执行引擎
        session_id = st.session_state.get("session_id", "streamlit_session")
        engine = execution_manager.get_engine(session_id)
        if not engine:
            engine = execution_manager.create_engine(session_id)

        # 设置环境
        engine.set_environment(environment)

        # 设置回调函数
        engine.set_progress_callback(update_progress)
        engine.set_result_callback(update_result)

        # 在后台线程中执行测试
        def execute_in_background():
            try:
                add_execution_log("INFO", "开始执行测试用例...")

                # 执行测试用例
                report = engine.execute_multiple_cases(
                    test_cases, environment, parallel, max_workers, stop_on_failure
                )

                # 保存报告到数据库
                dao.create_execution_report(report)

                # 更新执行状态
                st.session_state.api_test_execution_state.update({
                    "status": "completed",
                    "end_time": datetime.now().isoformat(),
                    "report": report
                })

                add_execution_log("SUCCESS", f"执行完成！通过率: {(report.passed_cases/report.total_cases*100):.1f}%")

            except Exception as e:
                add_execution_log("ERROR", f"执行失败: {e}")
                st.session_state.api_test_execution_state["status"] = "failed"
                st.session_state.api_test_execution_state["error"] = str(e)
            finally:
                st.session_state.api_test_execution_running = False

        # 启动后台线程
        thread = threading.Thread(target=execute_in_background, daemon=True)
        thread.start()

        add_execution_log("SUCCESS", "执行已启动，请切换到执行监控标签页查看进度")
        st.success("✅ 执行已开始，请切换到执行监控标签页查看进度")

    except Exception as e:
        st.error(f"❌ 执行启动失败: {e}")
        st.session_state.api_test_execution_running = False


def stop_execution():
    """停止执行"""
    try:
        session_id = st.session_state.get("session_id", "default")
        engine = execution_manager.get_engine(session_id)
        if engine:
            engine.stop_execution()
        
        st.session_state.api_test_execution_running = False
        add_execution_log("WARNING", "执行已被用户停止")
        st.warning("⚠️ 执行已停止")
        
    except Exception as e:
        st.error(f"❌ 停止执行失败: {e}")


def update_progress(current: int, total: int):
    """更新进度"""
    if "api_test_execution_state" in st.session_state:
        st.session_state.api_test_execution_state["progress"] = current


def update_result(result: ExecutionResult):
    """更新结果"""
    if "api_test_execution_state" in st.session_state:
        state = st.session_state.api_test_execution_state
        
        if result.status == ExecutionStatus.PASSED:
            state["passed"] += 1
            add_execution_log("SUCCESS", f"用例 {result.case_name} 执行通过")
        elif result.status == ExecutionStatus.FAILED:
            state["failed"] += 1
            add_execution_log("ERROR", f"用例 {result.case_name} 执行失败: {result.error_message}")
        elif result.status == ExecutionStatus.SKIPPED:
            state["skipped"] += 1
            add_execution_log("WARNING", f"用例 {result.case_name} 被跳过: {result.skip_reason}")


def add_execution_log(level: str, message: str):
    """添加执行日志"""
    if "api_test_execution_state" in st.session_state:
        log_entry = {
            "timestamp": datetime.now().strftime("%H:%M:%S"),
            "level": level,
            "message": message
        }
        st.session_state.api_test_execution_state["logs"].append(log_entry)


if __name__ == "__main__":
    render()
