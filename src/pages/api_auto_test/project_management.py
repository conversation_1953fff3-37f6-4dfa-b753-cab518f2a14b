#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
项目管理页面
"""

import streamlit as st
import pandas as pd
from datetime import datetime

from src.components.api_test.models import Project
from src.components.api_test.dao import ApiTestDAO


def render():
    """渲染项目管理页面"""
    st.title("📁 项目管理")
    st.markdown("管理接口测试项目，组织测试用例")
    
    # 初始化DAO
    dao = ApiTestDAO()
    
    # 主要功能选项卡
    tab1, tab2 = st.tabs(["📋 项目列表", "➕ 创建项目"])
    
    with tab1:
        render_project_list(dao)
    
    with tab2:
        render_create_project(dao)


def render_project_list(dao: ApiTestDAO):
    """渲染项目列表"""
    st.markdown("### 📋 项目列表")
    
    # 获取所有项目
    projects = dao.get_projects()
    
    if not projects:
        st.info("📝 暂无项目，请创建第一个项目")
        return
    
    # 搜索功能
    search_term = st.text_input(
        "🔍 搜索项目",
        placeholder="输入项目名称进行搜索",
        key="project_search"
    )
    
    # 过滤项目
    if search_term:
        filtered_projects = [
            p for p in projects 
            if search_term.lower() in p.name.lower() or 
               search_term.lower() in (p.description or "").lower()
        ]
    else:
        filtered_projects = projects
    
    if not filtered_projects:
        st.warning(f"🔍 没有找到包含 '{search_term}' 的项目")
        return
    
    # 显示项目统计
    st.markdown(f"**找到 {len(filtered_projects)} 个项目**")
    
    # 项目列表
    for i, project in enumerate(filtered_projects):
        with st.expander(f"📁 {project.name}", expanded=False):
            col1, col2, col3 = st.columns([2, 1, 1])
            
            with col1:
                st.markdown(f"**描述**: {project.description or '无描述'}")
                st.markdown(f"**创建时间**: {project.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
                st.markdown(f"**创建人**: {project.created_by or '未知'}")
            
            with col2:
                # 获取项目统计信息
                test_cases = dao.get_test_cases(project_id=project.id)
                st.metric("测试用例数", len(test_cases))
            
            with col3:
                # 操作按钮
                col_btn1, col_btn2 = st.columns(2)
                
                with col_btn1:
                    if st.button("✏️ 编辑", key=f"edit_project_{i}"):
                        st.session_state.api_test_edit_project_id = project.id
                        st.rerun()
                
                with col_btn2:
                    if st.button("🗑️ 删除", key=f"delete_project_{i}"):
                        if st.session_state.get(f"confirm_delete_project_{i}", False):
                            # 确认删除
                            if dao.delete_project(project.id):
                                st.success("✅ 项目删除成功")
                                st.rerun()
                            else:
                                st.error("❌ 项目删除失败")
                        else:
                            st.session_state[f"confirm_delete_project_{i}"] = True
                            st.warning("⚠️ 再次点击确认删除")
    
    # 处理编辑项目
    if st.session_state.get("api_test_edit_project_id"):
        render_edit_project_modal(dao, st.session_state.api_test_edit_project_id)


def render_edit_project_modal(dao: ApiTestDAO, project_id: str):
    """渲染编辑项目模态框"""
    project = dao.get_project_by_id(project_id)
    if not project:
        st.error("❌ 项目不存在")
        st.session_state.api_test_edit_project_id = None
        return
    
    st.markdown("---")
    st.markdown("### ✏️ 编辑项目")
    
    with st.form("edit_project_form"):
        name = st.text_input(
            "项目名称 *",
            value=project.name,
            placeholder="请输入项目名称"
        )
        
        description = st.text_area(
            "项目描述",
            value=project.description or "",
            placeholder="请输入项目描述",
            height=100
        )
        
        col_btn1, col_btn2, col_btn3 = st.columns([1, 1, 2])
        
        with col_btn1:
            submitted = st.form_submit_button("💾 保存", type="primary")
        
        with col_btn2:
            cancelled = st.form_submit_button("❌ 取消")
        
        if submitted:
            if name.strip():
                project.name = name.strip()
                project.description = description.strip()
                project.updated_at = datetime.now()
                
                if dao.update_project(project):
                    st.success("✅ 项目更新成功")
                    st.session_state.api_test_edit_project_id = None
                    st.rerun()
                else:
                    st.error("❌ 项目更新失败")
            else:
                st.error("❌ 项目名称不能为空")
        
        if cancelled:
            st.session_state.api_test_edit_project_id = None
            st.rerun()


def render_create_project(dao: ApiTestDAO):
    """渲染创建项目"""
    st.markdown("### ➕ 创建新项目")
    
    with st.form("create_project_form"):
        name = st.text_input(
            "项目名称 *",
            placeholder="请输入项目名称",
            help="项目的唯一标识名称"
        )
        
        description = st.text_area(
            "项目描述",
            placeholder="请输入项目描述",
            height=100,
            help="项目的详细描述信息"
        )
        
        created_by = st.text_input(
            "创建人",
            placeholder="请输入创建人姓名",
            help="项目创建者姓名"
        )
        
        # 提交按钮
        col_btn1, col_btn2 = st.columns([1, 3])
        
        with col_btn1:
            submitted = st.form_submit_button(
                "✅ 创建项目",
                type="primary",
                use_container_width=True
            )
        
        if submitted:
            if name.strip():
                # 创建项目对象
                project = Project(
                    name=name.strip(),
                    description=description.strip(),
                    created_by=created_by.strip()
                )
                
                # 保存到数据库
                if dao.create_project(project):
                    st.success("✅ 项目创建成功")
                    st.rerun()
                else:
                    st.error("❌ 项目创建失败，请重试")
            else:
                st.error("❌ 项目名称不能为空")
    
    # 项目创建指南
    with st.expander("📖 项目创建指南", expanded=False):
        st.markdown("""
        ### 🎯 项目管理说明
        
        **项目的作用**:
        - 📁 **组织管理**: 将相关的测试用例组织在一起
        - 🎯 **权限控制**: 不同项目可以分配给不同的团队
        - 📊 **统计分析**: 按项目维度统计测试结果
        - 🔄 **版本管理**: 支持项目级别的版本控制
        
        **命名建议**:
        - 使用有意义的名称，如 "用户管理系统"、"支付服务API"
        - 避免使用特殊字符，建议使用中文或英文
        - 名称应该简洁明了，便于团队理解
        
        **描述建议**:
        - 详细说明项目的业务范围和测试目标
        - 包含项目的技术栈和依赖信息
        - 记录项目的负责人和联系方式
        
        **最佳实践**:
        - 🎯 按业务模块或服务划分项目
        - 📋 定期清理无用的测试项目
        - 👥 明确项目的负责人和维护者
        - 📝 保持项目描述的及时更新
        """)


if __name__ == "__main__":
    render()
