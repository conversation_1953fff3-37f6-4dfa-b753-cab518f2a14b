#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
定时任务管理页面
"""

import streamlit as st
from datetime import datetime, timedelta
import uuid

from src.components.api_test.scheduler import (
    TaskScheduler, ScheduledTask, ScheduleType, TaskStatus, task_scheduler
)
from src.components.api_test.dao import ApiTestDAO
from src.components.api_test.notification import notification_manager


def render():
    """渲染定时任务管理页面"""
    st.title("⏰ 定时任务")
    st.markdown("配置和管理接口测试的定时执行任务")

    try:
        # 启动调度器
        if not task_scheduler.running:
            task_scheduler.start()

        # 初始化DAO
        dao = ApiTestDAO()

        # 主要功能选项卡
        tab1, tab2, tab3 = st.tabs(["📋 任务列表", "➕ 创建任务", "📊 执行统计"])

        with tab1:
            render_task_list(dao)

        with tab2:
            render_create_task(dao)

        with tab3:
            render_execution_stats()

    except Exception as e:
        st.error(f"❌ 定时任务模块加载失败: {e}")
        st.info("💡 请检查schedule模块是否正确安装: pip install schedule")

        # 显示简化版本
        st.markdown("### 📋 定时任务功能")
        st.info("🚧 定时任务功能暂时不可用，请联系管理员")

        if st.button("🔄 重新加载"):
            st.rerun()


def render_task_list(dao: ApiTestDAO):
    """渲染任务列表"""
    st.markdown("### 📋 定时任务列表")

    try:
        tasks = task_scheduler.list_tasks()

        if not tasks:
            st.info("📝 暂无定时任务，请创建第一个定时任务")
            return
    except Exception as e:
        st.error(f"❌ 获取任务列表失败: {e}")
        return
    
    # 显示任务列表
    for i, task in enumerate(tasks):
        status_color = {
            TaskStatus.PENDING: "🟡",
            TaskStatus.RUNNING: "🔵", 
            TaskStatus.COMPLETED: "🟢",
            TaskStatus.FAILED: "🔴",
            TaskStatus.CANCELLED: "⚫"
        }.get(task.status, "⚪")
        
        with st.expander(
            f"{status_color} {task.name} - {task.schedule_type.value}",
            expanded=False
        ):
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.markdown(f"**描述**: {task.description}")
                st.markdown(f"**调度类型**: {task.schedule_type.value}")
                st.markdown(f"**状态**: {'✅ 启用' if task.enabled else '❌ 禁用'}")
                
                if task.next_execution:
                    st.markdown(f"**下次执行**: {task.next_execution.strftime('%Y-%m-%d %H:%M:%S')}")
                
                if task.last_execution:
                    st.markdown(f"**上次执行**: {task.last_execution.strftime('%Y-%m-%d %H:%M:%S')}")
                
                st.markdown(f"**执行统计**: 总计{task.execution_count}次，成功{task.success_count}次，失败{task.failure_count}次")
                
                # 显示调度配置
                if task.schedule_type == ScheduleType.DAILY:
                    time_str = task.schedule_config.get("time", "09:00")
                    st.markdown(f"**执行时间**: 每天 {time_str}")
                elif task.schedule_type == ScheduleType.WEEKLY:
                    day = task.schedule_config.get("day", "monday")
                    time_str = task.schedule_config.get("time", "09:00")
                    st.markdown(f"**执行时间**: 每周{day} {time_str}")
                elif task.schedule_type == ScheduleType.INTERVAL:
                    interval = task.schedule_config.get("interval", 60)
                    unit = task.schedule_config.get("unit", "minutes")
                    st.markdown(f"**执行间隔**: 每{interval}{unit}")
            
            with col2:
                # 操作按钮
                col_btn1, col_btn2 = st.columns(2)
                
                with col_btn1:
                    if st.button("▶️ 立即执行", key=f"run_task_{i}"):
                        execute_task_now(task)
                    
                    if task.enabled:
                        if st.button("⏸️ 禁用", key=f"disable_task_{i}"):
                            task_scheduler.disable_task(task.id)
                            st.success("✅ 任务已禁用")
                            st.rerun()
                    else:
                        if st.button("▶️ 启用", key=f"enable_task_{i}"):
                            task_scheduler.enable_task(task.id)
                            st.success("✅ 任务已启用")
                            st.rerun()
                
                with col_btn2:
                    if st.button("✏️ 编辑", key=f"edit_task_{i}"):
                        st.session_state.edit_task_id = task.id
                        st.rerun()
                    
                    if st.button("🗑️ 删除", key=f"delete_task_{i}"):
                        if st.session_state.get(f"confirm_delete_task_{i}", False):
                            task_scheduler.remove_task(task.id)
                            st.success("✅ 任务删除成功")
                            st.rerun()
                        else:
                            st.session_state[f"confirm_delete_task_{i}"] = True
                            st.warning("⚠️ 再次点击确认删除")


def render_create_task(dao: ApiTestDAO):
    """渲染创建任务"""
    st.markdown("### ➕ 创建定时任务")
    
    # 检查基础数据
    projects = dao.get_projects()
    environments = dao.get_environments()
    
    if not projects or not environments:
        st.warning("⚠️ 请先创建项目和环境")
        return
    
    with st.form("create_task_form"):
        # 基本信息
        col1, col2 = st.columns(2)
        
        with col1:
            name = st.text_input(
                "任务名称 *",
                placeholder="如: 每日回归测试、核心接口巡检"
            )
            
            schedule_type = st.selectbox(
                "调度类型 *",
                options=[t.value for t in ScheduleType if t != ScheduleType.CRON],
                format_func=lambda x: {
                    "once": "一次性执行",
                    "daily": "每日执行", 
                    "weekly": "每周执行",
                    "interval": "间隔执行"
                }.get(x, x)
            )
        
        with col2:
            description = st.text_area(
                "任务描述",
                placeholder="描述任务的目的和执行内容"
            )
        
        # 调度配置
        st.markdown("**调度配置**")

        if schedule_type == "daily":
            execution_time_str = st.text_input("执行时间", value="09:00", placeholder="HH:MM格式，如: 09:00")
            schedule_config = {"time": execution_time_str}

        elif schedule_type == "weekly":
            col_week1, col_week2 = st.columns(2)
            with col_week1:
                day = st.selectbox(
                    "执行日期",
                    options=["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"],
                    format_func=lambda x: {
                        "monday": "周一", "tuesday": "周二", "wednesday": "周三",
                        "thursday": "周四", "friday": "周五", "saturday": "周六", "sunday": "周日"
                    }.get(x, x)
                )
            with col_week2:
                execution_time_str = st.text_input("执行时间", value="09:00", placeholder="HH:MM格式")

            schedule_config = {"day": day, "time": execution_time_str}

        elif schedule_type == "interval":
            col_int1, col_int2 = st.columns(2)
            with col_int1:
                interval = st.number_input("间隔数值", min_value=1, value=60)
            with col_int2:
                unit = st.selectbox("时间单位", options=["minutes", "hours", "days"],
                                  format_func=lambda x: {"minutes": "分钟", "hours": "小时", "days": "天"}.get(x, x))

            schedule_config = {"interval": interval, "unit": unit}

        elif schedule_type == "once":
            execution_time_str = st.text_input(
                "执行时间",
                value=(datetime.now() + timedelta(hours=1)).strftime("%Y-%m-%d %H:%M:%S"),
                placeholder="YYYY-MM-DD HH:MM:SS格式"
            )
            schedule_config = {"execute_time": execution_time_str}
        
        # 执行配置
        st.markdown("**执行配置**")
        
        col_exec1, col_exec2 = st.columns(2)
        
        with col_exec1:
            # 环境选择
            env_options = {env.id: env.name for env in environments}
            environment_id = st.selectbox(
                "执行环境 *",
                options=list(env_options.keys()),
                format_func=lambda x: env_options[x]
            )
            
            # 项目选择
            project_options = {proj.id: proj.name for proj in projects}
            selected_project_ids = st.multiselect(
                "选择项目",
                options=list(project_options.keys()),
                format_func=lambda x: project_options[x],
                help="不选择则执行所有项目的用例"
            )
        
        with col_exec2:
            # 标签筛选
            tags_input = st.text_input(
                "标签筛选",
                placeholder="用逗号分隔多个标签，如: 核心功能,回归测试",
                help="只执行包含这些标签的用例"
            )
            tags = [tag.strip() for tag in tags_input.split(",") if tag.strip()]
            
            # 执行参数
            parallel = st.checkbox("并行执行", value=False)
            if parallel:
                max_workers = st.number_input("最大并发数", min_value=1, max_value=10, value=3)
            else:
                max_workers = 1
            
            stop_on_failure = st.checkbox("遇到失败时停止", value=False)
        
        # 通知配置
        st.markdown("**通知配置**")
        
        notification_configs = list(notification_manager.configs.keys())
        if notification_configs:
            selected_notifications = st.multiselect(
                "通知配置",
                options=notification_configs,
                help="选择要发送通知的配置"
            )
            
            col_notify1, col_notify2 = st.columns(2)
            with col_notify1:
                notify_on_success = st.checkbox("成功时通知", value=True)
            with col_notify2:
                notify_on_failure = st.checkbox("失败时通知", value=True)
        else:
            st.info("💡 暂无通知配置，请先在通知管理中添加")
            selected_notifications = []
            notify_on_success = False
            notify_on_failure = False
        
        # 提交按钮
        col_btn1, col_btn2 = st.columns([1, 3])
        
        with col_btn1:
            submitted = st.form_submit_button(
                "✅ 创建任务",
                type="primary",
                use_container_width=True
            )
        
        if submitted:
            if name.strip():
                try:
                    # 创建定时任务
                    task = ScheduledTask(
                        id=str(uuid.uuid4()),
                        name=name.strip(),
                        description=description.strip(),
                        schedule_type=ScheduleType(schedule_type),
                        schedule_config=schedule_config,
                        project_ids=selected_project_ids,
                        environment_id=environment_id,
                        tags=tags,
                        parallel=parallel,
                        max_workers=max_workers,
                        stop_on_failure=stop_on_failure,
                        notification_configs=selected_notifications,
                        notify_on_success=notify_on_success,
                        notify_on_failure=notify_on_failure,
                        created_by="用户"
                    )
                    
                    # 添加到调度器
                    if task_scheduler.add_task(task):
                        st.success("✅ 定时任务创建成功")
                        st.rerun()
                    else:
                        st.error("❌ 定时任务创建失败")
                
                except Exception as e:
                    st.error(f"❌ 创建失败: {e}")
            else:
                st.error("❌ 任务名称不能为空")


def render_execution_stats():
    """渲染执行统计"""
    st.markdown("### 📊 执行统计")
    
    tasks = task_scheduler.list_tasks()
    
    if not tasks:
        st.info("📝 暂无定时任务")
        return
    
    # 统计信息
    total_tasks = len(tasks)
    enabled_tasks = len([t for t in tasks if t.enabled])
    total_executions = sum(t.execution_count for t in tasks)
    total_successes = sum(t.success_count for t in tasks)
    total_failures = sum(t.failure_count for t in tasks)
    
    # 显示统计卡片
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("总任务数", total_tasks)
    
    with col2:
        st.metric("启用任务", enabled_tasks)
    
    with col3:
        st.metric("总执行次数", total_executions)
    
    with col4:
        success_rate = (total_successes / total_executions * 100) if total_executions > 0 else 0
        st.metric("成功率", f"{success_rate:.1f}%")
    
    # 任务执行历史
    st.markdown("#### 📋 任务执行历史")
    
    history_data = []
    for task in tasks:
        if task.last_execution:
            history_data.append({
                "任务名称": task.name,
                "上次执行": task.last_execution.strftime('%Y-%m-%d %H:%M:%S'),
                "执行次数": task.execution_count,
                "成功次数": task.success_count,
                "失败次数": task.failure_count,
                "成功率": f"{(task.success_count / task.execution_count * 100):.1f}%" if task.execution_count > 0 else "0%"
            })
    
    if history_data:
        import pandas as pd
        df = pd.DataFrame(history_data)
        st.dataframe(df, use_container_width=True, hide_index=True)
    else:
        st.info("📝 暂无执行历史")


def execute_task_now(task: ScheduledTask):
    """立即执行任务"""
    with st.spinner(f"正在执行任务: {task.name}..."):
        success = task_scheduler.execute_task_now(task.id)
        
        if success:
            st.success(f"✅ 任务执行完成: {task.name}")
        else:
            st.error(f"❌ 任务执行失败: {task.name}")
        
        st.rerun()


if __name__ == "__main__":
    render()
