#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
数据库管理页面
"""

import streamlit as st
import json
from datetime import datetime

from src.components.api_test.database import DatabaseConfig, DatabaseType, db_manager
from src.components.api_test.dao import ApiTestDAO


def render():
    """渲染数据库管理页面"""
    st.title("🗄️ 数据库管理")
    st.markdown("管理数据库连接配置，支持数据库断言和数据准备")
    
    # 初始化DAO
    dao = ApiTestDAO()
    
    # 主要功能选项卡
    tab1, tab2, tab3 = st.tabs(["📋 数据库列表", "➕ 添加数据库", "🧪 连接测试"])
    
    with tab1:
        render_database_list(dao)
    
    with tab2:
        render_add_database(dao)
    
    with tab3:
        render_connection_test(dao)


def render_database_list(dao: ApiTestDAO):
    """渲染数据库列表"""
    st.markdown("### 📋 数据库配置列表")
    
    # 获取所有数据库配置
    db_configs = dao.get_database_configs()
    
    if not db_configs:
        st.info("📝 暂无数据库配置，请添加第一个数据库连接")
        return
    
    # 显示数据库配置
    for i, config in enumerate(db_configs):
        with st.expander(f"🗄️ {config.name} ({config.db_type.value})", expanded=False):
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.markdown(f"**数据库类型**: {config.db_type.value}")
                if config.db_type != DatabaseType.SQLITE:
                    st.markdown(f"**主机地址**: {config.host}:{config.port}")
                    st.markdown(f"**数据库名**: {config.database}")
                    st.markdown(f"**用户名**: {config.username}")
                    st.markdown(f"**字符集**: {config.charset}")
                else:
                    st.markdown(f"**数据库文件**: {config.database}")
                
                if config.connection_params:
                    st.markdown("**连接参数**:")
                    for key, value in config.connection_params.items():
                        st.markdown(f"- `{key}`: {value}")
            
            with col2:
                # 操作按钮
                col_btn1, col_btn2, col_btn3 = st.columns(3)
                
                with col_btn1:
                    if st.button("🧪 测试", key=f"test_db_{i}"):
                        test_database_connection(config)
                
                with col_btn2:
                    if st.button("✏️ 编辑", key=f"edit_db_{i}"):
                        st.session_state.api_test_edit_db_id = config.id
                        st.rerun()
                
                with col_btn3:
                    if st.button("🗑️ 删除", key=f"delete_db_{i}"):
                        if st.session_state.get(f"confirm_delete_db_{i}", False):
                            if dao.delete_database_config(config.id):
                                st.success("✅ 数据库配置删除成功")
                                st.rerun()
                            else:
                                st.error("❌ 数据库配置删除失败")
                        else:
                            st.session_state[f"confirm_delete_db_{i}"] = True
                            st.warning("⚠️ 再次点击确认删除")
    
    # 处理编辑数据库
    if st.session_state.get("api_test_edit_db_id"):
        render_edit_database_modal(dao, st.session_state.api_test_edit_db_id)


def render_add_database(dao: ApiTestDAO):
    """渲染添加数据库"""
    st.markdown("### ➕ 添加数据库配置")
    
    with st.form("add_database_form"):
        # 基本信息
        name = st.text_input(
            "配置名称 *",
            placeholder="如: 测试数据库、生产数据库"
        )
        
        db_type = st.selectbox(
            "数据库类型 *",
            options=[t.value for t in DatabaseType],
            index=0
        )
        
        # 根据数据库类型显示不同的配置项
        if db_type == DatabaseType.SQLITE.value:
            database = st.text_input(
                "数据库文件路径 *",
                placeholder="如: /path/to/database.db 或 database.db"
            )
            host = ""
            port = 0
            username = ""
            password = ""
            charset = ""
        else:
            col1, col2 = st.columns(2)
            
            with col1:
                host = st.text_input(
                    "主机地址 *",
                    placeholder="如: localhost 或 *************"
                )
                
                database = st.text_input(
                    "数据库名 *",
                    placeholder="数据库名称"
                )
                
                charset = st.text_input(
                    "字符集",
                    value="utf8mb4" if db_type == DatabaseType.MYSQL.value else "utf8",
                    placeholder="字符编码"
                )
            
            with col2:
                # 设置默认端口
                default_port = 3306
                if db_type == DatabaseType.POSTGRESQL.value:
                    default_port = 5432
                elif db_type == DatabaseType.ORACLE.value:
                    default_port = 1521
                elif db_type == DatabaseType.SQL_SERVER.value:
                    default_port = 1433
                
                port = st.number_input(
                    "端口 *",
                    min_value=1,
                    max_value=65535,
                    value=default_port
                )
                
                username = st.text_input(
                    "用户名 *",
                    placeholder="数据库用户名"
                )
                
                password = st.text_input(
                    "密码 *",
                    type="password",
                    placeholder="数据库密码"
                )
        
        # 高级配置
        with st.expander("🔧 高级配置 (可选)", expanded=False):
            connection_params_json = st.text_area(
                "连接参数 (JSON格式)",
                placeholder='{"autocommit": true, "connect_timeout": 10}',
                height=100,
                help="额外的数据库连接参数，JSON格式"
            )
        
        # 提交按钮
        col_btn1, col_btn2 = st.columns([1, 3])
        
        with col_btn1:
            submitted = st.form_submit_button(
                "✅ 添加配置",
                type="primary",
                use_container_width=True
            )
        
        if submitted:
            if name.strip() and (database.strip() if db_type == DatabaseType.SQLITE.value else 
                               host.strip() and database.strip() and username.strip()):
                try:
                    # 解析连接参数
                    connection_params = {}
                    if connection_params_json.strip():
                        connection_params = json.loads(connection_params_json)
                    
                    # 创建数据库配置
                    config = DatabaseConfig(
                        id=str(datetime.now().timestamp()),
                        name=name.strip(),
                        db_type=DatabaseType(db_type),
                        host=host.strip(),
                        port=port,
                        database=database.strip(),
                        username=username.strip(),
                        password=password.strip(),
                        charset=charset.strip() or "utf8mb4",
                        connection_params=connection_params
                    )
                    
                    # 保存到数据库
                    if dao.create_database_config(config):
                        st.success("✅ 数据库配置添加成功")
                        st.rerun()
                    else:
                        st.error("❌ 数据库配置添加失败，请重试")
                
                except json.JSONDecodeError as e:
                    st.error(f"❌ 连接参数JSON格式错误: {e}")
                except Exception as e:
                    st.error(f"❌ 添加失败: {e}")
            else:
                st.error("❌ 请填写所有必填项")


def render_connection_test(dao: ApiTestDAO):
    """渲染连接测试"""
    st.markdown("### 🧪 数据库连接测试")
    
    # 获取所有数据库配置
    db_configs = dao.get_database_configs()
    
    if not db_configs:
        st.info("📝 请先添加数据库配置")
        return
    
    # 选择要测试的数据库
    config_options = {config.id: f"{config.name} ({config.db_type.value})" for config in db_configs}
    
    selected_config_id = st.selectbox(
        "选择数据库配置",
        options=list(config_options.keys()),
        format_func=lambda x: config_options[x]
    )
    
    selected_config = next((c for c in db_configs if c.id == selected_config_id), None)
    
    if selected_config:
        col1, col2 = st.columns([1, 3])
        
        with col1:
            if st.button("🧪 测试连接", type="primary", use_container_width=True):
                test_database_connection(selected_config)
        
        # 显示配置信息
        st.markdown("**配置信息**:")
        st.json({
            "名称": selected_config.name,
            "类型": selected_config.db_type.value,
            "主机": selected_config.host if selected_config.host else "N/A",
            "端口": selected_config.port if selected_config.port else "N/A",
            "数据库": selected_config.database,
            "用户名": selected_config.username if selected_config.username else "N/A"
        })


def render_edit_database_modal(dao: ApiTestDAO, config_id: str):
    """渲染编辑数据库模态框"""
    config = dao.get_database_config_by_id(config_id)
    if not config:
        st.error("❌ 数据库配置不存在")
        st.session_state.api_test_edit_db_id = None
        return
    
    st.markdown("---")
    st.markdown("### ✏️ 编辑数据库配置")
    
    with st.form("edit_database_form"):
        # 基本信息
        name = st.text_input("配置名称 *", value=config.name)
        
        # 数据库类型不允许修改
        st.text_input("数据库类型", value=config.db_type.value, disabled=True)
        
        # 根据数据库类型显示配置项
        if config.db_type == DatabaseType.SQLITE:
            database = st.text_input("数据库文件路径 *", value=config.database)
            host = config.host
            port = config.port
            username = config.username
            password = config.password
            charset = config.charset
        else:
            col1, col2 = st.columns(2)
            
            with col1:
                host = st.text_input("主机地址 *", value=config.host)
                database = st.text_input("数据库名 *", value=config.database)
                charset = st.text_input("字符集", value=config.charset)
            
            with col2:
                port = st.number_input("端口 *", min_value=1, max_value=65535, value=config.port)
                username = st.text_input("用户名 *", value=config.username)
                password = st.text_input("密码 *", type="password", value=config.password)
        
        # 连接参数
        connection_params_json = st.text_area(
            "连接参数 (JSON格式)",
            value=json.dumps(config.connection_params, indent=2) if config.connection_params else "{}",
            height=100
        )
        
        col_btn1, col_btn2, col_btn3 = st.columns([1, 1, 2])
        
        with col_btn1:
            submitted = st.form_submit_button("💾 保存", type="primary")
        
        with col_btn2:
            cancelled = st.form_submit_button("❌ 取消")
        
        if submitted:
            if name.strip():
                try:
                    # 解析连接参数
                    connection_params = json.loads(connection_params_json) if connection_params_json.strip() else {}
                    
                    # 更新配置
                    config.name = name.strip()
                    config.host = host.strip()
                    config.port = port
                    config.database = database.strip()
                    config.username = username.strip()
                    config.password = password.strip()
                    config.charset = charset.strip()
                    config.connection_params = connection_params
                    
                    if dao.update_database_config(config):
                        st.success("✅ 数据库配置更新成功")
                        st.session_state.api_test_edit_db_id = None
                        st.rerun()
                    else:
                        st.error("❌ 数据库配置更新失败")
                
                except json.JSONDecodeError as e:
                    st.error(f"❌ 连接参数JSON格式错误: {e}")
            else:
                st.error("❌ 配置名称不能为空")
        
        if cancelled:
            st.session_state.api_test_edit_db_id = None
            st.rerun()


def test_database_connection(config: DatabaseConfig):
    """测试数据库连接"""
    with st.spinner("🔍 正在测试数据库连接..."):
        try:
            success, message = db_manager.test_connection(config)
            
            if success:
                st.success(f"✅ {message}")
            else:
                st.error(f"❌ {message}")
        
        except Exception as e:
            st.error(f"❌ 连接测试失败: {e}")


if __name__ == "__main__":
    render()
