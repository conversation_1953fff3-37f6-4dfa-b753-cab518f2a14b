#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
定时任务管理页面 - 简化版
"""

import streamlit as st
from datetime import datetime, timedelta
import json


def render():
    """渲染定时任务管理页面"""
    st.title("⏰ 定时任务")
    st.markdown("配置和管理接口测试的定时执行任务")
    
    # 检查依赖
    try:
        import schedule
        dependency_ok = True
    except ImportError:
        dependency_ok = False
    
    if not dependency_ok:
        st.error("❌ 缺少必要依赖")
        st.markdown("请安装schedule模块:")
        st.code("pip install schedule")
        return
    
    # 功能选项卡
    tab1, tab2, tab3 = st.tabs(["📋 任务列表", "➕ 创建任务", "📖 使用说明"])
    
    with tab1:
        render_task_list()
    
    with tab2:
        render_create_task()
    
    with tab3:
        render_usage_guide()


def render_task_list():
    """渲染任务列表"""
    st.markdown("### 📋 定时任务列表")
    
    # 从session state获取任务列表
    if "scheduled_tasks" not in st.session_state:
        st.session_state.scheduled_tasks = []
    
    tasks = st.session_state.scheduled_tasks
    
    if not tasks:
        st.info("📝 暂无定时任务，请创建第一个定时任务")
        return
    
    # 显示任务列表
    for i, task in enumerate(tasks):
        with st.expander(f"⏰ {task['name']} - {task['schedule_type']}", expanded=False):
            col1, col2 = st.columns([3, 1])
            
            with col1:
                st.markdown(f"**描述**: {task['description']}")
                st.markdown(f"**调度类型**: {task['schedule_type']}")
                st.markdown(f"**状态**: {'✅ 启用' if task['enabled'] else '❌ 禁用'}")
                st.markdown(f"**创建时间**: {task['created_at']}")
                
                # 显示调度配置
                if task['schedule_type'] == 'daily':
                    st.markdown(f"**执行时间**: 每天 {task['schedule_config'].get('time', '09:00')}")
                elif task['schedule_type'] == 'weekly':
                    day = task['schedule_config'].get('day', 'monday')
                    time_str = task['schedule_config'].get('time', '09:00')
                    st.markdown(f"**执行时间**: 每周{day} {time_str}")
                elif task['schedule_type'] == 'interval':
                    interval = task['schedule_config'].get('interval', 60)
                    unit = task['schedule_config'].get('unit', 'minutes')
                    st.markdown(f"**执行间隔**: 每{interval}{unit}")
            
            with col2:
                # 操作按钮
                if st.button("🗑️ 删除", key=f"delete_task_{i}"):
                    if st.session_state.get(f"confirm_delete_{i}", False):
                        st.session_state.scheduled_tasks.pop(i)
                        st.success("✅ 任务删除成功")
                        st.rerun()
                    else:
                        st.session_state[f"confirm_delete_{i}"] = True
                        st.warning("⚠️ 再次点击确认删除")
                
                # 启用/禁用按钮
                if task['enabled']:
                    if st.button("⏸️ 禁用", key=f"disable_task_{i}"):
                        st.session_state.scheduled_tasks[i]['enabled'] = False
                        st.success("✅ 任务已禁用")
                        st.rerun()
                else:
                    if st.button("▶️ 启用", key=f"enable_task_{i}"):
                        st.session_state.scheduled_tasks[i]['enabled'] = True
                        st.success("✅ 任务已启用")
                        st.rerun()


def render_create_task():
    """渲染创建任务"""
    st.markdown("### ➕ 创建定时任务")
    
    with st.form("create_task_form"):
        # 基本信息
        col1, col2 = st.columns(2)
        
        with col1:
            name = st.text_input(
                "任务名称 *",
                placeholder="如: 每日回归测试、核心接口巡检"
            )
            
            schedule_type = st.selectbox(
                "调度类型 *",
                options=["daily", "weekly", "interval"],
                format_func=lambda x: {
                    "daily": "每日执行", 
                    "weekly": "每周执行",
                    "interval": "间隔执行"
                }.get(x, x)
            )
        
        with col2:
            description = st.text_area(
                "任务描述",
                placeholder="描述任务的目的和执行内容"
            )
        
        # 调度配置
        st.markdown("**调度配置**")
        
        if schedule_type == "daily":
            execution_time = st.text_input("执行时间", value="09:00", placeholder="HH:MM格式，如: 09:00")
            schedule_config = {"time": execution_time}
        
        elif schedule_type == "weekly":
            col_week1, col_week2 = st.columns(2)
            with col_week1:
                day = st.selectbox(
                    "执行日期",
                    options=["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"],
                    format_func=lambda x: {
                        "monday": "周一", "tuesday": "周二", "wednesday": "周三",
                        "thursday": "周四", "friday": "周五", "saturday": "周六", "sunday": "周日"
                    }.get(x, x)
                )
            with col_week2:
                execution_time = st.text_input("执行时间", value="09:00", placeholder="HH:MM格式")
            
            schedule_config = {"day": day, "time": execution_time}
        
        elif schedule_type == "interval":
            col_int1, col_int2 = st.columns(2)
            with col_int1:
                interval = st.number_input("间隔数值", min_value=1, value=60)
            with col_int2:
                unit = st.selectbox("时间单位", options=["minutes", "hours", "days"],
                                  format_func=lambda x: {"minutes": "分钟", "hours": "小时", "days": "天"}.get(x, x))
            
            schedule_config = {"interval": interval, "unit": unit}
        
        # 执行配置
        st.markdown("**执行配置**")
        
        project_name = st.text_input("项目名称", placeholder="要执行的项目名称")
        environment_name = st.text_input("环境名称", placeholder="执行环境名称")
        tags = st.text_input("标签筛选", placeholder="用逗号分隔多个标签")
        
        # 提交按钮
        submitted = st.form_submit_button("✅ 创建任务", type="primary")
        
        if submitted:
            if name.strip():
                try:
                    # 创建任务
                    task = {
                        "id": f"task_{len(st.session_state.get('scheduled_tasks', []))}",
                        "name": name.strip(),
                        "description": description.strip(),
                        "schedule_type": schedule_type,
                        "schedule_config": schedule_config,
                        "project_name": project_name.strip(),
                        "environment_name": environment_name.strip(),
                        "tags": [tag.strip() for tag in tags.split(",") if tag.strip()],
                        "enabled": True,
                        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "execution_count": 0,
                        "success_count": 0,
                        "failure_count": 0
                    }
                    
                    # 添加到session state
                    if "scheduled_tasks" not in st.session_state:
                        st.session_state.scheduled_tasks = []
                    
                    st.session_state.scheduled_tasks.append(task)
                    
                    st.success("✅ 定时任务创建成功")
                    st.rerun()
                
                except Exception as e:
                    st.error(f"❌ 创建失败: {e}")
            else:
                st.error("❌ 任务名称不能为空")


def render_usage_guide():
    """渲染使用说明"""
    st.markdown("### 📖 使用说明")
    
    st.markdown("""
    #### 🎯 功能概述
    定时任务功能允许您配置自动化的接口测试执行计划，支持多种调度类型。
    
    #### 📋 调度类型说明
    
    **每日执行 (daily)**
    - 每天在指定时间执行一次
    - 适用于日常回归测试、健康检查
    - 配置示例: 每天09:00执行
    
    **每周执行 (weekly)**
    - 每周在指定日期和时间执行一次
    - 适用于周期性的完整测试
    - 配置示例: 每周一09:00执行
    
    **间隔执行 (interval)**
    - 按固定间隔重复执行
    - 适用于频繁的监控检查
    - 配置示例: 每30分钟执行一次
    
    #### 🔧 配置建议
    
    1. **任务命名**: 使用清晰的命名规则，如"每日回归测试"、"核心接口巡检"
    2. **时间设置**: 避免在业务高峰期执行大量测试
    3. **标签筛选**: 使用标签来精确控制要执行的测试用例
    4. **环境选择**: 确保选择正确的测试环境
    
    #### ⚠️ 注意事项
    
    - 当前版本为简化版本，任务配置保存在浏览器会话中
    - 刷新页面后任务配置会丢失
    - 实际的定时执行需要后台服务支持
    - 建议在生产环境中使用专业的任务调度系统
    
    #### 🚀 高级功能
    
    如需要完整的定时任务功能，包括：
    - 持久化存储
    - 实际的定时执行
    - 执行历史记录
    - 失败重试机制
    - 邮件/钉钉通知
    
    请联系系统管理员升级到完整版本。
    """)


if __name__ == "__main__":
    render()
