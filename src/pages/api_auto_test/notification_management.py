#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
通知管理页面
"""

import streamlit as st
import json
from typing import List

from src.components.api_test.notification import (
    NotificationManager, NotificationConfig, NotificationType,
    notification_manager
)


def render():
    """渲染通知管理页面"""
    st.title("📢 通知管理")
    st.markdown("配置测试结果通知，支持钉钉、企业微信等多种通知方式")
    
    # 主要功能选项卡
    tab1, tab2, tab3 = st.tabs(["📋 通知配置", "➕ 添加配置", "🧪 测试通知"])
    
    with tab1:
        render_notification_list()
    
    with tab2:
        render_add_notification()
    
    with tab3:
        render_test_notification()


def render_notification_list():
    """渲染通知配置列表"""
    st.markdown("### 📋 通知配置列表")
    
    configs = notification_manager.configs
    
    if not configs:
        st.info("📝 暂无通知配置，请添加第一个通知配置")
        return
    
    # 显示配置列表
    for i, (name, config) in enumerate(configs.items()):
        with st.expander(f"📢 {name} ({config.notification_type.value})", expanded=False):
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.markdown(f"**类型**: {config.notification_type.value}")
                st.markdown(f"**Webhook URL**: {config.webhook_url[:50]}...")
                st.markdown(f"**状态**: {'✅ 启用' if config.enabled else '❌ 禁用'}")
                
                if config.notification_type == NotificationType.DINGTALK:
                    if config.secret:
                        st.markdown(f"**签名密钥**: 已配置")
                    if config.at_mobiles:
                        st.markdown(f"**@手机号**: {', '.join(config.at_mobiles)}")
                    if config.at_all:
                        st.markdown(f"**@所有人**: 是")
                
                elif config.notification_type == NotificationType.WECHAT_WORK:
                    if config.mentioned_list:
                        st.markdown(f"**@用户**: {', '.join(config.mentioned_list)}")
                    if config.mentioned_mobile_list:
                        st.markdown(f"**@手机号**: {', '.join(config.mentioned_mobile_list)}")
            
            with col2:
                # 操作按钮
                col_btn1, col_btn2, col_btn3 = st.columns(3)
                
                with col_btn1:
                    if st.button("🧪 测试", key=f"test_notification_{i}"):
                        test_single_notification(name, config)
                
                with col_btn2:
                    new_status = not config.enabled
                    status_text = "启用" if new_status else "禁用"
                    if st.button(f"{'✅' if new_status else '❌'} {status_text}", key=f"toggle_notification_{i}"):
                        config.enabled = new_status
                        st.success(f"✅ 通知配置已{status_text}")
                        st.rerun()
                
                with col_btn3:
                    if st.button("🗑️ 删除", key=f"delete_notification_{i}"):
                        if st.session_state.get(f"confirm_delete_notification_{i}", False):
                            notification_manager.remove_config(name)
                            st.success("✅ 通知配置删除成功")
                            st.rerun()
                        else:
                            st.session_state[f"confirm_delete_notification_{i}"] = True
                            st.warning("⚠️ 再次点击确认删除")


def render_add_notification():
    """渲染添加通知配置"""
    st.markdown("### ➕ 添加通知配置")
    
    with st.form("add_notification_form"):
        # 基本信息
        name = st.text_input(
            "配置名称 *",
            placeholder="如: 测试团队钉钉群、开发团队企业微信"
        )
        
        notification_type = st.selectbox(
            "通知类型 *",
            options=[t.value for t in NotificationType],
            format_func=lambda x: {
                "dingtalk": "钉钉机器人",
                "wechat_work": "企业微信机器人",
                "webhook": "自定义Webhook"
            }.get(x, x)
        )
        
        webhook_url = st.text_input(
            "Webhook URL *",
            placeholder="机器人的Webhook地址"
        )
        
        # 根据通知类型显示不同配置
        if notification_type == "dingtalk":
            st.markdown("**钉钉机器人配置**")
            
            secret = st.text_input(
                "签名密钥",
                type="password",
                placeholder="钉钉机器人的加签密钥（可选）"
            )
            
            at_all = st.checkbox("@所有人")
            
            at_mobiles_input = st.text_input(
                "@指定手机号",
                placeholder="多个手机号用逗号分隔，如: 13800138000,13900139000"
            )
            at_mobiles = [mobile.strip() for mobile in at_mobiles_input.split(",") if mobile.strip()]
        
        elif notification_type == "wechat_work":
            st.markdown("**企业微信机器人配置**")
            
            mentioned_list_input = st.text_input(
                "@指定用户",
                placeholder="多个用户ID用逗号分隔"
            )
            mentioned_list = [user.strip() for user in mentioned_list_input.split(",") if user.strip()]
            
            mentioned_mobile_list_input = st.text_input(
                "@指定手机号",
                placeholder="多个手机号用逗号分隔"
            )
            mentioned_mobile_list = [mobile.strip() for mobile in mentioned_mobile_list_input.split(",") if mobile.strip()]
        
        # 提交按钮
        col_btn1, col_btn2 = st.columns([1, 3])
        
        with col_btn1:
            submitted = st.form_submit_button(
                "✅ 添加配置",
                type="primary",
                use_container_width=True
            )
        
        if submitted:
            if name.strip() and webhook_url.strip():
                try:
                    # 创建通知配置
                    if notification_type == "dingtalk":
                        config = NotificationConfig(
                            name=name.strip(),
                            notification_type=NotificationType.DINGTALK,
                            webhook_url=webhook_url.strip(),
                            secret=secret.strip() if secret.strip() else None,
                            at_mobiles=at_mobiles,
                            at_all=at_all
                        )
                    elif notification_type == "wechat_work":
                        config = NotificationConfig(
                            name=name.strip(),
                            notification_type=NotificationType.WECHAT_WORK,
                            webhook_url=webhook_url.strip(),
                            mentioned_list=mentioned_list,
                            mentioned_mobile_list=mentioned_mobile_list
                        )
                    else:  # webhook
                        config = NotificationConfig(
                            name=name.strip(),
                            notification_type=NotificationType.WEBHOOK,
                            webhook_url=webhook_url.strip()
                        )
                    
                    # 添加到管理器
                    notification_manager.add_config(config)
                    
                    st.success("✅ 通知配置添加成功")
                    st.rerun()
                
                except Exception as e:
                    st.error(f"❌ 添加失败: {e}")
            else:
                st.error("❌ 请填写配置名称和Webhook URL")


def render_test_notification():
    """渲染测试通知"""
    st.markdown("### 🧪 测试通知")
    
    configs = notification_manager.configs
    
    if not configs:
        st.info("📝 请先添加通知配置")
        return
    
    # 选择要测试的配置
    config_options = {name: f"{name} ({config.notification_type.value})" 
                     for name, config in configs.items() if config.enabled}
    
    if not config_options:
        st.warning("⚠️ 没有启用的通知配置")
        return
    
    selected_configs = st.multiselect(
        "选择要测试的通知配置",
        options=list(config_options.keys()),
        format_func=lambda x: config_options[x],
        default=list(config_options.keys())[:1]
    )
    
    col1, col2 = st.columns([1, 3])
    
    with col1:
        if st.button("🧪 发送测试通知", type="primary", use_container_width=True):
            if selected_configs:
                send_test_notifications(selected_configs)
            else:
                st.warning("⚠️ 请选择至少一个通知配置")
    
    # 显示测试消息预览
    st.markdown("**测试消息预览**:")
    test_report_data = {
        "project_name": "示例项目",
        "environment_name": "测试环境",
        "total_cases": 10,
        "passed_cases": 8,
        "failed_cases": 2,
        "skipped_cases": 0,
        "start_time": "2025-07-28 14:30:00",
        "end_time": "2025-07-28 14:32:15",
        "total_duration": 135.5,
        "failed_cases_detail": [
            {"case_name": "用户登录接口", "error_message": "状态码不匹配，期望200，实际500"},
            {"case_name": "获取用户信息", "error_message": "响应时间超时"}
        ]
    }
    
    st.json(test_report_data)


def test_single_notification(name: str, config: NotificationConfig):
    """测试单个通知配置"""
    test_report_data = {
        "project_name": "测试项目",
        "environment_name": "测试环境",
        "total_cases": 5,
        "passed_cases": 4,
        "failed_cases": 1,
        "skipped_cases": 0,
        "start_time": "2025-07-28 14:30:00",
        "end_time": "2025-07-28 14:31:30",
        "total_duration": 90.0,
        "failed_cases_detail": [
            {"case_name": "示例失败用例", "error_message": "这是一个测试通知"}
        ]
    }
    
    with st.spinner(f"正在发送测试通知到 {name}..."):
        results = notification_manager.send_test_result_notification(test_report_data, [name])
        
        if results.get(name, False):
            st.success(f"✅ 测试通知发送成功: {name}")
        else:
            st.error(f"❌ 测试通知发送失败: {name}")


def send_test_notifications(config_names: List[str]):
    """发送测试通知"""
    test_report_data = {
        "project_name": "测试项目",
        "environment_name": "测试环境",
        "total_cases": 10,
        "passed_cases": 8,
        "failed_cases": 2,
        "skipped_cases": 0,
        "start_time": "2025-07-28 14:30:00",
        "end_time": "2025-07-28 14:32:15",
        "total_duration": 135.5,
        "failed_cases_detail": [
            {"case_name": "用户登录接口", "error_message": "状态码不匹配，期望200，实际500"},
            {"case_name": "获取用户信息", "error_message": "响应时间超时"}
        ]
    }
    
    with st.spinner("正在发送测试通知..."):
        results = notification_manager.send_test_result_notification(test_report_data, config_names)
        
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        
        if success_count == total_count:
            st.success(f"✅ 所有通知发送成功 ({success_count}/{total_count})")
        elif success_count > 0:
            st.warning(f"⚠️ 部分通知发送成功 ({success_count}/{total_count})")
        else:
            st.error(f"❌ 所有通知发送失败 ({success_count}/{total_count})")
        
        # 显示详细结果
        for config_name, success in results.items():
            if success:
                st.success(f"✅ {config_name}: 发送成功")
            else:
                st.error(f"❌ {config_name}: 发送失败")


if __name__ == "__main__":
    render()
