#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
环境管理页面
"""

import streamlit as st
import json
from datetime import datetime

from src.components.api_test.models import Environment, KeyValue
from src.components.api_test.dao import ApiTestDAO


def render():
    """渲染环境管理页面"""
    st.title("🌐 环境管理")
    st.markdown("管理测试环境配置，支持多环境切换")
    
    # 初始化DAO
    dao = ApiTestDAO()
    
    # 主要功能选项卡
    tab1, tab2 = st.tabs(["📋 环境列表", "➕ 创建环境"])
    
    with tab1:
        render_environment_list(dao)
    
    with tab2:
        render_create_environment(dao)


def render_environment_list(dao: ApiTestDAO):
    """渲染环境列表"""
    st.markdown("### 📋 环境列表")
    
    # 获取所有环境
    environments = dao.get_environments()
    
    if not environments:
        st.info("📝 暂无环境配置，请创建第一个环境")
        return
    
    # 当前环境选择
    current_env_id = st.session_state.get("api_test_current_env_id", "")
    if not current_env_id and environments:
        # 默认选择第一个环境
        current_env_id = environments[0].id
        st.session_state.api_test_current_env_id = current_env_id
    
    env_options = {env.id: f"{'🌟 ' if env.is_default else ''}{env.name}" for env in environments}
    
    selected_env_id = st.selectbox(
        "🎯 当前使用环境",
        options=list(env_options.keys()),
        format_func=lambda x: env_options[x],
        index=list(env_options.keys()).index(current_env_id) if current_env_id in env_options else 0,
        key="current_env_selector"
    )
    
    if selected_env_id != current_env_id:
        st.session_state.api_test_current_env_id = selected_env_id
        st.rerun()
    
    st.markdown("---")
    
    # 环境详情展示
    for i, env in enumerate(environments):
        is_current = env.id == selected_env_id
        
        with st.expander(
            f"{'🌟 ' if env.is_default else ''}{'🎯 ' if is_current else ''}🌐 {env.name}",
            expanded=is_current
        ):
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.markdown(f"**描述**: {env.description or '无描述'}")
                st.markdown(f"**基础URL**: `{env.base_url or '未设置'}`")
                st.markdown(f"**超时时间**: {env.timeout}秒")
                st.markdown(f"**默认环境**: {'是' if env.is_default else '否'}")
                st.markdown(f"**创建时间**: {env.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 环境变量
                if env.variables:
                    st.markdown("**环境变量**:")
                    for key, value in env.variables.items():
                        # 敏感信息脱敏
                        display_value = value
                        if any(sensitive in key.lower() for sensitive in ['password', 'token', 'secret', 'key']):
                            display_value = '*' * len(value) if value else ''
                        st.markdown(f"- `{key}`: {display_value}")
                
                # 默认请求头
                if env.headers:
                    st.markdown("**默认请求头**:")
                    for header in env.headers:
                        if header.enabled:
                            st.markdown(f"- `{header.key}`: {header.value}")
            
            with col2:
                # 操作按钮
                col_btn1, col_btn2, col_btn3 = st.columns(3)
                
                with col_btn1:
                    if st.button("✏️ 编辑", key=f"edit_env_{i}"):
                        st.session_state.api_test_edit_env_id = env.id
                        st.rerun()
                
                with col_btn2:
                    if not env.is_default:
                        if st.button("⭐ 设为默认", key=f"default_env_{i}"):
                            set_default_environment(dao, env.id)
                            st.rerun()
                
                with col_btn3:
                    if st.button("🗑️ 删除", key=f"delete_env_{i}"):
                        if st.session_state.get(f"confirm_delete_env_{i}", False):
                            # 确认删除
                            if delete_environment(dao, env.id):
                                st.success("✅ 环境删除成功")
                                st.rerun()
                            else:
                                st.error("❌ 环境删除失败")
                        else:
                            st.session_state[f"confirm_delete_env_{i}"] = True
                            st.warning("⚠️ 再次点击确认删除")
    
    # 处理编辑环境
    if st.session_state.get("api_test_edit_env_id"):
        render_edit_environment_modal(dao, st.session_state.api_test_edit_env_id)


def render_edit_environment_modal(dao: ApiTestDAO, env_id: str):
    """渲染编辑环境模态框"""
    environments = dao.get_environments()
    env = next((e for e in environments if e.id == env_id), None)
    
    if not env:
        st.error("❌ 环境不存在")
        st.session_state.api_test_edit_env_id = None
        return
    
    st.markdown("---")
    st.markdown("### ✏️ 编辑环境")
    
    with st.form("edit_environment_form"):
        # 基本信息
        name = st.text_input("环境名称 *", value=env.name)
        description = st.text_area("环境描述", value=env.description or "", height=80)
        base_url = st.text_input("基础URL", value=env.base_url or "", placeholder="https://api.example.com")
        timeout = st.number_input("超时时间(秒)", min_value=1, max_value=300, value=env.timeout)
        is_default = st.checkbox("设为默认环境", value=env.is_default)
        
        # 环境变量
        st.markdown("**环境变量**")
        variables_json = st.text_area(
            "变量配置 (JSON格式)",
            value=json.dumps(env.variables, indent=2, ensure_ascii=False) if env.variables else "{}",
            height=150,
            help="格式: {\"key1\": \"value1\", \"key2\": \"value2\"}"
        )
        
        # 默认请求头
        st.markdown("**默认请求头**")
        headers_data = []
        for header in env.headers:
            headers_data.append({
                "key": header.key,
                "value": header.value,
                "enabled": header.enabled,
                "description": header.description
            })
        
        headers_json = st.text_area(
            "请求头配置 (JSON格式)",
            value=json.dumps(headers_data, indent=2, ensure_ascii=False) if headers_data else "[]",
            height=150,
            help="格式: [{\"key\": \"Content-Type\", \"value\": \"application/json\", \"enabled\": true}]"
        )
        
        col_btn1, col_btn2, col_btn3 = st.columns([1, 1, 2])
        
        with col_btn1:
            submitted = st.form_submit_button("💾 保存", type="primary")
        
        with col_btn2:
            cancelled = st.form_submit_button("❌ 取消")
        
        if submitted:
            if name.strip():
                try:
                    # 解析JSON
                    variables = json.loads(variables_json) if variables_json.strip() else {}
                    headers_list = json.loads(headers_json) if headers_json.strip() else []
                    
                    # 构建headers对象
                    headers = []
                    for h in headers_list:
                        headers.append(KeyValue(
                            key=h.get("key", ""),
                            value=h.get("value", ""),
                            enabled=h.get("enabled", True),
                            description=h.get("description", "")
                        ))
                    
                    # 更新环境
                    env.name = name.strip()
                    env.description = description.strip()
                    env.base_url = base_url.strip()
                    env.timeout = timeout
                    env.is_default = is_default
                    env.variables = variables
                    env.headers = headers
                    env.updated_at = datetime.now()
                    
                    if update_environment(dao, env):
                        st.success("✅ 环境更新成功")
                        st.session_state.api_test_edit_env_id = None
                        st.rerun()
                    else:
                        st.error("❌ 环境更新失败")
                
                except json.JSONDecodeError as e:
                    st.error(f"❌ JSON格式错误: {e}")
            else:
                st.error("❌ 环境名称不能为空")
        
        if cancelled:
            st.session_state.api_test_edit_env_id = None
            st.rerun()


def render_create_environment(dao: ApiTestDAO):
    """渲染创建环境"""
    st.markdown("### ➕ 创建新环境")
    
    with st.form("create_environment_form"):
        # 基本信息
        name = st.text_input(
            "环境名称 *",
            placeholder="如: 开发环境、测试环境、生产环境"
        )
        
        description = st.text_area(
            "环境描述",
            placeholder="请输入环境描述",
            height=80
        )
        
        base_url = st.text_input(
            "基础URL",
            placeholder="https://api.example.com",
            help="所有接口请求的基础地址"
        )
        
        col1, col2 = st.columns(2)
        with col1:
            timeout = st.number_input(
                "超时时间(秒)",
                min_value=1,
                max_value=300,
                value=30
            )
        
        with col2:
            is_default = st.checkbox("设为默认环境")
        
        # 环境变量
        st.markdown("**环境变量 (可选)**")
        variables_json = st.text_area(
            "变量配置 (JSON格式)",
            placeholder='{"host": "api.example.com", "version": "v1"}',
            height=100,
            help="格式: {\"key1\": \"value1\", \"key2\": \"value2\"}"
        )
        
        # 默认请求头
        st.markdown("**默认请求头 (可选)**")
        headers_json = st.text_area(
            "请求头配置 (JSON格式)",
            placeholder='[{"key": "Content-Type", "value": "application/json", "enabled": true}]',
            height=100,
            help="格式: [{\"key\": \"header名\", \"value\": \"header值\", \"enabled\": true}]"
        )
        
        # 提交按钮
        col_btn1, col_btn2 = st.columns([1, 3])
        
        with col_btn1:
            submitted = st.form_submit_button(
                "✅ 创建环境",
                type="primary",
                use_container_width=True
            )
        
        if submitted:
            if name.strip():
                try:
                    # 解析JSON
                    variables = json.loads(variables_json) if variables_json.strip() else {}
                    headers_list = json.loads(headers_json) if headers_json.strip() else []
                    
                    # 构建headers对象
                    headers = []
                    for h in headers_list:
                        headers.append(KeyValue(
                            key=h.get("key", ""),
                            value=h.get("value", ""),
                            enabled=h.get("enabled", True),
                            description=h.get("description", "")
                        ))
                    
                    # 创建环境对象
                    environment = Environment(
                        name=name.strip(),
                        description=description.strip(),
                        base_url=base_url.strip(),
                        timeout=timeout,
                        is_default=is_default,
                        variables=variables,
                        headers=headers
                    )
                    
                    # 保存到数据库
                    if dao.create_environment(environment):
                        st.success("✅ 环境创建成功")
                        st.rerun()
                    else:
                        st.error("❌ 环境创建失败，请重试")
                
                except json.JSONDecodeError as e:
                    st.error(f"❌ JSON格式错误: {e}")
            else:
                st.error("❌ 环境名称不能为空")


def set_default_environment(dao: ApiTestDAO, env_id: str) -> bool:
    """设置默认环境"""
    return dao.set_default_environment(env_id)


def update_environment(dao: ApiTestDAO, environment: Environment) -> bool:
    """更新环境"""
    return dao.update_environment(environment)


def delete_environment(dao: ApiTestDAO, env_id: str) -> bool:
    """删除环境"""
    return dao.delete_environment(env_id)


if __name__ == "__main__":
    render()
