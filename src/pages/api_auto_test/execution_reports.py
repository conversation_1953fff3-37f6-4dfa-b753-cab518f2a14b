#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
执行报告页面
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta

from src.components.api_test.models import *
from src.components.api_test.dao import ApiTestDAO


def render():
    """渲染执行报告页面"""
    st.title("📊 执行报告")
    st.markdown("查看和分析接口测试执行报告")
    
    # 初始化DAO
    dao = ApiTestDAO()
    
    # 主要功能选项卡
    tab1, tab2, tab3 = st.tabs(["📋 报告列表", "📊 报告详情", "📈 趋势分析"])
    
    with tab1:
        render_report_list(dao)
    
    with tab2:
        render_report_details(dao)
    
    with tab3:
        render_trend_analysis(dao)


def render_report_list(dao: ApiTestDAO):
    """渲染报告列表"""
    st.markdown("### 📋 执行报告列表")

    # 获取真实的执行报告
    reports = dao.get_execution_reports(50)
    
    if not reports:
        st.info("📝 暂无执行报告，请先执行测试用例")
        return
    
    # 筛选条件
    col1, col2, col3 = st.columns(3)
    
    with col1:
        env_filter = st.selectbox(
            "环境筛选",
            options=["全部", "开发环境", "测试环境", "预生产环境"],
            key="report_env_filter"
        )
    
    with col2:
        status_filter = st.selectbox(
            "状态筛选", 
            options=["全部", "已完成", "执行中", "已失败"],
            key="report_status_filter"
        )
    
    with col3:
        date_range = st.selectbox(
            "时间范围",
            options=["全部", "今天", "最近7天", "最近30天"],
            key="report_date_filter"
        )
    
    # 过滤报告
    filtered_reports = reports

    if env_filter != "全部":
        filtered_reports = [r for r in filtered_reports if r.environment_name == env_filter]
    
    # 显示报告列表
    if filtered_reports:
        st.markdown(f"**找到 {len(filtered_reports)} 个报告**")
        
        for i, report in enumerate(filtered_reports):
            pass_rate = (report.passed_cases / report.total_cases * 100) if report.total_cases > 0 else 0

            with st.expander(
                f"📊 {report.name} - 通过率 {pass_rate:.1f}%",
                expanded=False
            ):
                col1, col2, col3 = st.columns([2, 1, 1])

                with col1:
                    st.markdown(f"**环境**: {report.environment_name}")
                    st.markdown(f"**执行时间**: {report.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    st.markdown(f"**执行时长**: {report.total_duration:.1f}秒")
                    st.markdown(f"**状态**: 已完成")

                with col2:
                    # 统计信息
                    st.metric("总用例数", report.total_cases)
                    st.metric("通过数", report.passed_cases)
                    st.metric("失败数", report.failed_cases)

                with col3:
                    # 通过率饼图
                    fig = go.Figure(data=[go.Pie(
                        labels=['通过', '失败', '跳过', '错误'],
                        values=[report.passed_cases, report.failed_cases, report.skipped_cases, report.error_cases],
                        hole=0.3,
                        marker_colors=['#00CC96', '#EF553B', '#FFA15A', '#FF6B6B']
                    )])
                    fig.update_layout(
                        height=200,
                        margin=dict(t=20, b=20, l=20, r=20),
                        showlegend=False
                    )
                    st.plotly_chart(fig, use_container_width=True)

                # 操作按钮
                col_btn1, col_btn2, col_btn3 = st.columns(3)

                with col_btn1:
                    if st.button("📊 查看详情", key=f"view_report_{i}"):
                        st.session_state.api_test_selected_report_id = report.id
                        st.rerun()

                with col_btn2:
                    if st.button("📥 导出报告", key=f"export_report_{i}"):
                        export_report(report)

                with col_btn3:
                    if st.button("🗑️ 删除", key=f"delete_report_{i}"):
                        if st.session_state.get(f"confirm_delete_report_{i}", False):
                            if dao.delete_execution_report(report.id):
                                st.success("✅ 报告删除成功")
                                st.rerun()
                            else:
                                st.error("❌ 报告删除失败")
                        else:
                            st.session_state[f"confirm_delete_report_{i}"] = True
                            st.warning("⚠️ 再次点击确认删除")
    else:
        st.warning("🔍 没有找到符合条件的报告")


def render_report_details(dao: ApiTestDAO):
    """渲染报告详情"""
    st.markdown("### 📊 报告详情")

    selected_report_id = st.session_state.get("api_test_selected_report_id")

    if not selected_report_id:
        st.info("💡 请从报告列表中选择一个报告查看详情")
        return

    # 获取真实的报告详情
    report = dao.get_execution_report_by_id(selected_report_id)

    if not report:
        st.error("❌ 报告不存在")
        st.session_state.api_test_selected_report_id = None
        return

    st.markdown("#### 📋 执行概览")
    
    col1, col2, col3, col4 = st.columns(4)

    pass_rate = (report.passed_cases / report.total_cases * 100) if report.total_cases > 0 else 0

    with col1:
        st.metric("总用例数", report.total_cases)

    with col2:
        st.metric("通过数", report.passed_cases)

    with col3:
        st.metric("失败数", report.failed_cases)

    with col4:
        st.metric("通过率", f"{pass_rate:.1f}%")
    
    # 执行时间分布
    st.markdown("#### ⏱️ 响应时间分布")

    if report.case_results:
        # 使用真实的响应时间数据
        response_times = [result.response_time for result in report.case_results if result.response_time]
        case_names = [result.case_name for result in report.case_results if result.response_time]

        if response_times:
            fig = px.bar(
                x=case_names,
                y=response_times,
                title="各用例响应时间",
                labels={'x': '测试用例', 'y': '响应时间(ms)'}
            )
            fig.update_layout(height=400)
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("📊 暂无响应时间数据")
    else:
        st.info("📊 暂无用例执行结果")
    
    # 失败用例详情
    st.markdown("#### ❌ 失败用例详情")

    failed_cases = [result for result in report.case_results if result.status == ExecutionStatus.FAILED]

    if failed_cases:
        failed_data = []
        for case in failed_cases:
            failed_data.append({
                "用例名称": case.case_name,
                "错误信息": case.error_message or "未知错误",
                "响应时间(ms)": case.response_time or 0,
                "跳过原因": case.skip_reason or ""
            })

        df_failed = pd.DataFrame(failed_data)
        st.dataframe(
            df_failed,
            use_container_width=True,
            hide_index=True
        )
    else:
        st.success("🎉 没有失败的用例")
    
    # 断言结果统计
    st.markdown("#### 🎯 断言结果统计")
    
    assertion_stats = {
        "状态码断言": {"通过": 23, "失败": 2},
        "响应时间断言": {"通过": 20, "失败": 5},
        "响应体断言": {"通过": 22, "失败": 3},
        "JSON路径断言": {"通过": 18, "失败": 1}
    }
    
    for assertion_type, stats in assertion_stats.items():
        col1, col2, col3 = st.columns([2, 1, 1])
        with col1:
            st.markdown(f"**{assertion_type}**")
        with col2:
            st.markdown(f"✅ {stats['通过']}")
        with col3:
            st.markdown(f"❌ {stats['失败']}")


def render_trend_analysis(dao: ApiTestDAO):
    """渲染趋势分析"""
    st.markdown("### 📈 趋势分析")
    
    # 通过率趋势
    st.markdown("#### 📊 通过率趋势")
    
    # 模拟趋势数据
    dates = pd.date_range(start='2025-07-22', end='2025-07-28', freq='D')
    pass_rates = [85.2, 88.1, 92.3, 89.7, 91.2, 88.9, 93.3]
    
    fig = px.line(
        x=dates,
        y=pass_rates,
        title="最近7天通过率趋势",
        labels={'x': '日期', 'y': '通过率(%)'}
    )
    fig.update_layout(height=400)
    fig.add_hline(y=90, line_dash="dash", line_color="red", 
                  annotation_text="目标通过率 90%")
    st.plotly_chart(fig, use_container_width=True)
    
    # 执行次数统计
    st.markdown("#### 📈 执行次数统计")
    
    execution_counts = [3, 5, 2, 4, 6, 3, 4]
    
    fig = px.bar(
        x=dates,
        y=execution_counts,
        title="最近7天执行次数",
        labels={'x': '日期', 'y': '执行次数'}
    )
    fig.update_layout(height=400)
    st.plotly_chart(fig, use_container_width=True)
    
    # 平均响应时间趋势
    st.markdown("#### ⏱️ 平均响应时间趋势")
    
    avg_response_times = [145, 132, 158, 142, 139, 151, 148]
    
    fig = px.line(
        x=dates,
        y=avg_response_times,
        title="最近7天平均响应时间趋势",
        labels={'x': '日期', 'y': '平均响应时间(ms)'}
    )
    fig.update_layout(height=400)
    fig.add_hline(y=200, line_dash="dash", line_color="orange",
                  annotation_text="响应时间阈值 200ms")
    st.plotly_chart(fig, use_container_width=True)
    
    # 环境对比
    st.markdown("#### 🌐 环境对比")
    
    env_comparison = {
        "环境": ["开发环境", "测试环境", "预生产环境"],
        "通过率": [92.1, 88.5, 95.2],
        "平均响应时间": [125, 148, 132],
        "执行次数": [15, 12, 8]
    }
    
    df_env = pd.DataFrame(env_comparison)
    
    col1, col2 = st.columns(2)
    
    with col1:
        fig = px.bar(
            df_env,
            x="环境",
            y="通过率",
            title="各环境通过率对比"
        )
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        fig = px.bar(
            df_env,
            x="环境", 
            y="平均响应时间",
            title="各环境响应时间对比"
        )
        st.plotly_chart(fig, use_container_width=True)


def export_report(report):
    """导出报告"""
    try:
        import json
        from datetime import datetime

        # 准备导出数据
        if hasattr(report, 'name'):
            # ExecutionReport对象
            export_data = {
                "报告名称": report.name,
                "环境": report.environment_name,
                "执行时间": report.start_time.strftime('%Y-%m-%d %H:%M:%S'),
                "总用例数": report.total_cases,
                "通过数": report.passed_cases,
                "失败数": report.failed_cases,
                "跳过数": report.skipped_cases,
                "错误数": report.error_cases,
                "通过率": f"{(report.passed_cases / report.total_cases * 100):.1f}%" if report.total_cases > 0 else "0%",
                "执行时长": f"{report.total_duration:.1f}秒",
                "用例结果": [
                    {
                        "用例名称": result.case_name,
                        "状态": result.status.value,
                        "响应时间": f"{result.response_time:.2f}ms" if result.response_time else "N/A",
                        "错误信息": result.error_message or "",
                        "跳过原因": result.skip_reason or ""
                    }
                    for result in report.case_results
                ]
            }
            filename = f"测试报告_{report.name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        else:
            # 字典对象（兼容旧代码）
            export_data = report
            filename = f"测试报告_{report.get('name', 'unknown')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        # 生成JSON文件内容
        json_content = json.dumps(export_data, ensure_ascii=False, indent=2)

        # 提供下载
        st.download_button(
            label="📥 下载JSON报告",
            data=json_content,
            file_name=filename,
            mime="application/json"
        )

        st.success(f"✅ 报告已准备好下载")

    except Exception as e:
        st.error(f"❌ 导出报告失败: {e}")


if __name__ == "__main__":
    render()
