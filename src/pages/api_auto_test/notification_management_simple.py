#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
通知管理页面 - 简化版
"""

import streamlit as st
import json
import requests
from datetime import datetime


def render():
    """渲染通知管理页面"""
    st.title("📢 通知管理")
    st.markdown("配置测试结果通知，支持钉钉、企业微信等多种通知方式")
    
    # 主要功能选项卡
    tab1, tab2, tab3 = st.tabs(["📋 通知配置", "➕ 添加配置", "🧪 测试通知"])
    
    with tab1:
        render_notification_list()
    
    with tab2:
        render_add_notification()
    
    with tab3:
        render_test_notification()


def render_notification_list():
    """渲染通知配置列表"""
    st.markdown("### 📋 通知配置列表")
    
    # 从session state获取配置列表
    if "notification_configs" not in st.session_state:
        st.session_state.notification_configs = []
    
    configs = st.session_state.notification_configs
    
    if not configs:
        st.info("📝 暂无通知配置，请添加第一个通知配置")
        return
    
    # 显示配置列表
    for i, config in enumerate(configs):
        with st.expander(f"📢 {config['name']} ({config['type']})", expanded=False):
            col1, col2 = st.columns([3, 1])
            
            with col1:
                st.markdown(f"**类型**: {config['type']}")
                st.markdown(f"**Webhook URL**: {config['webhook_url'][:50]}...")
                st.markdown(f"**状态**: {'✅ 启用' if config['enabled'] else '❌ 禁用'}")
                st.markdown(f"**创建时间**: {config['created_at']}")
                
                if config['type'] == '钉钉机器人':
                    if config.get('secret'):
                        st.markdown("**签名密钥**: 已配置")
                    if config.get('at_mobiles'):
                        st.markdown(f"**@手机号**: {', '.join(config['at_mobiles'])}")
                    if config.get('at_all'):
                        st.markdown("**@所有人**: 是")
            
            with col2:
                # 操作按钮
                if st.button("🧪 测试", key=f"test_notification_{i}"):
                    test_notification(config)
                
                # 启用/禁用按钮
                if config['enabled']:
                    if st.button("⏸️ 禁用", key=f"disable_notification_{i}"):
                        st.session_state.notification_configs[i]['enabled'] = False
                        st.success("✅ 通知配置已禁用")
                        st.rerun()
                else:
                    if st.button("▶️ 启用", key=f"enable_notification_{i}"):
                        st.session_state.notification_configs[i]['enabled'] = True
                        st.success("✅ 通知配置已启用")
                        st.rerun()
                
                if st.button("🗑️ 删除", key=f"delete_notification_{i}"):
                    if st.session_state.get(f"confirm_delete_notification_{i}", False):
                        st.session_state.notification_configs.pop(i)
                        st.success("✅ 通知配置删除成功")
                        st.rerun()
                    else:
                        st.session_state[f"confirm_delete_notification_{i}"] = True
                        st.warning("⚠️ 再次点击确认删除")


def render_add_notification():
    """渲染添加通知配置"""
    st.markdown("### ➕ 添加通知配置")
    
    with st.form("add_notification_form"):
        # 基本信息
        name = st.text_input(
            "配置名称 *",
            placeholder="如: 测试团队钉钉群、开发团队企业微信"
        )
        
        notification_type = st.selectbox(
            "通知类型 *",
            options=["钉钉机器人", "企业微信机器人", "自定义Webhook"]
        )
        
        webhook_url = st.text_input(
            "Webhook URL *",
            placeholder="机器人的Webhook地址"
        )
        
        # 根据通知类型显示不同配置
        if notification_type == "钉钉机器人":
            st.markdown("**钉钉机器人配置**")
            
            secret = st.text_input(
                "签名密钥",
                type="password",
                placeholder="钉钉机器人的加签密钥（可选）"
            )
            
            at_all = st.checkbox("@所有人")
            
            at_mobiles_input = st.text_input(
                "@指定手机号",
                placeholder="多个手机号用逗号分隔，如: 13800138000,13900139000"
            )
            at_mobiles = [mobile.strip() for mobile in at_mobiles_input.split(",") if mobile.strip()]
        
        elif notification_type == "企业微信机器人":
            st.markdown("**企业微信机器人配置**")
            
            mentioned_list_input = st.text_input(
                "@指定用户",
                placeholder="多个用户ID用逗号分隔"
            )
            mentioned_list = [user.strip() for user in mentioned_list_input.split(",") if user.strip()]
            
            mentioned_mobile_list_input = st.text_input(
                "@指定手机号",
                placeholder="多个手机号用逗号分隔"
            )
            mentioned_mobile_list = [mobile.strip() for mobile in mentioned_mobile_list_input.split(",") if mobile.strip()]
        
        # 提交按钮
        submitted = st.form_submit_button("✅ 添加配置", type="primary")
        
        if submitted:
            if name.strip() and webhook_url.strip():
                try:
                    # 创建通知配置
                    config = {
                        "id": f"config_{len(st.session_state.get('notification_configs', []))}",
                        "name": name.strip(),
                        "type": notification_type,
                        "webhook_url": webhook_url.strip(),
                        "enabled": True,
                        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    
                    if notification_type == "钉钉机器人":
                        config.update({
                            "secret": secret.strip() if secret.strip() else None,
                            "at_mobiles": at_mobiles,
                            "at_all": at_all
                        })
                    elif notification_type == "企业微信机器人":
                        config.update({
                            "mentioned_list": mentioned_list,
                            "mentioned_mobile_list": mentioned_mobile_list
                        })
                    
                    # 添加到session state
                    if "notification_configs" not in st.session_state:
                        st.session_state.notification_configs = []
                    
                    st.session_state.notification_configs.append(config)
                    
                    st.success("✅ 通知配置添加成功")
                    st.rerun()
                
                except Exception as e:
                    st.error(f"❌ 添加失败: {e}")
            else:
                st.error("❌ 请填写配置名称和Webhook URL")


def render_test_notification():
    """渲染测试通知"""
    st.markdown("### 🧪 测试通知")
    
    # 从session state获取配置
    configs = st.session_state.get("notification_configs", [])
    enabled_configs = [config for config in configs if config['enabled']]
    
    if not enabled_configs:
        st.info("📝 请先添加并启用通知配置")
        return
    
    # 选择要测试的配置
    config_options = {i: f"{config['name']} ({config['type']})" 
                     for i, config in enumerate(enabled_configs)}
    
    selected_config_indices = st.multiselect(
        "选择要测试的通知配置",
        options=list(config_options.keys()),
        format_func=lambda x: config_options[x],
        default=list(config_options.keys())[:1] if config_options else []
    )
    
    if st.button("🧪 发送测试通知", type="primary"):
        if selected_config_indices:
            for index in selected_config_indices:
                config = enabled_configs[index]
                test_notification(config)
        else:
            st.warning("⚠️ 请选择至少一个通知配置")
    
    # 显示测试消息预览
    st.markdown("**测试消息预览**:")
    test_data = {
        "项目名称": "示例项目",
        "环境名称": "测试环境",
        "总用例数": 10,
        "通过数": 8,
        "失败数": 2,
        "通过率": "80.0%",
        "执行时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    st.json(test_data)


def test_notification(config):
    """测试通知发送"""
    try:
        with st.spinner(f"正在发送测试通知到 {config['name']}..."):
            if config['type'] == "钉钉机器人":
                success = send_dingtalk_notification(config)
            elif config['type'] == "企业微信机器人":
                success = send_wechat_work_notification(config)
            else:
                success = send_webhook_notification(config)
            
            if success:
                st.success(f"✅ 测试通知发送成功: {config['name']}")
            else:
                st.error(f"❌ 测试通知发送失败: {config['name']}")
    
    except Exception as e:
        st.error(f"❌ 发送通知异常: {e}")


def send_dingtalk_notification(config):
    """发送钉钉通知"""
    try:
        message = {
            "msgtype": "markdown",
            "markdown": {
                "title": "🧪 接口测试通知测试",
                "text": f"""
# 🧪 接口自动化测试报告

**项目**: 测试项目
**环境**: 测试环境
**状态**: 测试通知

## 📊 执行统计
- **总用例数**: 10
- **通过数**: 8
- **失败数**: 2
- **通过率**: 80.0%

## ⏱️ 执行信息
- **执行时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

这是一条测试通知，用于验证通知配置是否正常工作。
"""
            }
        }
        
        # 添加@功能
        if config.get('at_all') or config.get('at_mobiles'):
            message["at"] = {
                "isAtAll": config.get('at_all', False),
                "atMobiles": config.get('at_mobiles', [])
            }
        
        response = requests.post(
            config['webhook_url'],
            headers={'Content-Type': 'application/json'},
            json=message,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            return result.get('errcode') == 0
        
        return False
    
    except Exception as e:
        print(f"钉钉通知发送失败: {e}")
        return False


def send_wechat_work_notification(config):
    """发送企业微信通知"""
    try:
        message = {
            "msgtype": "markdown",
            "markdown": {
                "content": f"""# 🧪 接口自动化测试报告

**项目**: 测试项目
**环境**: 测试环境
**状态**: 测试通知
**通过率**: 80.0%

**执行统计**: 总计10个用例，通过8个，失败2个
**执行时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

这是一条测试通知，用于验证通知配置是否正常工作。
"""
            }
        }
        
        # 添加@功能
        if config.get('mentioned_list') or config.get('mentioned_mobile_list'):
            message["markdown"]["mentioned_list"] = config.get('mentioned_list', [])
            message["markdown"]["mentioned_mobile_list"] = config.get('mentioned_mobile_list', [])
        
        response = requests.post(
            config['webhook_url'],
            headers={'Content-Type': 'application/json'},
            json=message,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            return result.get('errcode') == 0
        
        return False
    
    except Exception as e:
        print(f"企业微信通知发送失败: {e}")
        return False


def send_webhook_notification(config):
    """发送Webhook通知"""
    try:
        data = {
            "type": "test_notification",
            "project_name": "测试项目",
            "environment_name": "测试环境",
            "total_cases": 10,
            "passed_cases": 8,
            "failed_cases": 2,
            "pass_rate": 80.0,
            "execution_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "message": "这是一条测试通知"
        }
        
        response = requests.post(
            config['webhook_url'],
            headers={'Content-Type': 'application/json'},
            json=data,
            timeout=10
        )
        
        return response.status_code == 200
    
    except Exception as e:
        print(f"Webhook通知发送失败: {e}")
        return False


if __name__ == "__main__":
    render()
