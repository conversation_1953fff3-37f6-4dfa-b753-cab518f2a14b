#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/21
# <AUTHOR> Kai

"""
Mock管理页面
"""

import streamlit as st
import pandas as pd
from src.mock.dao import mock_dao
import json

def render():
    """渲染Mock管理页面"""
    st.title("🎭 Mock配置管理")
    st.markdown("---")

    # 获取所有测试案例
    test_cases = mock_dao.list_test_cases()

    # 如果没有配置，显示添加配置的引导
    if not test_cases:
        st.info("👋 欢迎使用Mock配置管理！请先添加您的第一个Mock配置。")

        # 添加配置表单
        with st.expander("➕ 添加Mock配置", expanded=True):
            with st.form("add_config_form"):
                col1, col2 = st.columns(2)

                with col1:
                    test_case = st.text_input("测试案例 *", placeholder="例如: payment-test", help="用于分组管理的测试案例名称")
                    api_url = st.text_input("API路径 *", placeholder="例如: /api/payment", help="Mock接口的路径")
                    condition = st.text_input("匹配条件", placeholder="例如: amount=100", help="可选：请求匹配条件")

                with col2:
                    description = st.text_area("描述", placeholder="配置描述...", help="可选：配置的详细描述")
                    mock_body = st.text_area("Mock响应体", placeholder='{"code": 200, "message": "success"}', help="Mock接口返回的JSON响应")

                col_btn1, col_btn2, col_btn3 = st.columns([1, 1, 2])
                with col_btn1:
                    if st.form_submit_button("✅ 添加配置", type="primary"):
                        if test_case and api_url:
                            success = mock_dao.insert_mock_config(
                                test_case=test_case,
                                api_url=api_url,
                                description=description,
                                mock_body=mock_body,
                                condition=condition
                            )
                            if success:
                                st.success("✅ 配置添加成功")
                                st.rerun()
                            else:
                                st.error("❌ 配置添加失败")
                        else:
                            st.error("请填写必填字段（测试案例和API路径）")

                with col_btn2:
                    if st.form_submit_button("📋 示例配置"):
                        st.session_state.show_example = True

        # 显示示例配置
        if st.session_state.get("show_example", False):
            st.subheader("📋 配置示例")

            example_configs = [
                {
                    "name": "支付接口示例",
                    "test_case": "payment-api",
                    "api_url": "/api/payment",
                    "description": "模拟支付接口响应",
                    "mock_body": '{"code": 200, "message": "支付成功", "data": {"orderId": "12345", "amount": 100.00}}',
                    "condition": "amount>0"
                },
                {
                    "name": "用户信息示例",
                    "test_case": "user-api",
                    "api_url": "/api/user/info",
                    "description": "模拟用户信息查询",
                    "mock_body": '{"code": 200, "data": {"userId": 1001, "name": "张三", "email": "<EMAIL>"}}',
                    "condition": ""
                }
            ]

            for i, example in enumerate(example_configs):
                with st.expander(f"示例 {i+1}: {example['name']}", expanded=False):
                    st.code(f"""
测试案例: {example['test_case']}
API路径: {example['api_url']}
描述: {example['description']}
匹配条件: {example['condition'] or '无'}
响应体:
{example['mock_body']}
                    """)

                    if st.button(f"使用示例 {i+1}", key=f"use_example_{i}"):
                        # 添加示例配置
                        success = mock_dao.insert_mock_config(
                            test_case=example['test_case'],
                            api_url=example['api_url'],
                            description=example['description'],
                            mock_body=example['mock_body'],
                            condition=example['condition']
                        )
                        if success:
                            st.success(f"✅ 示例配置 '{example['name']}' 添加成功")
                            st.rerun()
                        else:
                            st.error("❌ 示例配置添加失败")

        return  # 如果没有配置，直接返回
    
    # 主内容区域 - 有配置时显示
    # 添加快速操作按钮
    col_btn1, col_btn2, col_btn3, col_btn4 = st.columns(4)

    with col_btn1:
        if st.button("➕ 快速添加", type="primary", use_container_width=True):
            st.session_state.show_add_form = True

    with col_btn2:
        if st.button("📋 批量导入", use_container_width=True):
            st.session_state.show_import_form = True

    with col_btn3:
        if st.button("📤 导出配置", use_container_width=True):
            st.session_state.show_export_form = True

    with col_btn4:
        if st.button("🔄 刷新列表", use_container_width=True):
            st.rerun()

    st.markdown("---")

    # 快速添加表单
    if st.session_state.get("show_add_form", False):
        with st.expander("➕ 快速添加Mock配置", expanded=True):
            with st.form("quick_add_form"):
                col1, col2 = st.columns(2)

                with col1:
                    test_case = st.text_input("测试案例 *", placeholder="例如: payment-test")
                    api_url = st.text_input("API路径 *", placeholder="例如: /api/payment")

                with col2:
                    description = st.text_input("描述", placeholder="配置描述...")
                    condition = st.text_input("匹配条件", placeholder="例如: amount=100")

                mock_body = st.text_area("Mock响应体", placeholder='{"code": 200, "message": "success"}', height=100)

                col_form1, col_form2 = st.columns(2)
                with col_form1:
                    if st.form_submit_button("✅ 添加配置", type="primary"):
                        if test_case and api_url:
                            success = mock_dao.insert_mock_config(
                                test_case=test_case,
                                api_url=api_url,
                                description=description,
                                mock_body=mock_body,
                                condition=condition
                            )
                            if success:
                                st.success("✅ 配置添加成功")
                                st.session_state.show_add_form = False
                                st.rerun()
                            else:
                                st.error("❌ 配置添加失败")
                        else:
                            st.error("请填写必填字段")

                with col_form2:
                    if st.form_submit_button("❌ 取消"):
                        st.session_state.show_add_form = False
                        st.rerun()

    # 主内容区域
    col1, col2 = st.columns([2, 1])

    with col1:
        st.subheader("📋 Mock配置列表")
        
        # 选择测试案例
        selected_test_case = st.selectbox(
            "选择测试案例",
            options=["全部"] + test_cases,
            index=0
        )
        
        # 获取配置列表
        if selected_test_case == "全部":
            all_configs = []
            for tc in test_cases:
                configs = mock_dao.get_mock_configs_by_test_case(tc)
                all_configs.extend(configs)
            configs = all_configs
        else:
            configs = mock_dao.get_mock_configs_by_test_case(selected_test_case)
        
        if configs:
            # 转换为DataFrame
            df_data = []
            for config in configs:
                df_data.append({
                    "ID": config["id"],
                    "测试案例": config["test_case"],
                    "API路径": config["api_url"],
                    "描述": config["description"] or "",
                    "条件": config["condition"] or "",
                    "创建时间": config["create_time"]
                })
            
            df = pd.DataFrame(df_data)
            
            # 显示表格
            st.dataframe(
                df,
                use_container_width=True,
                hide_index=True,
                column_config={
                    "ID": st.column_config.NumberColumn("ID", width="small"),
                    "测试案例": st.column_config.TextColumn("测试案例", width="medium"),
                    "API路径": st.column_config.TextColumn("API路径", width="medium"),
                    "描述": st.column_config.TextColumn("描述", width="large"),
                    "条件": st.column_config.TextColumn("条件", width="medium"),
                    "创建时间": st.column_config.TextColumn("创建时间", width="medium")
                }
            )
            
            # 配置详情
            st.subheader("🔍 配置详情")
            config_id = st.selectbox(
                "选择配置查看详情",
                options=[f"{c['id']} - {c['api_url']}" for c in configs],
                format_func=lambda x: x.split(" - ")[1] if " - " in x else x
            )
            
            if config_id:
                selected_id = int(config_id.split(" - ")[0])
                selected_config = next((c for c in configs if c["id"] == selected_id), None)
                
                if selected_config:
                    col_detail1, col_detail2 = st.columns(2)
                    
                    with col_detail1:
                        st.markdown(f"**测试案例**: {selected_config['test_case']}")
                        st.markdown(f"**API路径**: {selected_config['api_url']}")
                        st.markdown(f"**描述**: {selected_config['description'] or '无'}")
                        st.markdown(f"**条件**: {selected_config['condition'] or '无'}")
                    
                    with col_detail2:
                        st.markdown(f"**创建时间**: {selected_config['create_time']}")
                        st.markdown(f"**更新时间**: {selected_config['update_time']}")
                    
                    # Mock响应体
                    st.markdown("**Mock响应体**:")
                    if selected_config['mock_body']:
                        try:
                            # 尝试格式化JSON
                            parsed_body = json.loads(selected_config['mock_body'])
                            formatted_json = json.dumps(parsed_body, ensure_ascii=False, indent=2)
                            st.code(formatted_json, language="json")
                        except json.JSONDecodeError:
                            st.text_area("响应体", selected_config['mock_body'], height=100, disabled=True)
                    else:
                        st.info("无响应体配置")
                    
                    # 操作按钮
                    col_btn1, col_btn2, col_btn3 = st.columns(3)
                    
                    with col_btn1:
                        if st.button("✏️ 编辑", key=f"edit_{selected_id}"):
                            st.session_state.edit_config_id = selected_id
                            st.session_state.show_edit_form = True
                    
                    with col_btn2:
                        if st.button("🧪 测试", key=f"test_{selected_id}"):
                            st.session_state.test_config_id = selected_id
                            st.session_state.show_test_form = True
                    
                    with col_btn3:
                        if st.button("🗑️ 删除", key=f"delete_{selected_id}", type="secondary"):
                            if st.session_state.get(f"confirm_delete_{selected_id}", False):
                                success = mock_dao.delete_mock_config(selected_id)
                                if success:
                                    st.success("✅ 配置删除成功")
                                    st.rerun()
                                else:
                                    st.error("❌ 配置删除失败")
                            else:
                                st.session_state[f"confirm_delete_{selected_id}"] = True
                                st.warning("再次点击确认删除")
        else:
            st.info("该测试案例暂无配置")
    
    with col2:
        st.subheader("📊 统计信息")
        
        # 统计卡片
        total_configs = len(configs) if configs else 0
        total_test_cases = len(test_cases)
        
        st.metric("配置总数", total_configs)
        st.metric("测试案例数", total_test_cases)
        
        # 最近活动
        st.subheader("🕒 最近活动")
        if configs:
            recent_configs = sorted(configs, key=lambda x: x["update_time"], reverse=True)[:5]
            for config in recent_configs:
                st.markdown(f"- **{config['test_case']}** - {config['api_url']}")
                st.caption(f"更新时间: {config['update_time']}")
        else:
            st.info("暂无活动记录")
    
    # 编辑表单
    if st.session_state.get("show_edit_form", False):
        edit_config_form()
    
    # 测试表单
    if st.session_state.get("show_test_form", False):
        test_config_form()


def edit_config_form():
    """编辑配置表单"""
    config_id = st.session_state.get("edit_config_id")
    if not config_id:
        return
    
    # 获取配置详情
    # 这里需要实现获取单个配置的方法
    st.subheader("✏️ 编辑配置")
    
    with st.form("edit_config_form"):
        description = st.text_area("描述")
        mock_body = st.text_area("Mock响应体", height=200)
        condition = st.text_input("匹配条件")
        
        col1, col2 = st.columns(2)
        with col1:
            if st.form_submit_button("保存", type="primary"):
                success = mock_dao.update_mock_config(
                    config_id=config_id,
                    description=description,
                    mock_body=mock_body,
                    condition=condition
                )
                if success:
                    st.success("✅ 配置更新成功")
                    st.session_state.show_edit_form = False
                    st.rerun()
                else:
                    st.error("❌ 配置更新失败")
        
        with col2:
            if st.form_submit_button("取消"):
                st.session_state.show_edit_form = False
                st.rerun()


def test_config_form():
    """测试配置表单"""
    config_id = st.session_state.get("test_config_id")
    if not config_id:
        return

    st.subheader("🧪 测试配置")

    with st.form("test_config_form"):
        request_data = st.text_area("请求数据", placeholder='{"amount": 100}', height=100)

        col1, col2 = st.columns(2)
        with col1:
            if st.form_submit_button("发送测试", type="primary"):
                st.info("🧪 Mock测试功能请使用专门的测试页面")

        with col2:
            if st.form_submit_button("关闭"):
                st.session_state.show_test_form = False
                st.rerun()


if __name__ == "__main__":
    render()
