#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/21
# <AUTHOR> Kai

"""
Mock日志页面
"""

import streamlit as st
import pandas as pd
from src.mock.dao import mock_dao
import json
from datetime import datetime, <PERSON><PERSON><PERSON>

def render():
    """渲染Mock日志页面"""
    st.title("📊 Mock日志")

    # 左右布局
    col_left, col_right = st.columns([2, 1])

    # 左侧：日志列表
    with col_left:
        # 过滤选项
        col1, col2, col3 = st.columns(3)
        with col1:
            # 获取所有API路径用于筛选
            all_logs = mock_dao.get_mock_logs(limit=1000)
            api_paths = ["全部"] + list(set([log["api_url"] for log in all_logs]))
            api_filter = st.selectbox("接口名称", api_paths)
        with col2:
            status_filter = st.selectbox("状态码", ["全部", "200", "400", "404", "500"])
        with col3:
            limit = st.selectbox("显示数量", [50, 100, 200, 500], index=1)

        # 获取日志数据
        logs = mock_dao.get_mock_logs(limit=limit)

        # 应用接口名称过滤
        if api_filter != "全部":
            logs = [log for log in logs if log["api_url"] == api_filter]

        # 应用状态码过滤
        if status_filter != "全部":
            logs = [log for log in logs if str(log["status_code"]) == status_filter]

        if logs:
            # 转换为DataFrame
            df_data = []
            for log in logs:
                df_data.append({
                    "ID": log["id"],
                    "时间": log["request_time"][:16] if log["request_time"] else "-",
                    "API": log["api_url"],
                    "方法": log.get("method", "POST"),
                    "状态码": log["status_code"]
                })

            df = pd.DataFrame(df_data)

            # 显示日志表格
            selected_rows = st.dataframe(
                df,
                use_container_width=True,
                hide_index=True,
                on_select="rerun",
                selection_mode="single-row",
                column_config={
                    "ID": st.column_config.NumberColumn("ID", width="small"),
                    "时间": st.column_config.TextColumn("时间", width="medium"),
                    "API": st.column_config.TextColumn("API", width="large"),
                    "方法": st.column_config.TextColumn("方法", width="small"),
                    "状态码": st.column_config.NumberColumn("状态码", width="small")
                }
            )
        else:
            st.info("暂无日志记录")

    # 右侧：日志详情
    with col_right:

        if logs and 'selected_rows' in locals() and selected_rows.selection.rows:
            selected_log = logs[selected_rows.selection.rows[0]]

            # 基本信息
            st.text(f"ID: {selected_log['id']}")
            st.text(f"时间: {selected_log['request_time']}")
            st.text(f"API: {selected_log['api_url']}")
            st.text(f"方法: {selected_log.get('method', 'POST')}")

            # 状态码
            status_code = selected_log['status_code']
            if status_code == 200:
                st.success(f"状态码: {status_code}")
            else:
                st.error(f"状态码: {status_code}")

            # 请求数据
            if selected_log['request_data']:
                st.markdown("**请求数据**")
                try:
                    request_json = json.loads(selected_log['request_data'])
                    st.json(request_json)
                except:
                    st.code(selected_log['request_data'])

            # 响应数据
            if selected_log['response_data']:
                st.markdown("**响应数据**")
                try:
                    response_json = json.loads(selected_log['response_data'])
                    st.json(response_json)
                except:
                    st.code(selected_log['response_data'])
        else:
            pass  # 不显示任何提示


if __name__ == "__main__":
    render()
