#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/21
# <AUTHOR> Kai


"""
Mock工作台 - 重新设计的Mock功能
"""

import streamlit as st
import pandas as pd
import json
import requests
from src.mock.dao import mock_dao
from datetime import datetime

def render():
    """渲染Mock工作台"""
    st.title("🎭 Mock工作台")
    
    # 初始化session state
    if "current_project" not in st.session_state:
        st.session_state.current_project = "默认项目"
    if "mock_configs" not in st.session_state:
        st.session_state.mock_configs = load_mock_configs()
    
    # 顶部工具栏
    render_toolbar()
    
    # 主要内容区域
    col_left, col_right = st.columns([1, 1])
    
    with col_left:
        render_api_list()
    
    with col_right:
        render_test_panel()


def render_toolbar():
    """渲染顶部工具栏"""
    col1, col2, col3, col4, col5 = st.columns([2, 1, 1, 1, 1])
    
    with col1:
        # 项目选择
        projects = get_projects()
        current_project = st.selectbox(
            "项目", 
            projects, 
            index=projects.index(st.session_state.current_project) if st.session_state.current_project in projects else 0,
            key="project_selector"
        )
        if current_project != st.session_state.current_project:
            st.session_state.current_project = current_project
            st.session_state.mock_configs = load_mock_configs()
            st.rerun()
    
    with col2:
        if st.button("➕ 新建API", use_container_width=True):
            st.session_state.show_create_form = True
    
    with col3:
        if st.button("📁 新建项目", use_container_width=True):
            st.session_state.show_project_form = True
    
    with col4:
        if st.button("📤 导出", use_container_width=True):
            export_project()
    
    with col5:
        if st.button("📥 导入", use_container_width=True):
            st.session_state.show_import_form = True
    
    # 弹出表单
    if st.session_state.get("show_create_form", False):
        show_create_api_form()
    
    if st.session_state.get("show_project_form", False):
        show_create_project_form()
    
    if st.session_state.get("show_import_form", False):
        show_import_form()


def render_api_list():
    """渲染API列表"""
    st.markdown("### 📋 API列表")
    
    configs = st.session_state.mock_configs
    
    if not configs:
        st.info("暂无API配置，点击'新建API'开始创建")
        return
    
    # 搜索和过滤
    col_search, col_filter = st.columns([2, 1])
    with col_search:
        search_term = st.text_input("🔍 搜索API", placeholder="输入API路径或描述")
    with col_filter:
        status_filter = st.selectbox("状态", ["全部", "启用", "禁用"])
    
    # 过滤配置
    filtered_configs = configs
    if search_term:
        filtered_configs = [c for c in filtered_configs 
                          if search_term.lower() in c['api_url'].lower() 
                          or search_term.lower() in (c['description'] or '').lower()]
    
    # API卡片列表
    for config in filtered_configs:
        render_api_card(config)


def render_api_card(config):
    """渲染API卡片"""
    with st.container():
        # 卡片样式
        st.markdown(f"""
        <div style="
            border: 1px solid #e0e0e0; 
            border-radius: 8px; 
            padding: 16px; 
            margin: 8px 0;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        ">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h4 style="margin: 0; color: #333;">
                        <span style="background: #28a745; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px; margin-right: 8px;">
                            POST
                        </span>
                        {config['api_url']}
                    </h4>
                    <p style="margin: 4px 0; color: #666; font-size: 14px;">
                        {config['description'] or '无描述'}
                    </p>
                    <small style="color: #999;">
                        更新时间: {config['update_time'][:16] if config['update_time'] else '未知'}
                    </small>
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)
        
        # 操作按钮
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            if st.button("🧪 测试", key=f"test_{config['id']}", use_container_width=True):
                st.session_state.selected_api = config
                st.session_state.test_mode = "selected"
        with col2:
            if st.button("✏️ 编辑", key=f"edit_{config['id']}", use_container_width=True):
                st.session_state.edit_config = config
                st.session_state.show_edit_form = True
        with col3:
            if st.button("📋 复制", key=f"copy_{config['id']}", use_container_width=True):
                copy_api_config(config)
        with col4:
            if st.button("🗑️ 删除", key=f"delete_{config['id']}", use_container_width=True):
                delete_api_config(config['id'])


def render_test_panel():
    """渲染测试面板"""
    st.markdown("### 🧪 API测试")
    
    # 测试模式选择
    test_mode = st.radio("测试模式", ["快速测试", "自定义请求"], horizontal=True)
    
    if test_mode == "快速测试":
        render_quick_test()
    else:
        render_custom_test()


def render_quick_test():
    """渲染快速测试"""
    if st.session_state.get("selected_api"):
        config = st.session_state.selected_api
        
        st.markdown(f"**测试接口**: `{config['api_url']}`")
        st.markdown(f"**描述**: {config['description'] or '无'}")
        
        # 构建请求URL
        base_url = "http://localhost:8001"
        full_url = f"{base_url}/mock/{st.session_state.current_project}{config['api_url']}"
        
        st.code(full_url)
        
        # 请求参数
        with st.expander("请求参数", expanded=True):
            request_body = st.text_area(
                "请求体 (JSON)", 
                value='{\n  "test": true\n}',
                height=100
            )
        
        # 预期响应
        if config['mock_body']:
            with st.expander("预期响应", expanded=False):
                try:
                    parsed_body = json.loads(config['mock_body'])
                    st.json(parsed_body)
                except:
                    st.code(config['mock_body'])
        
        # 发送测试
        if st.button("🚀 发送测试请求", type="primary", use_container_width=True):
            send_test_request(full_url, request_body)
    else:
        st.info("请在左侧选择一个API进行测试")


def render_custom_test():
    """渲染自定义测试"""
    st.markdown("**自定义HTTP请求**")
    
    # 请求配置
    col_method, col_url = st.columns([1, 3])
    with col_method:
        method = st.selectbox("方法", ["GET", "POST", "PUT", "DELETE"])
    with col_url:
        custom_url = st.text_input("URL", placeholder="https://api.example.com/test")
    
    # 请求头
    headers = st.text_area(
        "请求头 (JSON)",
        value='{\n  "Content-Type": "application/json"\n}',
        height=80
    )
    
    # 请求体
    if method in ["POST", "PUT"]:
        body = st.text_area("请求体 (JSON)", height=100)
    else:
        body = ""
    
    # 发送请求
    if st.button("🚀 发送请求", type="primary", use_container_width=True):
        if custom_url:
            send_custom_request(method, custom_url, headers, body)
        else:
            st.error("请输入URL")


def show_create_api_form():
    """显示创建API表单"""
    with st.expander("➕ 新建API", expanded=True):
        with st.form("create_api_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                api_path = st.text_input("API路径 *", placeholder="/api/users")
                method = st.selectbox("HTTP方法", ["GET", "POST", "PUT", "DELETE"])
            
            with col2:
                name = st.text_input("API名称", placeholder="用户列表接口")
                status_code = st.selectbox("状态码", [200, 201, 400, 401, 403, 404, 500])
            
            description = st.text_area("描述", placeholder="接口功能描述")
            
            # 响应模板选择
            template = st.selectbox("响应模板", [
                "自定义",
                "成功响应 (data)",
                "成功响应 (list)", 
                "错误响应",
                "分页响应"
            ])
            
            if template == "自定义":
                response_body = st.text_area("响应体 (JSON)", height=150)
            else:
                response_body = get_response_template(template)
                st.code(response_body, language="json")
            
            col_btn1, col_btn2 = st.columns(2)
            with col_btn1:
                if st.form_submit_button("✅ 创建", type="primary"):
                    create_api_config(api_path, method, name, description, response_body, status_code)
            with col_btn2:
                if st.form_submit_button("❌ 取消"):
                    st.session_state.show_create_form = False
                    st.rerun()


def get_response_template(template_type):
    """获取响应模板"""
    templates = {
        "成功响应 (data)": '{\n  "code": 200,\n  "message": "success",\n  "data": {\n    "id": 1,\n    "name": "示例数据"\n  }\n}',
        "成功响应 (list)": '{\n  "code": 200,\n  "message": "success",\n  "data": [\n    {"id": 1, "name": "项目1"},\n    {"id": 2, "name": "项目2"}\n  ]\n}',
        "错误响应": '{\n  "code": 400,\n  "message": "参数错误",\n  "error": "缺少必要参数"\n}',
        "分页响应": '{\n  "code": 200,\n  "message": "success",\n  "data": {\n    "list": [],\n    "total": 0,\n    "page": 1,\n    "size": 10\n  }\n}'
    }
    return templates.get(template_type, "")


def load_mock_configs():
    """加载Mock配置"""
    # 这里应该根据项目加载配置
    test_cases = mock_dao.list_test_cases()
    all_configs = []
    for tc in test_cases:
        if tc == st.session_state.current_project:
            configs = mock_dao.get_mock_configs_by_test_case(tc)
            all_configs.extend(configs)
    return all_configs


def get_projects():
    """获取项目列表"""
    test_cases = mock_dao.list_test_cases()
    if not test_cases:
        return ["默认项目"]
    return test_cases


def create_api_config(api_path, method, name, description, response_body, status_code):
    """创建API配置"""
    success = mock_dao.insert_mock_config(
        test_case=st.session_state.current_project,
        api_url=api_path,
        description=f"{name} - {description}",
        mock_body=response_body,
        condition=f"method={method}"
    )
    
    if success:
        st.success("✅ API创建成功")
        st.session_state.show_create_form = False
        st.session_state.mock_configs = load_mock_configs()
        st.rerun()
    else:
        st.error("❌ API创建失败")


def send_test_request(url, body):
    """发送测试请求"""
    try:
        headers = {"Content-Type": "application/json"}
        
        if body.strip():
            data = json.loads(body)
        else:
            data = {}
        
        with st.spinner("发送请求中..."):
            response = requests.post(url, json=data, headers=headers, timeout=10)
        
        # 显示响应结果
        st.markdown("### 📥 响应结果")
        
        col1, col2, col3 = st.columns(3)
        with col1:
            if response.status_code == 200:
                st.success(f"状态码: {response.status_code}")
            else:
                st.error(f"状态码: {response.status_code}")
        with col2:
            st.metric("响应时间", f"{response.elapsed.total_seconds():.3f}s")
        with col3:
            st.metric("响应大小", f"{len(response.content)} bytes")
        
        # 响应体
        try:
            response_json = response.json()
            st.json(response_json)
        except:
            st.code(response.text)
            
    except Exception as e:
        st.error(f"请求失败: {str(e)}")


def send_custom_request(method, url, headers_text, body_text):
    """发送自定义请求"""
    # 实现自定义请求逻辑
    pass


def copy_api_config(config):
    """复制API配置"""
    st.success("API配置已复制到剪贴板")


def delete_api_config(config_id):
    """删除API配置"""
    if mock_dao.delete_mock_config(config_id):
        st.success("✅ API删除成功")
        st.session_state.mock_configs = load_mock_configs()
        st.rerun()
    else:
        st.error("❌ API删除失败")


def show_create_project_form():
    """显示创建项目表单"""
    pass


def show_import_form():
    """显示导入表单"""
    pass


def export_project():
    """导出项目"""
    pass


if __name__ == "__main__":
    render()
