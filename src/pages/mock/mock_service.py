#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/21
# <AUTHOR> Kai


"""
Mock服务管理页面
"""

import streamlit as st
import subprocess
import requests
import time
import json
import os
from pathlib import Path

# 配置文件路径
CONFIG_FILE = "data/mock_service_config.json"


def load_service_config():
    """加载服务配置"""
    default_config = {
        "host": "0.0.0.0",
        "port": 8001,
        "log_level": "info",
        "auto_reload": True
    }

    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 合并默认配置，确保所有字段都存在
                default_config.update(config)
        return default_config
    except Exception as e:
        st.error(f"加载配置失败: {e}")
        return default_config


def save_service_config_to_file(host, port, log_level, auto_reload):
    """保存服务配置到文件"""
    config = {
        "host": host,
        "port": int(port),
        "log_level": log_level,
        "auto_reload": auto_reload
    }

    try:
        # 确保data目录存在
        os.makedirs("data", exist_ok=True)

        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        st.error(f"保存配置失败: {e}")
        return False


def render():
    """渲染Mock服务管理页面"""
    st.title("🔧 Mock服务")

    # 左右布局
    col_left, col_right = st.columns([2, 1])

    # 左侧：服务控制和配置
    with col_left:
        # 服务状态显示
        st.markdown("### 🔧 Mock服务状态")

        # 检查服务状态
        try:
            response = requests.get("http://localhost:8001/health", timeout=2)
            if response.status_code == 200:
                st.success("🟢 Mock服务运行中")
                service_running = True
            else:
                st.error("🔴 Mock服务异常")
                service_running = False
        except:
            st.error("🔴 Mock服务已停止")
            service_running = False

        # 服务控制按钮
        st.markdown("### 🎛️ 服务控制")
        col_btn1, col_btn2, col_btn3 = st.columns(3)

        with col_btn1:
            if service_running:
                if st.button("🛑 停止服务", use_container_width=True):
                    stop_service()
                    st.rerun()
            else:
                if st.button("🚀 启动服务", type="primary", use_container_width=True):
                    start_service()
                    st.rerun()

        with col_btn2:
            if st.button("🔄 重启服务", use_container_width=True):
                restart_service()
                st.rerun()

        with col_btn3:
            if st.button("📊 刷新状态", use_container_width=True):
                st.rerun()

        # 服务配置
        st.markdown("### ⚙️ 服务配置")

        # 加载当前配置
        current_config = load_service_config()

        with st.form("service_config"):
            col_cfg1, col_cfg2 = st.columns(2)
            with col_cfg1:
                host = st.text_input("服务地址", value=current_config["host"],
                                     help="0.0.0.0 允许外部访问，127.0.0.1 仅本地访问")
                port = st.number_input("服务端口", value=current_config["port"],
                                       min_value=1000, max_value=65535)
            with col_cfg2:
                log_level = st.selectbox("日志级别", ["info", "debug", "warning", "error"],
                                         index=["info", "debug", "warning", "error"].index(current_config["log_level"]))
                auto_reload = st.checkbox("自动重载", value=current_config["auto_reload"])

            if st.form_submit_button("💾 保存配置"):
                save_service_config(host, port, log_level, auto_reload)

        # 在form外部处理重启按钮
        if st.session_state.get('need_restart_service', False):
            if st.button("🔄 立即重启服务应用配置", type="primary", use_container_width=True):
                restart_service()
                st.session_state.need_restart_service = False
                st.rerun()

    # 右侧：服务信息
    with col_right:
        st.markdown("**服务信息**")

        # 服务地址
        st.markdown("**服务地址**")
        st.code("http://**************:8001")
        st.code("http://**************:8001/docs")

        # 快速测试
        st.markdown("**快速测试**")
        if st.button("🧪 测试连接", use_container_width=True):
            test_service_connection()


def start_service():
    """启动服务"""
    try:
        # 加载配置
        config = load_service_config()
        host = config["host"]
        port = config["port"]

        # 启动服务
        with st.spinner("正在启动Mock服务..."):
            # 使用subprocess启动服务
            script_path = Path("mock_server.py")
            if script_path.exists():
                # 使用虚拟环境的Python
                python_path = Path("venvs/bin/python")
                if not python_path.exists():
                    python_path = "python"  # 回退到系统Python

                # 使用配置文件中的参数启动服务
                subprocess.Popen([
                    str(python_path), str(script_path),
                    "--host", host,
                    "--port", str(port)
                ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                # 等待服务启动
                time.sleep(3)

                # 检查是否启动成功
                try:
                    # 使用配置的地址检查服务状态
                    check_url = f"http://localhost:{port}/health"
                    response = requests.get(check_url, timeout=5)
                    if response.status_code == 200:
                        st.success(f"✅ Mock服务启动成功 ({host}:{port})")
                    else:
                        st.error("❌ 服务启动失败")
                except:
                    st.error("❌ 服务启动失败，无法连接")
            else:
                st.error("❌ 找不到mock_server.py文件")

    except Exception as e:
        st.error(f"❌ 启动服务失败: {e}")


def stop_service():
    """停止服务"""
    try:
        with st.spinner("正在停止Mock服务..."):
            # 使用pkill命令停止Mock服务
            import subprocess
            result = subprocess.run(["pkill", "-f", "mock_server.py"], capture_output=True)

            # 等待进程停止
            time.sleep(2)

            # 检查是否成功停止
            try:
                response = requests.get("http://localhost:8001/health", timeout=1)
                st.warning("⚠️ 服务可能仍在运行")
            except:
                st.success("✅ Mock服务已停止")

    except Exception as e:
        st.error(f"❌ 停止服务失败: {e}")


def restart_service():
    """重启服务"""
    with st.spinner("正在重启Mock服务..."):
        stop_service()
        time.sleep(2)
        start_service()


def save_service_config(host, port, log_level, auto_reload):
    """保存服务配置"""
    try:
        # 保存配置到文件
        if save_service_config_to_file(host, port, log_level, auto_reload):
            st.success("✅ 配置保存成功")
            st.info("💡 重启服务后配置生效")

            # 设置重启标志，在form外部处理
            st.session_state.need_restart_service = True
        else:
            st.error("❌ 配置保存失败")
    except Exception as e:
        st.error(f"❌ 保存配置失败: {e}")


def test_service_connection():
    """测试服务连接"""
    try:
        # 使用配置的端口进行连接测试
        config = load_service_config()
        port = config["port"]
        test_url = f"http://localhost:{port}/health"

        response = requests.get(test_url, timeout=3)
        if response.status_code == 200:
            st.success(f"✅ 服务连接正常 (端口: {port})")
        else:
            st.error(f"❌ 服务响应异常: {response.status_code}")
    except:
        config = load_service_config()
        port = config["port"]
        st.error(f"❌ 无法连接到服务 (端口: {port})")


if __name__ == "__main__":
    render()
