#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/21
# <AUTHOR> Kai

import streamlit as st
import requests
import json
from datetime import datetime
from typing import Dict, List, Any
from src.database.mock_backup import mock_backup_db


def render():
    """渲染Mock编辑页面"""
    st.header("🎭 Mock配置编辑")

    # 检查是否在编辑模式
    if st.session_state.get('edit_mode', False):
        render_edit_form()
    else:
        render_main_page()


def render_main_page():
    """渲染主页面"""
    # 搜索区域
    col1, col2 = st.columns([4, 1])

    with col1:
        search_channel = st.text_input(
            "渠道名称查询",
            placeholder="输入渠道名称进行搜索，留空查询所有",
            key="search_channel"
        )

    with col2:
        st.markdown("<br>", unsafe_allow_html=True)  # 对齐按钮
        if st.button("🔍 查询", type="primary", use_container_width=True):
            query_mock_configs(search_channel)

    # 默认加载所有配置
    if 'mock_configs' not in st.session_state:
        query_mock_configs("")

    # 显示查询结果表格
    if 'mock_configs' in st.session_state:
        render_config_table()


def query_mock_configs(test_case=""):
    """查询Mock配置"""
    try:
        with st.spinner("正在查询Mock配置..."):
            url = "http://web.epaydev.xyz/capi/netforward/mock/queryMockConfig"
            headers = {
                "Content-Type": "application/json"
            }
            data = {
                "testCase": test_case
            }

            response = requests.post(url, headers=headers, json=data, timeout=10)

            if response.status_code == 200:
                result = response.json()

                if result.get("code") == 1:
                    mock_list = result.get("data", {}).get("mockResDTOList", [])

                    # 如果有搜索条件，进行客户端过滤确保结果准确
                    if test_case and test_case.strip():
                        filtered_list = []
                        for config in mock_list:
                            config_test_case = config.get("testCase", "")
                            # 检查是否匹配搜索条件
                            if test_case.lower() in config_test_case.lower():
                                # 检查是否有配置数量，没有配置的渠道不显示
                                mock_config_list = config.get("mockConfigList", [])
                                config_count = len(mock_config_list) if mock_config_list is not None else 0
                                if config_count > 0:
                                    filtered_list.append(config)
                        st.session_state.mock_configs = filtered_list
                    else:
                        st.session_state.mock_configs = mock_list
                    # 移除成功提示，直接显示结果
                else:
                    st.error(f"查询失败: {result.get('message', '未知错误')}")
                    st.session_state.mock_configs = []
            else:
                st.error(f"请求失败，状态码: {response.status_code}")
                st.session_state.mock_configs = []

    except requests.exceptions.Timeout:
        st.error("请求超时，请检查网络连接")
        st.session_state.mock_configs = []
    except requests.exceptions.ConnectionError:
        st.error("连接失败，请检查网络或服务器状态")
        st.session_state.mock_configs = []
    except Exception as e:
        st.error(f"查询出错: {str(e)}")
        st.session_state.mock_configs = []


def render_config_table():
    """渲染配置表格"""
    configs = st.session_state.mock_configs

    if not configs:
        st.info("暂无配置数据")
        return

    st.markdown("### 📊 Mock配置列表")

    # 准备表格数据
    table_data = []
    for i, config in enumerate(configs):
        # 安全获取mockConfigList，处理None情况
        mock_config_list = config.get("mockConfigList", [])
        config_count = len(mock_config_list) if mock_config_list is not None else 0

        table_data.append({
            "序号": i + 1,
            "渠道名称": config.get("testCase", ""),
            "配置数量": config_count,
            "编辑": False
        })

    # 显示表格
    edited_df = st.data_editor(
        table_data,
        use_container_width=True,
        hide_index=True,
        column_config={
            "序号": st.column_config.NumberColumn("序号", width="small"),
            "渠道名称": st.column_config.TextColumn("渠道名称", width="large"),
            "配置数量": st.column_config.NumberColumn("配置数量", width="medium"),
            "编辑": st.column_config.CheckboxColumn("编辑", width="small")
        },
        disabled=["序号", "渠道名称", "配置数量"],
        key="config_table"
    )

    # 检查是否有选中的编辑项
    selected_rows = [i for i, row in enumerate(edited_df) if row["编辑"]]

    if selected_rows:
        selected_index = selected_rows[0]
        selected_config = configs[selected_index]

        # 重新获取最新配置数据，避免缓存问题
        test_case = selected_config.get("testCase", "")
        try:
            # 调用接口获取最新配置
            response = requests.get(f"http://web.epaydev.xyz/capi/netforward/mock/getMockConfig?testCase={test_case}", timeout=10)
            if response.status_code == 200:
                latest_config = response.json()
                if latest_config.get("success", False):
                    # 使用最新数据
                    st.session_state.edit_config = latest_config.get("data", selected_config)
                else:
                    # 接口返回失败，使用缓存数据
                    st.session_state.edit_config = selected_config
            else:
                # 接口调用失败，使用缓存数据
                st.session_state.edit_config = selected_config
        except:
            # 网络错误，使用缓存数据
            st.session_state.edit_config = selected_config

        st.session_state.edit_mode = True
        st.rerun()


def render_edit_form():
    """渲染编辑表单"""
    config = st.session_state.get('edit_config', {})
    test_case = config.get("testCase", "")

    # 首次编辑时自动备份原始配置
    auto_backup_on_first_edit(test_case, config)

    # 顶部导航 - 增加高级编辑功能
    col1, col2, col3, col4, col5, col6 = st.columns([1, 2, 1, 1, 1, 1])
    with col1:
        if st.button("← 返回", use_container_width=True):
            st.session_state.edit_mode = False
            if 'edit_config' in st.session_state:
                del st.session_state.edit_config
            st.rerun()

    with col2:
        # 显示备份状态
        backup_status = "🟢 已备份" if mock_backup_db.backup_exists(test_case) else "🔴 未备份"
        st.markdown(f"**编辑配置: {test_case}** ({backup_status})")

    with col3:
        # 备份按钮
        if st.button("💾 备份", use_container_width=True, help="保存当前配置到本地数据库"):
            backup_current_config(test_case, config)

    with col4:
        # 重置按钮
        if st.button("🔄 重置", use_container_width=True, help="从备份恢复原始配置"):
            reset_to_backup(test_case)

    with col5:
        # 高级编辑按钮
        if st.button("⚙️ 高级编辑", use_container_width=True, help="JSON高级编辑模式"):
            st.session_state.advanced_edit_mode = True
            st.rerun()

    with col6:
        if st.button("💾 保存", type="primary", use_container_width=True):
            mock_config_list = config.get("mockConfigList", [])
            if mock_config_list:
                edited_configs = []
                for i, mock_config in enumerate(mock_config_list):
                    edited_config = collect_edited_config(mock_config, i)
                    edited_configs.append(edited_config)
                save_config_without_close(test_case, edited_configs)

    # 检查是否进入高级编辑模式
    if st.session_state.get('advanced_edit_mode', False):
        render_advanced_edit_mode(config, test_case)
        return

    mock_config_list = config.get("mockConfigList", [])

    if not mock_config_list:
        st.warning("该渠道暂无配置项")
        return

    # 使用左右布局显示所有字段
    render_all_fields_layout(mock_config_list)


def render_advanced_edit_mode(config, test_case):
    """渲染高级编辑模式"""
    st.markdown("### ⚙️ 高级编辑模式 - JSON配置")



    # 退出高级编辑模式按钮
    col1, col2, col3 = st.columns([1, 4, 1])
    with col1:
        if st.button("← 返回普通编辑", use_container_width=True):
            st.session_state.advanced_edit_mode = False
            # 清除相关状态
            if 'just_saved' in st.session_state:
                del st.session_state.just_saved
            st.rerun()

    # 显示当前配置的JSON
    st.markdown("**当前配置JSON:**")

    # 格式化JSON显示
    try:
        formatted_json = json.dumps(config, indent=2, ensure_ascii=False)
    except Exception as e:
        formatted_json = str(config)
        st.error(f"JSON格式化失败: {e}")

    # 可编辑的JSON文本区域
    edited_json = st.text_area(
        "编辑JSON配置",
        value=formatted_json,
        height=400,
        help="在此处编辑完整的JSON配置，支持所有字段的修改"
    )

    # 验证JSON格式
    json_valid = True
    parsed_config = None
    try:
        parsed_config = json.loads(edited_json)
        st.success("✅ JSON格式正确")
    except json.JSONDecodeError as e:
        json_valid = False
        st.error(f"❌ JSON格式错误: {e}")

    # 发送按钮
    col1, col2, col3 = st.columns([2, 1, 2])
    with col2:
        if st.button("🚀 发送保存", type="primary", use_container_width=True, disabled=not json_valid):
            if json_valid and parsed_config:
                save_advanced_config(test_case, parsed_config)

    # 保存状态显示（在发送按钮下方）
    if st.session_state.get('just_saved', False):
        st.success("✅ 配置保存成功")
        # 清除标志，避免重复显示
        st.session_state.just_saved = False

    if st.session_state.get('save_error', False):
        st.error(f"❌ 保存失败: {st.session_state.get('save_error_msg', '未知错误')}")
        # 清除错误标志
        st.session_state.save_error = False
        if 'save_error_msg' in st.session_state:
            del st.session_state.save_error_msg


def save_advanced_config(test_case, config):
    """保存高级编辑的配置"""
    try:
        with st.spinner("正在保存高级编辑配置..."):
            # 提取mockConfigList
            mock_config_list = config.get("mockConfigList", [])

            if not mock_config_list:
                st.error("❌ 配置中没有找到mockConfigList")
                return

            # 调用保存接口 - 使用正确的外部接口
            save_url = "http://web.epaydev.xyz/capi/netforward/mock/addMockConfig"

            payload = {
                "testCase": test_case,
                "mockConfigList": mock_config_list
            }

            response = requests.post(save_url, json=payload, timeout=10)

            if response.status_code == 200:
                result = response.json()

                # 检查多种可能的成功标识
                success_indicators = [
                    result.get("success", False),
                    result.get("code") == 200,
                    result.get("code") == "200",
                    result.get("status") == "success",
                    "成功" in str(result.get("message", "")),
                    "操作成功" in str(result.get("message", ""))
                ]

                if any(success_indicators):
                    # 更新session state中的配置
                    st.session_state.edit_config = config
                    # 设置刚刚保存成功的标志
                    st.session_state.just_saved = True
                    st.rerun()
                else:
                    # 设置错误状态
                    st.session_state.save_error = True
                    st.session_state.save_error_msg = result.get('message', '未知错误')
                    st.rerun()
            else:
                # 设置错误状态
                st.session_state.save_error = True
                st.session_state.save_error_msg = f"状态码: {response.status_code}"
                st.rerun()

    except requests.exceptions.RequestException as e:
        # 设置错误状态
        st.session_state.save_error = True
        st.session_state.save_error_msg = f"网络请求失败: {e}"
        st.rerun()
    except Exception as e:
        # 设置错误状态
        st.session_state.save_error = True
        st.session_state.save_error_msg = f"保存配置时发生错误: {e}"
        st.rerun()

def render_all_fields_layout(mock_config_list):
    """使用左右布局渲染所有字段"""
    # 计算需要多少列来显示所有字段
    total_fields = 0
    for mock_config in mock_config_list:
        # 基本字段数量
        total_fields += 4  # apiUrl, description, mockRule, condition

        # mockBody字段数量
        mock_body = mock_config.get("mockBody", "")
        if isinstance(mock_body, str) and mock_body:
            try:
                parsed_body = json.loads(mock_body)
                if isinstance(parsed_body, dict):
                    total_fields += count_json_fields(parsed_body)
                else:
                    total_fields += 1
            except json.JSONDecodeError:
                total_fields += 1
        else:
            total_fields += 1

    # 使用多列布局，每列最多显示8个字段
    fields_per_column = 8
    num_columns = min(3, max(1, (total_fields + fields_per_column - 1) // fields_per_column))

    columns = st.columns(num_columns)
    current_column = 0
    current_field_count = 0

    # 渲染所有配置项的字段
    for config_index, mock_config in enumerate(mock_config_list):
        # 如果有多个配置项，添加配置分割标识
        if len(mock_config_list) > 1:
            if config_index > 0:
                # 跨所有列添加分割线
                st.markdown("---")
            st.markdown(f"### 配置项 {config_index + 1}")

            # 为每个配置项重新创建列布局
            columns = st.columns(num_columns)
            current_column = 0
            current_field_count = 0

        # 渲染基本字段
        basic_fields = ["apiUrl", "description", "condition"]
        for field in basic_fields:
            if current_field_count >= fields_per_column:
                current_column = (current_column + 1) % num_columns
                current_field_count = 0

            with columns[current_column]:
                value = mock_config.get(field, "")
                st.text_input(
                    f"**{field}**",
                    value=str(value) if value is not None else "",
                    key=f"config_{config_index}_{field}"
                )
            current_field_count += 1

        # 渲染mockRule字段
        if current_field_count >= fields_per_column:
            current_column = (current_column + 1) % num_columns
            current_field_count = 0

        with columns[current_column]:
            mock_rule = mock_config.get("mockRule")
            if mock_rule is None:
                st.text_input(
                    "**mockRule**",
                    value="",
                    key=f"config_{config_index}_mockRule",
                    placeholder="留空表示null"
                )
            else:
                st.text_area(
                    "**mockRule**",
                    value=json.dumps(mock_rule, ensure_ascii=False, indent=2) if isinstance(mock_rule, dict) else str(mock_rule),
                    height=100,
                    key=f"config_{config_index}_mockRule"
                )
        current_field_count += 1

        # 渲染mockBody字段
        mock_body = mock_config.get("mockBody", "")

        # 处理mockBody为对象的情况
        if isinstance(mock_body, dict):
            # mockBody直接是字典对象
            current_column, current_field_count = render_json_fields_in_columns(
                mock_body, f"config_{config_index}_mockBody",
                columns, current_column, current_field_count, fields_per_column
            )
        elif isinstance(mock_body, str) and mock_body:
            try:
                parsed_body = json.loads(mock_body)
                if isinstance(parsed_body, dict):
                    # 递归渲染JSON字段
                    current_column, current_field_count = render_json_fields_in_columns(
                        parsed_body, f"config_{config_index}_mockBody",
                        columns, current_column, current_field_count, fields_per_column
                    )
                else:
                    # 非字典JSON
                    if current_field_count >= fields_per_column:
                        current_column = (current_column + 1) % num_columns
                        current_field_count = 0

                    with columns[current_column]:
                        st.text_area(
                            "**mockBody**",
                            value=json.dumps(parsed_body, ensure_ascii=False, indent=2),
                            height=100,
                            key=f"config_{config_index}_mockBody"
                        )
                    current_field_count += 1
            except json.JSONDecodeError:
                # 非JSON字符串
                if current_field_count >= fields_per_column:
                    current_column = (current_column + 1) % num_columns
                    current_field_count = 0

                with columns[current_column]:
                    st.text_area(
                        "**mockBody**",
                        value=mock_body,
                        height=100,
                        key=f"config_{config_index}_mockBody"
                    )
                current_field_count += 1
        else:
            # 空的mockBody
            if current_field_count >= fields_per_column:
                current_column = (current_column + 1) % num_columns
                current_field_count = 0

            with columns[current_column]:
                st.text_area(
                    "**mockBody**",
                    value="",
                    height=100,
                    key=f"config_{config_index}_mockBody"
                )
            current_field_count += 1

        # createTime字段（只读）
        if current_field_count >= fields_per_column:
            current_column = (current_column + 1) % num_columns
            current_field_count = 0

        with columns[current_column]:
            create_time = mock_config.get("createTime", "")
            st.text_input(
                "**createTime**",
                value=str(create_time),
                disabled=True,
                key=f"config_{config_index}_createTime_readonly"
            )
        current_field_count += 1







def count_json_fields(json_obj):
    """递归计算JSON对象中的字段数量，包括所有嵌套结构"""
    if json_obj is None:
        return 1

    if not isinstance(json_obj, (dict, list)):
        return 1

    count = 0
    if isinstance(json_obj, dict):
        for key, value in json_obj.items():
            if isinstance(value, dict):
                # 嵌套对象，递归计算
                count += count_json_fields(value)
            elif isinstance(value, list):
                # 数组，检查是否包含对象
                if value and len(value) > 0:
                    if isinstance(value[0], dict):
                        for item in value:
                            if isinstance(item, dict):
                                count += count_json_fields(item)
                            else:
                                count += 1
                    else:
                        count += 1
                else:
                    count += 1
            else:
                # 检查字符串是否为JSON
                if isinstance(value, str) and value.strip() and (value.strip().startswith('{') or value.strip().startswith('[')):
                    try:
                        parsed_json = json.loads(value)
                        count += count_json_fields(parsed_json)
                    except (json.JSONDecodeError, TypeError):
                        count += 1
                else:
                    count += 1
    elif isinstance(json_obj, list):
        for item in json_obj:
            count += count_json_fields(item)

    return count


def render_json_fields_in_columns(json_obj, key_prefix, columns, current_column, current_field_count, fields_per_column):
    """在多列中递归渲染JSON字段，完全拆解所有JSON结构"""
    # 安全检查
    if not isinstance(json_obj, dict):
        return current_column, current_field_count

    if not columns or len(columns) == 0:
        return current_column, current_field_count

    try:
        for json_key, json_value in json_obj.items():
            if current_field_count >= fields_per_column:
                current_column = (current_column + 1) % len(columns)
                current_field_count = 0

            with columns[current_column]:
                if isinstance(json_value, dict):
                    # 嵌套对象，递归拆解
                    current_column, current_field_count = render_json_fields_in_columns(
                        json_value, f"{key_prefix}_{json_key}",
                        columns, current_column, current_field_count, fields_per_column
                    )
                    # 由于递归调用已经处理了字段计数，这里不需要再增加
                    continue
                elif isinstance(json_value, list):
                    # 数组，检查是否包含对象
                    if json_value and len(json_value) > 0:
                        try:
                            if isinstance(json_value[0], dict):
                                # 如果数组包含对象，为每个对象创建字段
                                for i, item in enumerate(json_value):
                                    if isinstance(item, dict):
                                        current_column, current_field_count = render_json_fields_in_columns(
                                            item, f"{key_prefix}_{json_key}_{i}",
                                            columns, current_column, current_field_count, fields_per_column
                                        )
                                    else:
                                        # 数组中的简单值
                                        if current_field_count >= fields_per_column:
                                            current_column = (current_column + 1) % len(columns)
                                            current_field_count = 0

                                        with columns[current_column]:
                                            st.text_input(
                                                f"**{json_key}[{i}]**",
                                                value=str(item) if item is not None else "",
                                                key=f"{key_prefix}_{json_key}_{i}"
                                            )
                                        current_field_count += 1
                                continue
                            else:
                                # 简单数组，使用文本区域
                                st.text_area(
                                    f"**{json_key} (数组)**",
                                    value=json.dumps(json_value, ensure_ascii=False, indent=2),
                                    height=100,
                                    key=f"{key_prefix}_{json_key}"
                                )
                        except (IndexError, TypeError):
                            # 数组为空或有其他问题，使用文本区域
                            st.text_area(
                                f"**{json_key} (数组)**",
                                value=json.dumps(json_value, ensure_ascii=False, indent=2),
                                height=100,
                                key=f"{key_prefix}_{json_key}"
                            )
                    else:
                        # 空数组，使用文本区域
                        st.text_area(
                            f"**{json_key} (空数组)**",
                            value="[]",
                            height=100,
                            key=f"{key_prefix}_{json_key}"
                        )
                elif isinstance(json_value, bool):
                    # 布尔值
                    st.selectbox(
                        f"**{json_key}**",
                        options=[True, False],
                        index=0 if json_value else 1,
                        key=f"{key_prefix}_{json_key}"
                    )
                elif isinstance(json_value, (int, float)):
                    # 数字
                    st.number_input(
                        f"**{json_key}**",
                        value=json_value,
                        key=f"{key_prefix}_{json_key}"
                    )
                elif json_value is None:
                    # null值
                    st.text_input(
                        f"**{json_key}**",
                        value="",
                        key=f"{key_prefix}_{json_key}",
                        placeholder="null"
                    )
                else:
                    # 字符串或其他类型，检查是否是JSON字符串
                    value_str = str(json_value) if json_value is not None else ""

                    # 尝试解析字符串是否为JSON
                    if value_str and (value_str.strip().startswith('{') or value_str.strip().startswith('[')):
                        try:
                            parsed_json = json.loads(value_str)
                            if isinstance(parsed_json, dict):
                                # 字符串是JSON对象，递归拆解
                                current_column, current_field_count = render_json_fields_in_columns(
                                    parsed_json, f"{key_prefix}_{json_key}",
                                    columns, current_column, current_field_count, fields_per_column
                                )
                                continue
                            elif isinstance(parsed_json, list) and parsed_json and len(parsed_json) > 0 and isinstance(parsed_json[0], dict):
                                # 字符串是JSON数组且包含对象
                                for i, item in enumerate(parsed_json):
                                    if isinstance(item, dict):
                                        current_column, current_field_count = render_json_fields_in_columns(
                                            item, f"{key_prefix}_{json_key}_{i}",
                                            columns, current_column, current_field_count, fields_per_column
                                        )
                                continue
                        except (json.JSONDecodeError, TypeError, IndexError):
                            pass  # 不是有效的JSON，按普通字符串处理

                    # 普通字符串
                    if len(value_str) > 50:
                        st.text_area(
                            f"**{json_key}**",
                            value=value_str,
                            height=100,
                            key=f"{key_prefix}_{json_key}"
                        )
                    else:
                        st.text_input(
                            f"**{json_key}**",
                            value=value_str,
                            key=f"{key_prefix}_{json_key}"
                        )

            current_field_count += 1

    except Exception as e:
        # 如果渲染过程中出现任何错误，记录并继续
        st.error(f"渲染字段时出错: {str(e)}")

    return current_column, current_field_count


def collect_edited_config(mock_config, index):
    """收集编辑后的配置数据"""
    edited_config = {}

    # 收集基本字段
    basic_fields = ["apiUrl", "description", "condition"]
    for field in basic_fields:
        key = f"config_{index}_{field}"
        if key in st.session_state:
            value = st.session_state[key]
            # 处理空值为null
            if value == "" or value == "null":
                edited_config[field] = None
            else:
                edited_config[field] = value
        else:
            original_value = mock_config.get(field, "")
            edited_config[field] = None if original_value == "" or original_value == "null" else original_value

    # 收集mockRule
    mock_rule_key = f"config_{index}_mockRule"
    if mock_rule_key in st.session_state:
        mock_rule_value = st.session_state[mock_rule_key]
        if mock_rule_value == "" or mock_rule_value == "null":
            edited_config["mockRule"] = None
        elif isinstance(mock_rule_value, str) and mock_rule_value.strip().startswith(("{", "[")):
            try:
                edited_config["mockRule"] = json.loads(mock_rule_value)
            except json.JSONDecodeError:
                edited_config["mockRule"] = mock_rule_value
        else:
            edited_config["mockRule"] = mock_rule_value
    else:
        original_mock_rule = mock_config.get("mockRule")
        edited_config["mockRule"] = None if original_mock_rule == "" or original_mock_rule == "null" else original_mock_rule

    # 收集mockBody
    mock_body = mock_config.get("mockBody", "")

    # 处理mockBody为对象的情况
    if isinstance(mock_body, dict):
        # mockBody直接是字典对象，重建JSON对象
        rebuilt_body = collect_json_fields(mock_body, f"config_{index}_mockBody")
        edited_config["mockBody"] = rebuilt_body
    elif isinstance(mock_body, str) and mock_body:
        try:
            parsed_body = json.loads(mock_body)
            if isinstance(parsed_body, dict):
                # 重建JSON对象
                rebuilt_body = collect_json_fields(parsed_body, f"config_{index}_mockBody")
                edited_config["mockBody"] = json.dumps(rebuilt_body, ensure_ascii=False, separators=(',', ':'))
            else:
                # 非字典JSON
                mock_body_key = f"config_{index}_mockBody"
                if mock_body_key in st.session_state:
                    value = st.session_state[mock_body_key]
                    edited_config["mockBody"] = "" if value == "null" else value
                else:
                    edited_config["mockBody"] = mock_body
        except json.JSONDecodeError:
            # 非JSON字符串
            mock_body_key = f"config_{index}_mockBody"
            if mock_body_key in st.session_state:
                value = st.session_state[mock_body_key]
                edited_config["mockBody"] = "" if value == "null" else value
            else:
                edited_config["mockBody"] = mock_body
    else:
        # 空的mockBody
        mock_body_key = f"config_{index}_mockBody"
        if mock_body_key in st.session_state:
            value = st.session_state[mock_body_key]
            edited_config["mockBody"] = "" if value == "null" else value
        else:
            edited_config["mockBody"] = ""

    # createTime保持不变
    edited_config["createTime"] = mock_config.get("createTime", "")

    return edited_config


def collect_json_fields(json_obj, key_prefix):
    """递归收集JSON字段的编辑值，完全重建所有嵌套结构"""
    # 安全检查
    if json_obj is None:
        return None

    if not isinstance(json_obj, (dict, list)):
        return json_obj

    if isinstance(json_obj, dict):
        rebuilt_obj = {}
        for json_key, json_value in json_obj.items():
            if isinstance(json_value, dict):
                # 嵌套对象，递归收集
                rebuilt_obj[json_key] = collect_json_fields(json_value, f"{key_prefix}_{json_key}")
            elif isinstance(json_value, list):
                # 数组处理
                if json_value and len(json_value) > 0 and isinstance(json_value[0], dict):
                    # 对象数组
                    rebuilt_array = []
                    for i, item in enumerate(json_value):
                        if isinstance(item, dict):
                            rebuilt_item = collect_json_fields(item, f"{key_prefix}_{json_key}_{i}")
                            rebuilt_array.append(rebuilt_item)
                        else:
                            # 简单值
                            field_key = f"{key_prefix}_{json_key}_{i}"
                            if field_key in st.session_state:
                                value = st.session_state[field_key]
                                # 尝试转换为原始类型
                                if isinstance(item, (int, float)) and str(value).replace('.', '').replace('-', '').isdigit():
                                    rebuilt_array.append(float(value) if '.' in str(value) else int(value))
                                elif isinstance(item, bool):
                                    rebuilt_array.append(value)
                                else:
                                    rebuilt_array.append(value)
                            else:
                                rebuilt_array.append(item)
                    rebuilt_obj[json_key] = rebuilt_array
                else:
                    # 简单数组，检查是否有对应的session state
                    field_key = f"{key_prefix}_{json_key}"
                    if field_key in st.session_state:
                        try:
                            rebuilt_obj[json_key] = json.loads(st.session_state[field_key])
                        except (json.JSONDecodeError, TypeError):
                            rebuilt_obj[json_key] = st.session_state[field_key]
                    else:
                        rebuilt_obj[json_key] = json_value
            else:
                # 简单字段
                field_key = f"{key_prefix}_{json_key}"
                if field_key in st.session_state:
                    field_value = st.session_state[field_key]

                    # 处理空值和null值
                    if field_value == "" or field_value == "null":
                        rebuilt_obj[json_key] = None
                    elif isinstance(json_value, bool):
                        rebuilt_obj[json_key] = field_value
                    elif isinstance(json_value, (int, float)):
                        # 数字类型处理
                        if field_value == "":
                            rebuilt_obj[json_key] = None
                        else:
                            rebuilt_obj[json_key] = field_value
                    elif isinstance(json_value, str):
                        # 检查原始值是否为JSON字符串
                        if json_value and json_value.strip() and (json_value.strip().startswith('{') or json_value.strip().startswith('[')):
                            try:
                                original_parsed = json.loads(json_value)
                                # 如果原始值是JSON，尝试重建
                                rebuilt_json = collect_json_fields(original_parsed, f"{key_prefix}_{json_key}")
                                rebuilt_obj[json_key] = json.dumps(rebuilt_json, ensure_ascii=False, separators=(',', ':'))
                            except (json.JSONDecodeError, TypeError):
                                rebuilt_obj[json_key] = field_value if field_value != "" else None
                        else:
                            rebuilt_obj[json_key] = field_value if field_value != "" else None
                    elif json_value is None:
                        # 原始值为null
                        rebuilt_obj[json_key] = field_value if field_value != "" else None
                    else:
                        rebuilt_obj[json_key] = field_value if field_value != "" else None
                else:
                    # 保持原始值，但处理空字符串为null
                    original_value = json_value
                    if original_value == "" or original_value == "null":
                        rebuilt_obj[json_key] = None
                    else:
                        rebuilt_obj[json_key] = original_value



        return rebuilt_obj

    elif isinstance(json_obj, list):
        rebuilt_array = []
        for i, item in enumerate(json_obj):
            if isinstance(item, dict):
                rebuilt_item = collect_json_fields(item, f"{key_prefix}_{i}")
                rebuilt_array.append(rebuilt_item)
            else:
                field_key = f"{key_prefix}_{i}"
                if field_key in st.session_state:
                    rebuilt_array.append(st.session_state[field_key])
                else:
                    rebuilt_array.append(item)
        return rebuilt_array

    return json_obj








def save_config(test_case, mock_config_list):
    """保存配置到服务器"""
    try:
        # 处理数据格式
        processed_config_list = []
        for config in mock_config_list:
            processed_config = {}

            for key, value in config.items():
                if key == "mockBody":
                    if isinstance(value, dict):
                        processed_config[key] = json.dumps(value, ensure_ascii=False, separators=(',', ':'))
                    elif value == "" or value == "null":
                        processed_config[key] = None
                    else:
                        processed_config[key] = value
                elif key == "mockRule":
                    if value == "" or value == "null":
                        processed_config[key] = None
                    elif isinstance(value, str) and value.strip().startswith(("{", "[")):
                        try:
                            processed_config[key] = json.loads(value)
                        except json.JSONDecodeError:
                            processed_config[key] = value
                    else:
                        processed_config[key] = value
                else:
                    if value == "" or value == "null":
                        processed_config[key] = None
                    else:
                        processed_config[key] = value

            processed_config_list.append(processed_config)

        save_data = {
            "testCase": test_case,
            "mockConfigList": processed_config_list
        }

        # 直接保存
        with st.spinner("正在保存..."):
            url = "http://web.epaydev.xyz/capi/netforward/mock/addMockConfig"
            headers = {"Content-Type": "application/json"}
            response = requests.post(url, headers=headers, json=save_data, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 1:
                    st.success("✅ 保存成功！")
                    # 自动刷新数据
                    st.session_state.edit_mode = False
                    if 'edit_config' in st.session_state:
                        del st.session_state.edit_config
                    if 'mock_configs' in st.session_state:
                        del st.session_state.mock_configs
                    st.rerun()
                else:
                    st.error(f"❌ 保存失败: {result.get('message', '未知错误')}")
            else:
                st.error(f"❌ 请求失败，状态码: {response.status_code}")

    except Exception as e:
        st.error(f"❌ 保存失败: {str(e)}")


def save_config_without_close(test_case: str, mock_config_list: List[Dict[str, Any]]):
    """保存配置但不关闭页面"""
    try:
        # 构建保存数据
        save_data = {
            "testCase": test_case,
            "mockConfigList": []
        }

        # 处理每个配置项
        for config in mock_config_list:
            processed_config = {}
            for key, value in config.items():
                if key == "mockBody":
                    if value == "" or value == "null":
                        processed_config[key] = None
                    else:
                        # mockBody直接保存为字符串，不做额外处理
                        processed_config[key] = value
                else:
                    processed_config[key] = value
            save_data["mockConfigList"].append(processed_config)

        # 发送保存请求
        with st.spinner("正在保存配置..."):
            url = "http://web.epaydev.xyz/capi/netforward/mock/addMockConfig"
            headers = {"Content-Type": "application/json"}
            response = requests.post(url, headers=headers, json=save_data, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 1:
                    st.success("✅ 保存成功！")
                    st.info("💡 配置已更新，您可以继续编辑或返回列表")

                    # 可选：刷新当前配置数据
                    # 这里可以重新获取最新数据，但为了保持用户的编辑状态，暂时不刷新

                else:
                    st.error(f"❌ 保存失败: {result.get('message', '未知错误')}")
            else:
                st.error(f"❌ 请求失败，状态码: {response.status_code}")

    except Exception as e:
        st.error(f"❌ 保存失败: {str(e)}")


def backup_current_config(test_case: str, config: Dict[str, Any]):
    """备份当前配置到本地数据库"""
    try:
        # 获取当前的完整配置数据
        mock_config_list = config.get("mockConfigList", [])

        # 构建备份数据结构
        backup_data = {
            "testCase": test_case,
            "mockConfigList": mock_config_list
        }

        # 保存到数据库
        description = f"手动备份 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        success = mock_backup_db.save_channel_backup(test_case, backup_data, description)

        if success:
            st.success(f"✅ 渠道 {test_case} 备份成功！")
            st.info(f"📝 备份描述: {description}")
        else:
            st.error("❌ 备份失败，请重试")

    except Exception as e:
        st.error(f"❌ 备份过程中出错: {str(e)}")


def reset_to_backup(test_case: str):
    """从备份恢复配置"""
    try:
        # 检查是否存在备份
        if not mock_backup_db.backup_exists(test_case):
            st.warning(f"⚠️ 渠道 {test_case} 没有找到备份数据")
            st.info("💡 请先点击'💾 备份'按钮保存当前配置")
            return

        # 获取备份数据
        backup_data = mock_backup_db.get_channel_backup(test_case)

        if backup_data:
            # 清除当前的编辑状态 - 只清除config_开头的session state
            keys_to_remove = [key for key in st.session_state.keys() if key.startswith('config_')]
            for key in keys_to_remove:
                del st.session_state[key]

            # 更新edit_config为备份数据，移除备份元信息
            clean_backup_data = {
                "testCase": backup_data.get("testCase", ""),
                "mockConfigList": backup_data.get("mockConfigList", [])
            }
            st.session_state.edit_config = clean_backup_data

            # 显示恢复信息
            backup_info = backup_data.get("_backup_info", {})
            backup_time = backup_info.get("backup_time", "未知")
            description = backup_info.get("description", "无描述")
            config_count = backup_info.get("config_count", 0)

            st.success(f"✅ 已从备份恢复配置！")
            st.info(f"📅 备份时间: {backup_time}")
            st.info(f"📝 备份描述: {description}")
            st.info(f"📊 配置项数量: {config_count}")

            # 刷新页面以显示恢复的数据
            st.rerun()
        else:
            st.error("❌ 获取备份数据失败")

    except Exception as e:
        st.error(f"❌ 恢复过程中出错: {str(e)}")


def auto_backup_on_first_edit(test_case: str, config: Dict[str, Any]):
    """在首次编辑时自动备份原始配置"""
    try:
        # 检查是否已经有备份
        if not mock_backup_db.backup_exists(test_case):
            # 构建备份数据结构
            backup_data = {
                "testCase": test_case,
                "mockConfigList": config.get("mockConfigList", [])
            }

            # 自动备份
            description = f"首次编辑自动备份 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            success = mock_backup_db.save_channel_backup(test_case, backup_data, description)

            if success:
                st.info(f"💾 已自动备份原始配置")

    except Exception as e:
        # 自动备份失败不影响正常编辑
        pass


if __name__ == "__main__":
    render()
