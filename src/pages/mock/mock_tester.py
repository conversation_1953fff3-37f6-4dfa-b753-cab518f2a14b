#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/21
# <AUTHOR> Kai


"""
Mock API 测试器 - 简化版
"""

import streamlit as st
import requests
import json
import time
from src.mock.dao import mock_dao


def render():
    """渲染Mock测试页面"""
    st.title("🧪 API 测试")

    # 直接渲染Mock API测试
    render_mock_api_test()


def render_mock_api_test():
    """渲染Mock API测试"""
    # 左右布局
    col_left, col_right = st.columns([1, 1])

    with col_left:

        # 获取所有Mock配置
        test_cases = mock_dao.list_test_cases()
        all_configs = []
        for tc in test_cases:
            configs = mock_dao.get_mock_configs_by_test_case(tc)
            all_configs.extend(configs)

        if not all_configs:
            st.warning("暂无Mock配置，请先在API管理页面创建")
            return

        # API选择
        api_options = []
        for config in all_configs:
            option_text = f"{config['api_url']} - {config['description'] or '无描述'}"
            api_options.append(option_text)

        selected_api = st.selectbox("选择要测试的API", api_options)

        if selected_api:
            # 解析选择的配置
            api_url = selected_api.split(" - ")[0]
            selected_config = next((c for c in all_configs if c['api_url'] == api_url), None)

            if selected_config:
                # 构建完整URL - 直接使用API路径，不加项目前缀
                base_url = "http://localhost:8001"
                full_url = f"{base_url}/mock{selected_config['api_url']}"

                # 显示URL
                st.code(full_url, language="text")

                # 请求头
                headers_input = st.text_area(
                    "请求头 (JSON格式)",
                    value='{\n  "Content-Type": "application/json"\n}',
                    height=100
                )

                # 请求体
                body_input = st.text_area(
                    "请求体 (JSON格式)",
                    value='{\n  "test": true\n}',
                    height=120
                )

                # 发送测试按钮
                if st.button("🚀 发送测试", type="primary", use_container_width=True):
                    send_mock_test(full_url, headers_input, body_input)

    with col_right:
        st.markdown("### 📥 响应结果")

        # 显示测试结果
        if "test_result" in st.session_state:
            render_test_result()
        else:
            st.info("选择接口并发送测试查看响应结果")


def send_mock_test(url, headers_text, body_text):
    """发送Mock测试请求"""
    try:
        # 解析请求头
        headers = {}
        if headers_text.strip():
            headers = json.loads(headers_text)

        # 解析请求体
        body = {}
        if body_text.strip():
            body = json.loads(body_text)

        # 记录开始时间
        start_time = time.time()

        # 发送请求
        with st.spinner("🚀 发送请求中..."):
            response = requests.post(url, json=body, headers=headers, timeout=10)

        # 计算响应时间
        response_time = (time.time() - start_time) * 1000

        # 保存测试结果
        st.session_state.test_result = {
            "url": url,
            "method": "POST",
            "status_code": response.status_code,
            "response_time": response_time,
            "response_size": len(response.content),
            "response_headers": dict(response.headers),
            "response_body": response.text,
            "success": response.status_code == 200
        }

        st.rerun()

    except json.JSONDecodeError as e:
        st.error(f"❌ JSON格式错误: {str(e)}")
    except requests.exceptions.ConnectionError:
        st.error("❌ 连接失败，请检查Mock服务是否启动")
    except requests.exceptions.Timeout:
        st.error("❌ 请求超时")
    except Exception as e:
        st.error(f"❌ 请求失败: {str(e)}")


def render_test_result():
    """渲染测试结果"""
    result = st.session_state.test_result

    # 状态信息
    col_status, col_time, col_size = st.columns(3)

    with col_status:
        # 根据状态码添加颜色标识
        if result["success"]:
            status_display = f"🟢 {result['status_code']}"
        else:
            status_display = f"🔴 {result['status_code']}"
        st.metric("状态码", status_display)

    with col_time:
        st.metric("响应时间", f"{result['response_time']:.0f}ms")

    with col_size:
        st.metric("响应大小", f"{result['response_size']} bytes")

    # 响应内容
    tab1, tab2 = st.tabs(["📄 响应体", "📝 响应头"])

    with tab1:
        if result["response_body"]:
            format_choice = st.radio("显示格式", ["Pretty JSON", "Raw Text"], horizontal=True)

            if format_choice == "Pretty JSON":
                try:
                    parsed_json = json.loads(result["response_body"])
                    st.json(parsed_json)
                except:
                    st.code(result["response_body"], language="text")
            else:
                st.code(result["response_body"], language="text")
        else:
            st.info("无响应体")

    with tab2:
        if result["response_headers"]:
            # 格式化显示响应头
            for key, value in result["response_headers"].items():
                st.text(f"{key}: {value}")
        else:
            st.info("无响应头")


if __name__ == "__main__":
    render()
