#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/21
# <AUTHOR> Kai

"""
Mock管理页面 - 简洁版重新设计
"""

import streamlit as st
import pandas as pd
from src.mock.dao import mock_dao
import json
import requests

def render():
    """渲染Mock管理页面"""
    st.title("🎭 Mock API 管理")

    # 简洁的顶部操作栏
    col1, col2 = st.columns([2, 1])
    with col1:
        search_term = st.text_input("🔍 搜索API", placeholder="输入API路径或描述")
    with col2:
        st.write("")
        st.write("")
        if st.button("新建API", type="primary", use_container_width=True):
            st.session_state.show_create_modal = True

    # 主要内容区域
    render_api_management(search_term)

    # 新建API表单
    if st.session_state.get("show_create_modal", False):
        render_create_modal()

    # 编辑API表单
    if st.session_state.get("show_edit_modal", False):
        render_edit_modal()


def render_api_management(search_term=""):
    """渲染API管理主界面"""
    # 获取所有配置
    test_cases = mock_dao.list_test_cases()
    all_configs = []
    for tc in test_cases:
        configs = mock_dao.get_mock_configs_by_test_case(tc)
        all_configs.extend(configs)

    # 搜索过滤
    if search_term:
        all_configs = [c for c in all_configs
                      if search_term.lower() in c['api_url'].lower()
                      or search_term.lower() in (c['description'] or '').lower()]

    if not all_configs:
        render_empty_state()
        return

    # 主要内容：接口列表
    st.markdown("---")

    # 转换为DataFrame用于表格展示
    df_data = []
    for config in all_configs:
        # 获取启用状态
        enabled = config.get("enabled", True)
        status_text = "🟢 启用" if enabled else "🔴 禁用"

        # 处理更新时间
        update_time = config.get("update_time", "")
        if update_time:
            if isinstance(update_time, str):
                time_display = update_time[:16]
            else:
                # 如果是时间戳，转换为字符串
                try:
                    import datetime
                    time_display = datetime.datetime.fromtimestamp(update_time).strftime("%Y-%m-%d %H:%M")
                except:
                    time_display = "未知"
        else:
            time_display = "未知"

        df_data.append({
            "ID": config["id"],
            "API路径": config["api_url"],
            "描述": config["description"] or "无描述",
            "状态": status_text,
            "更新时间": time_display
        })

    df = pd.DataFrame(df_data)

    # 显示表格
    selected_rows = st.dataframe(
        df,
        use_container_width=True,
        hide_index=True,
        on_select="rerun",
        selection_mode="single-row",
        column_config={
            "ID": st.column_config.NumberColumn("ID", width="small"),
            "API路径": st.column_config.TextColumn("API路径", width="large"),
            "描述": st.column_config.TextColumn("描述", width="medium"),
            "状态": st.column_config.TextColumn("状态", width="small"),
            "更新时间": st.column_config.TextColumn("更新时间", width="medium")
        }
    )

    # 选中接口的详细信息
    if selected_rows.selection.rows:
        selected_id = df.iloc[selected_rows.selection.rows[0]]["ID"]
        selected_config = next((c for c in all_configs if c["id"] == selected_id), None)

        if selected_config:
            st.markdown("---")

            # 完整URL - 直接使用API路径，不加项目前缀
            base_url = "http://localhost:8001"
            full_url = f"{base_url}/mock{selected_config['api_url']}"
            st.code(full_url, language="text")

            # 响应体
            if selected_config['mock_body']:
                st.markdown("**📄 响应体**")
                try:
                    parsed_body = json.loads(selected_config['mock_body'])
                    st.json(parsed_body)
                except:
                    st.code(selected_config['mock_body'], language="json")

            # 操作按钮
            st.markdown("---")
            col_btn1, col_btn2, col_btn3, col_btn4 = st.columns(4)
            with col_btn1:
                if st.button("🧪 测试接口", type="primary", use_container_width=True):
                    test_api(selected_config)
            with col_btn2:
                enabled = selected_config.get("enabled", True)
                toggle_text = "🔴 禁用" if enabled else "🟢 启用"
                if st.button(toggle_text, use_container_width=True):
                    if mock_dao.toggle_mock_config_status(selected_config['id']):
                        st.success("状态切换成功")
                        st.rerun()
                    else:
                        st.error("状态切换失败")
            with col_btn3:
                if st.button("✏️ 编辑接口", use_container_width=True):
                    st.session_state.edit_config = selected_config
                    st.session_state.show_edit_modal = True
            with col_btn4:
                if st.button("🗑️ 删除接口", use_container_width=True):
                    if mock_dao.delete_mock_config(selected_config['id']):
                        st.success("删除成功")
                        st.rerun()
                    else:
                        st.error("删除失败")


def render_empty_state():
    """渲染空状态"""
    st.info("暂无API配置")


def render_create_modal():
    """渲染创建API模态框"""
    with st.expander("➕ 创建新的Mock API", expanded=True):
        with st.form("create_api_form"):
            # 基本信息 - 紧凑布局
            col1, col2 = st.columns(2)
            with col1:
                api_path = st.text_input("API路径 *", placeholder="/api/users")
            with col2:
                method = st.selectbox("HTTP方法", ["POST", "GET", "PUT", "DELETE"])

            # 第二行
            col3, col4, col5 = st.columns(3)
            with col3:
                name = st.text_input("API名称", placeholder="用户列表接口")
            with col4:
                status_code = st.selectbox("状态码", [200, 201, 400, 401, 403, 404, 500])
            with col5:
                template = st.selectbox("响应模板", [
                    "自定义",
                    "标准成功响应",
                    "列表数据响应",
                    "错误响应",
                    "分页数据响应"
                ])

            # 描述
            description = st.text_input("描述", placeholder="API功能描述")

            # 使用固定的项目名称
            project = "default"

            # 响应体
            if template != "自定义":
                response_body = get_response_template(template)
                st.markdown("**响应体模板:**")
                response_input = st.text_area("响应体 (JSON)", value=response_body, height=150)
            else:
                response_input = st.text_area("响应体 (JSON)",
                                            placeholder='{"code": 200, "message": "success", "data": {}}',
                                            height=150)

            # 高级配置 - 单行
            with st.expander("高级配置", expanded=False):
                col_adv1, col_adv2, col_adv3 = st.columns(3)
                with col_adv1:
                    delay = st.number_input("响应延迟 (毫秒)", 0, 5000, 0)
                with col_adv2:
                    condition = st.text_input("匹配条件", placeholder="amount>100")
                with col_adv3:
                    enabled = st.checkbox("启用接口", value=True)

            # 提交按钮
            col_submit1, col_submit2 = st.columns(2)
            with col_submit1:
                if st.form_submit_button("✅ 创建API", type="primary"):
                    create_api(project, api_path, method, name, description, response_input, status_code, condition, enabled)

            with col_submit2:
                if st.form_submit_button("❌ 取消"):
                    st.session_state.show_create_modal = False
                    st.rerun()


def get_response_template(template_type):
    """获取响应模板"""
    templates = {
        "标准成功响应": """{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "示例数据",
    "status": "active",
    "createTime": "2025-01-24T10:00:00Z"
  },
  "timestamp": 1737705600000
}""",
        "列表数据响应": """{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "项目1",
      "status": "active"
    },
    {
      "id": 2,
      "name": "项目2",
      "status": "inactive"
    }
  ],
  "total": 2
}""",
        "错误响应": """{
  "code": 400,
  "message": "请求参数错误",
  "error": {
    "field": "email",
    "message": "邮箱格式不正确"
  },
  "timestamp": 1737705600000
}""",
        "分页数据响应": """{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "数据1"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 10,
      "total": 100,
      "pages": 10
    }
  }
}"""
    }
    return templates.get(template_type, "{}")





def render_edit_modal():
    """渲染编辑API模态框"""
    if "edit_config" not in st.session_state:
        return

    config = st.session_state.edit_config

    with st.expander("✏️ 编辑Mock API", expanded=True):
        with st.form("edit_api_form"):
            # 显示不可编辑的信息
            st.text(f"API路径: {config['api_url']}")

            # 可编辑的字段
            description = st.text_input("描述", value=config['description'] or "")

            # 响应体
            response_input = st.text_area(
                "响应体 (JSON)",
                value=config['mock_body'] or "",
                height=200
            )

            # 高级配置
            with st.expander("高级配置", expanded=False):
                condition = st.text_input("匹配条件", value=config.get('condition', '') or "")

            # 提交按钮
            col_submit1, col_submit2 = st.columns(2)
            with col_submit1:
                if st.form_submit_button("✅ 保存修改", type="primary"):
                    update_api(config['id'], description, response_input, condition)

            with col_submit2:
                if st.form_submit_button("❌ 取消"):
                    st.session_state.show_edit_modal = False
                    if "edit_config" in st.session_state:
                        del st.session_state.edit_config
                    st.rerun()


def create_api(project, api_path, method, name, description, response_body, status_code, condition, enabled):
    """创建API"""
    try:
        # 验证JSON格式
        json.loads(response_body)

        success = mock_dao.insert_mock_config(
            test_case=project,
            api_url=api_path,
            description=f"{name} - {description}",
            mock_body=response_body,
            condition=condition,
            enabled=enabled
        )

        if success:
            st.success("✅ API创建成功")
            st.session_state.show_create_modal = False
            st.rerun()
        else:
            st.error("❌ API创建失败")

    except json.JSONDecodeError:
        st.error("❌ 响应体JSON格式错误")


def update_api(config_id, description, response_body, condition):
    """更新API"""
    try:
        # 验证JSON格式
        if response_body.strip():
            json.loads(response_body)

        success = mock_dao.update_mock_config(
            config_id=config_id,
            description=description,
            mock_body=response_body,
            condition=condition
        )

        if success:
            st.success("✅ API更新成功")
            st.session_state.show_edit_modal = False
            if "edit_config" in st.session_state:
                del st.session_state.edit_config
            st.rerun()
        else:
            st.error("❌ API更新失败")

    except json.JSONDecodeError:
        st.error("❌ 响应体JSON格式错误")


def test_api(config):
    """测试API"""
    st.session_state.test_config = config
    st.info(f"🧪 开始测试API: {config['api_url']}")

    # 构建测试URL - 直接使用API路径，不加项目前缀
    base_url = "http://localhost:8001"
    test_url = f"{base_url}/mock{config['api_url']}"

    try:
        with st.spinner("发送测试请求..."):
            response = requests.post(test_url,
                                   json={"test": True},
                                   headers={"Content-Type": "application/json"},
                                   timeout=5)

        if response.status_code == 200:
            st.success(f"✅ 测试成功 - 状态码: {response.status_code}")
            with st.expander("查看响应", expanded=True):
                try:
                    st.json(response.json())
                except:
                    st.code(response.text)
        else:
            st.error(f"❌ 测试失败 - 状态码: {response.status_code}")

    except requests.exceptions.ConnectionError:
        st.error("❌ 连接失败，请确保Mock服务已启动")
    except Exception as e:
        st.error(f"❌ 测试失败: {str(e)}")


def delete_api(config_id):
    """删除API"""
    if mock_dao.delete_mock_config(config_id):
        st.success("✅ API删除成功")
        st.rerun()
    else:
        st.error("❌ API删除失败")





if __name__ == "__main__":
    render()
