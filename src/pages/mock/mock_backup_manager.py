#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/21
# <AUTHOR> Kai

"""
Mock配置备份管理页面
用于查看、管理和恢复所有渠道的备份数据
"""

import streamlit as st
import pandas as pd
import json
from datetime import datetime
from src.database.mock_backup import mock_backup_db


def render():
    """渲染备份管理页面"""
    st.title("🗄️ Mock配置备份管理")
    st.markdown("---")

    # 获取所有备份
    backups = mock_backup_db.list_all_backups()

    if not backups:
        st.info("📭 暂无备份数据")
        st.markdown("💡 **提示**: 在Mock编辑页面点击'💾 备份'按钮可以保存配置备份")
        return

    # 统计信息
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("📊 总备份数", len(backups))
    with col2:
        total_configs = sum(backup.get("config_count", 0) for backup in backups)
        st.metric("⚙️ 总配置项", total_configs)
    with col3:
        latest_backup = max(backups, key=lambda x: x.get("update_time", ""))
        latest_time = latest_backup.get("update_time", "").split(" ")[0] if latest_backup else "无"
        st.metric("📅 最新备份", latest_time)
    with col4:
        unique_channels = len(set(backup.get("test_case", "") for backup in backups))
        st.metric("🔗 渠道数量", unique_channels)

    st.markdown("---")

    # 搜索和过滤
    col1, col2, col3 = st.columns(3)
    with col1:
        search_term = st.text_input("🔍 搜索渠道名称", placeholder="输入渠道名称进行搜索...")
    with col2:
        sort_by = st.selectbox("📊 排序方式", ["更新时间", "渠道名称", "配置数量"])

    # 过滤备份
    filtered_backups = backups
    if search_term:
        filtered_backups = [
            backup for backup in backups
            if search_term.lower() in backup.get("test_case", "").lower()
        ]

    # 排序
    if sort_by == "更新时间":
        filtered_backups.sort(key=lambda x: x.get("update_time", ""), reverse=True)
    elif sort_by == "渠道名称":
        filtered_backups.sort(key=lambda x: x.get("test_case", ""))
    elif sort_by == "配置数量":
        filtered_backups.sort(key=lambda x: x.get("config_count", 0), reverse=True)

    if not filtered_backups:
        st.warning("🔍 没有找到匹配的备份")
        return

    # 创建表格数据 - 序号在前面，其他信息按重要性排列
    table_data = []
    for i, backup in enumerate(filtered_backups):
        table_data.append({
            "序号": i + 1,
            "渠道名称": backup.get("test_case", ""),
            "配置项数量": backup.get("config_count", 0),
            "备份时间": backup.get("backup_time", "").split(".")[0] if backup.get("backup_time") else "",
            "备份描述": backup.get("description", "")
        })

    # 显示表格
    st.subheader("📋 备份列表 (点击行查看JSON)")
    df = pd.DataFrame(table_data)

    # 使用streamlit的数据编辑器显示表格，支持行选择
    event = st.dataframe(
        df,
        use_container_width=True,
        hide_index=True,
        column_config={
            "序号": st.column_config.NumberColumn("序号", width="small"),
            "渠道名称": st.column_config.TextColumn("🔗 渠道名称", width="medium"),
            "配置项数量": st.column_config.NumberColumn("⚙️ 配置项", width="small"),
            "备份时间": st.column_config.TextColumn("📅 备份时间", width="medium"),
            "备份描述": st.column_config.TextColumn("📝 描述", width="large")
        },
        on_select="rerun",
        selection_mode="single-row"
    )

    # 处理行选择事件
    if event.selection.rows:
        selected_row = event.selection.rows[0]
        selected_backup = filtered_backups[selected_row]
        selected_channel = selected_backup.get("test_case", "")

        if selected_channel:
            st.markdown("---")
            view_backup_json(selected_channel)


def view_backup_json(test_case: str):
    """查看备份的JSON结构"""
    backup_data = mock_backup_db.get_channel_backup(test_case)

    if backup_data:

        # 备份信息
        backup_info = backup_data.get("_backup_info", {})
        col1, col2, col3 = st.columns(3)
        with col1:
            st.markdown(f"**📅 备份时间**: {backup_info.get('backup_time', '未知')}")
        with col2:
            st.markdown(f"**⚙️ 配置项数量**: {backup_info.get('config_count', 0)}")
        with col3:
            st.markdown(f"**📝 描述**: {backup_info.get('description', '无描述')}")

        st.markdown("---")

        # 移除备份信息，只显示原始数据
        display_data = {
            "testCase": backup_data.get("testCase", ""),
            "mockConfigList": backup_data.get("mockConfigList", [])
        }

        # 显示完整JSON结构
        formatted_json = json.dumps(display_data, ensure_ascii=False, indent=2)
        st.code(formatted_json, language="json", line_numbers=True)

    else:
        st.error("❌ 获取备份数据失败")





if __name__ == "__main__":
    render()
