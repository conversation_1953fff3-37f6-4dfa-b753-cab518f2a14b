#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/21
# <AUTHOR> Kai


"""
假身份数据生成器
"""

import streamlit as st
import pandas as pd
import random
import json
from datetime import datetime, timedelta
import re

# 假数据库
SURNAMES = ["王", "李", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴", "徐", "孙", "胡", "朱", "高", "林", "何", "郭", "马", "罗", "梁", "宋", "郑", "谢", "韩", "唐", "冯", "于", "董", "萧", "程", "曹", "袁", "邓", "许", "傅", "沈", "曾", "彭", "吕"]
MALE_NAMES = ["伟", "强", "磊", "军", "勇", "涛", "明", "超", "亮", "华", "建", "国", "峰", "辉", "鹏", "志", "杰", "宇", "文", "斌", "俊", "凯", "浩", "博", "威", "龙", "东", "刚", "庆", "飞"]
FEMALE_NAMES = ["芳", "娜", "敏", "静", "丽", "强", "洁", "莉", "萍", "红", "娟", "秀", "慧", "巧", "美", "雅", "素", "真", "环", "雪", "荣", "爱", "妹", "霞", "香", "月", "莺", "媛", "艳", "瑞"]

PROVINCES = {
    "北京": "110000", "天津": "120000", "河北": "130000", "山西": "140000", "内蒙古": "150000",
    "辽宁": "210000", "吉林": "220000", "黑龙江": "230000", "上海": "310000", "江苏": "320000",
    "浙江": "330000", "安徽": "340000", "福建": "350000", "江西": "360000", "山东": "370000",
    "河南": "410000", "湖北": "420000", "湖南": "430000", "广东": "440000", "广西": "450000",
    "海南": "460000", "重庆": "500000", "四川": "510000", "贵州": "520000", "云南": "530000",
    "西藏": "540000", "陕西": "610000", "甘肃": "620000", "青海": "630000", "宁夏": "640000",
    "新疆": "650000"
}

CITIES = ["市区", "县城", "开发区", "新区", "高新区"]
STREETS = ["中山路", "人民路", "解放路", "建设路", "文化路", "学府路", "工业路", "商业街", "步行街", "金融街"]
BUILDINGS = ["小区", "花园", "广场", "大厦", "公寓", "别墅", "社区", "家园", "城", "苑"]

# 银行卡信息
BANKS = {
    "工商银行": {"code": "102", "prefixes": ["6222", "6212", "9558", "6215"]},
    "农业银行": {"code": "103", "prefixes": ["6228", "9559", "6216", "6230"]},
    "中国银行": {"code": "104", "prefixes": ["6013", "6217", "4563", "6200"]},
    "建设银行": {"code": "105", "prefixes": ["6227", "4367", "6236", "6214"]},
    "交通银行": {"code": "301", "prefixes": ["6222", "4058", "6218", "6229"]},
    "招商银行": {"code": "308", "prefixes": ["6225", "4392", "6226", "6259"]},
    "浦发银行": {"code": "310", "prefixes": ["6221", "4568", "6235", "6232"]},
    "民生银行": {"code": "305", "prefixes": ["6226", "4568", "6288", "6231"]},
    "兴业银行": {"code": "309", "prefixes": ["6222", "4864", "6229", "6238"]},
    "平安银行": {"code": "307", "prefixes": ["6222", "4568", "6234", "6239"]},
    "中信银行": {"code": "302", "prefixes": ["6222", "4392", "6228", "6237"]},
    "光大银行": {"code": "303", "prefixes": ["6222", "4568", "6282", "6233"]}
}

def generate_name(gender="random"):
    """生成姓名"""
    surname = random.choice(SURNAMES)
    if gender == "random":
        gender = random.choice(["male", "female"])
    
    if gender == "male":
        name = random.choice(MALE_NAMES)
        if random.random() > 0.7:  # 30%概率生成两字名
            name += random.choice(MALE_NAMES)
    else:
        name = random.choice(FEMALE_NAMES)
        if random.random() > 0.7:
            name += random.choice(FEMALE_NAMES)
    
    return surname + name, gender

def generate_id_card(gender="random", age_range=(18, 65)):
    """生成身份证号"""
    # 随机选择省份
    province_code = random.choice(list(PROVINCES.values()))
    
    # 生成市县代码
    city_code = f"{random.randint(1, 99):02d}"
    district_code = f"{random.randint(1, 99):02d}"
    
    # 生成出生日期
    start_date = datetime.now() - timedelta(days=age_range[1]*365)
    end_date = datetime.now() - timedelta(days=age_range[0]*365)
    birth_date = start_date + timedelta(days=random.randint(0, (end_date - start_date).days))
    birth_str = birth_date.strftime("%Y%m%d")
    
    # 生成顺序码
    sequence = f"{random.randint(1, 999):03d}"
    
    # 性别码（奇数男，偶数女）
    if gender == "male":
        sequence = sequence[:-1] + str(random.choice([1, 3, 5, 7, 9]))
    elif gender == "female":
        sequence = sequence[:-1] + str(random.choice([0, 2, 4, 6, 8]))
    
    # 前17位
    id_17 = province_code[:2] + city_code + district_code + birth_str + sequence
    
    # 计算校验码
    weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
    
    sum_val = sum(int(id_17[i]) * weights[i] for i in range(17))
    check_code = check_codes[sum_val % 11]
    
    return id_17 + check_code

def generate_phone():
    """生成手机号"""
    prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                '150', '151', '152', '153', '155', '156', '157', '158', '159',
                '180', '181', '182', '183', '184', '185', '186', '187', '188', '189']
    prefix = random.choice(prefixes)
    suffix = f"{random.randint(10000000, 99999999)}"
    return prefix + suffix

def generate_address():
    """生成地址"""
    province = random.choice(list(PROVINCES.keys()))
    city = random.choice(CITIES)
    street = random.choice(STREETS)
    building = random.choice(BUILDINGS)
    
    street_num = random.randint(1, 999)
    building_num = random.randint(1, 50)
    unit_num = random.randint(1, 6)
    room_num = random.randint(101, 2999)
    
    return f"{province}{city}{street}{street_num}号{building_num}{building}{unit_num}单元{room_num}室"

def generate_email(name):
    """生成邮箱"""
    domains = ["qq.com", "163.com", "126.com", "gmail.com", "sina.com", "sohu.com"]
    domain = random.choice(domains)

    # 使用拼音或数字
    prefixes = [
        "".join([chr(ord('a') + random.randint(0, 25)) for _ in range(random.randint(6, 12))]),
        f"user{random.randint(1000, 9999)}",
        f"{name.lower()}{random.randint(10, 99)}"
    ]
    prefix = random.choice(prefixes)

    return f"{prefix}@{domain}"

def luhn_checksum(card_num):
    """计算Luhn算法校验码"""
    def digits_of(n):
        return [int(d) for d in str(n)]

    digits = digits_of(card_num)
    odd_digits = digits[-1::-2]
    even_digits = digits[-2::-2]
    checksum = sum(odd_digits)
    for d in even_digits:
        checksum += sum(digits_of(d*2))
    return checksum % 10

def generate_bank_card(bank_name=None):
    """生成银行卡号"""
    if bank_name is None:
        bank_name = random.choice(list(BANKS.keys()))

    bank_info = BANKS[bank_name]
    prefix = random.choice(bank_info["prefixes"])

    # 生成卡号主体部分（不包括校验位）
    if len(prefix) == 4:
        # 16位卡号
        card_body = prefix + "".join([str(random.randint(0, 9)) for _ in range(11)])
    else:
        # 19位卡号
        card_body = prefix + "".join([str(random.randint(0, 9)) for _ in range(14)])

    # 计算校验位
    temp_card = card_body + "0"
    checksum = luhn_checksum(int(temp_card))
    check_digit = (10 - checksum) % 10

    card_number = card_body + str(check_digit)

    # 格式化显示（每4位一组）
    formatted_card = " ".join([card_number[i:i+4] for i in range(0, len(card_number), 4)])

    return {
        "bank_name": bank_name,
        "card_number": card_number,
        "formatted_card": formatted_card,
        "bank_code": bank_info["code"]
    }

def generate_fake_identity(count=1, gender="random", age_range=(18, 65), include_bank_card=True):
    """生成假身份数据"""
    identities = []

    for _ in range(count):
        name, actual_gender = generate_name(gender)
        id_card = generate_id_card(actual_gender, age_range)
        phone = generate_phone()
        address = generate_address()
        email = generate_email(name)

        # 从身份证提取年龄
        birth_year = int(id_card[6:10])
        age = datetime.now().year - birth_year

        identity = {
            "姓名": name,
            "性别": "男" if actual_gender == "male" else "女",
            "年龄": age,
            "身份证": id_card,
            "手机号": phone,
            "邮箱": email
        }

        # 添加银行卡信息
        if include_bank_card:
            bank_card = generate_bank_card()
            identity.update({
                "银行名称": bank_card["bank_name"],
                "银行卡号": bank_card["formatted_card"]
            })

        # 地址放在最后
        identity["地址"] = address

        identities.append(identity)

    return identities

def render():
    """渲染假身份数据页面"""
    st.title("🎭 假身份数据生成器")
    
    # 生成参数配置
    st.markdown("### ⚙️ 生成配置")
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        count = st.number_input("生成数量", min_value=1, max_value=1000, value=10)

    with col2:
        gender = st.selectbox("性别", ["随机", "男", "女"])
        gender_map = {"随机": "random", "男": "male", "女": "female"}
        gender_code = gender_map[gender]

    with col3:
        min_age = st.number_input("最小年龄", min_value=1, max_value=100, value=18)

    with col4:
        max_age = st.number_input("最大年龄", min_value=1, max_value=100, value=65)
    
    # 生成按钮
    col_btn1, col_btn2, col_btn3 = st.columns([1, 1, 2])
    with col_btn1:
        if st.button("🎲 生成数据", type="primary", use_container_width=True):
            if min_age > max_age:
                st.error("最小年龄不能大于最大年龄")
            else:
                with st.spinner("正在生成假身份数据..."):
                    identities = generate_fake_identity(count, gender_code, (min_age, max_age), True)
                    st.session_state.fake_identities = identities
                st.success(f"✅ 成功生成 {len(identities)} 条假身份数据")
    
    with col_btn2:
        if st.button("🗑️ 清空数据", use_container_width=True):
            if 'fake_identities' in st.session_state:
                del st.session_state.fake_identities
            st.success("✅ 数据已清空")
    
    # 显示生成的数据
    if 'fake_identities' in st.session_state and st.session_state.fake_identities:
        identities = st.session_state.fake_identities
        
        st.markdown("### 📋 生成的假身份数据")
        
        # 数据统计
        col_stat1, col_stat2, col_stat3, col_stat4 = st.columns(4)
        with col_stat1:
            st.metric("总数量", len(identities))
        with col_stat2:
            male_count = sum(1 for item in identities if item["性别"] == "男")
            st.metric("男性", male_count)
        with col_stat3:
            female_count = len(identities) - male_count
            st.metric("女性", female_count)
        with col_stat4:
            avg_age = sum(item["年龄"] for item in identities) / len(identities)
            st.metric("平均年龄", f"{avg_age:.1f}岁")
        
        # 数据表格
        df = pd.DataFrame(identities)
        
        # 配置列显示（地址在最后）
        column_config = {
            "姓名": st.column_config.TextColumn("姓名", width="small"),
            "性别": st.column_config.TextColumn("性别", width="small"),
            "年龄": st.column_config.NumberColumn("年龄", width="small"),
            "身份证": st.column_config.TextColumn("身份证", width="medium"),
            "手机号": st.column_config.TextColumn("手机号", width="medium"),
            "邮箱": st.column_config.TextColumn("邮箱", width="medium"),
            "银行名称": st.column_config.TextColumn("银行名称", width="small"),
            "银行卡号": st.column_config.TextColumn("银行卡号", width="medium"),
            "地址": st.column_config.TextColumn("地址", width="large")
        }

        # 表格显示
        st.dataframe(
            df,
            use_container_width=True,
            hide_index=True,
            column_config=column_config
        )
        
        # 导出功能
        st.markdown("### 📤 数据导出")
        col_export1, col_export2, col_export3 = st.columns(3)
        
        with col_export1:
            # CSV导出
            csv_data = df.to_csv(index=False, encoding='utf-8-sig')
            st.download_button(
                label="📄 导出CSV",
                data=csv_data,
                file_name=f"fake_identities_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv",
                use_container_width=True
            )
        
        with col_export2:
            # JSON导出
            json_data = json.dumps(identities, ensure_ascii=False, indent=2)
            st.download_button(
                label="📋 导出JSON",
                data=json_data,
                file_name=f"fake_identities_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json",
                use_container_width=True
            )
        
        with col_export3:
            # 复制功能
            if st.button("📋 复制到剪贴板", use_container_width=True):
                st.info("💡 请手动复制上方表格数据")
    
    else:
        st.info("💡 点击「生成数据」按钮开始生成假身份数据")
        
        # 功能说明
        with st.expander("📖 功能说明", expanded=False):
            st.markdown("""
            **假身份数据生成器功能：**
            
            🎯 **数据生成**
            - 随机生成真实格式的姓名、身份证、手机号
            - 支持性别筛选和年龄范围设置
            - 自动生成匹配的邮箱和地址信息
            - 可选生成银行卡号（支持12家主流银行）
            
            📊 **数据管理**
            - 表格形式展示生成的数据
            - 实时统计数据概况
            - 支持大批量数据生成（最多1000条）
            
            📤 **数据导出**
            - CSV格式导出，支持Excel打开
            - JSON格式导出，便于程序使用
            - 一键复制功能
            
            ⚠️ **使用说明**
            - 生成的身份证号符合国标格式但为虚假数据
            - 银行卡号通过Luhn算法校验，格式正确但为虚假数据
            - 仅用于测试和开发，请勿用于非法用途
            - 手机号为随机生成，可能与真实号码重复

            🏦 **支持银行**
            - 工商银行、农业银行、中国银行、建设银行
            - 交通银行、招商银行、浦发银行、民生银行
            - 兴业银行、平安银行、中信银行、光大银行
            """)
