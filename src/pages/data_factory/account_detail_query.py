#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
动账查询页面
提供动账流水的查询和展示功能
"""

import streamlit as st
import pandas as pd
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

try:
    from src.database.db_connector import account_query
except ImportError:
    # 如果导入失败，创建一个模拟的查询对象
    class MockAccountQuery:
        def query_account_details(self, uid=None, business_no=None, limit=100):
            return pd.DataFrame()

        def get_statistics(self, uid=None, business_no=None):
            return {
                'total_count': 0,
                'unique_users': 0,
                'unique_orders': 0,
                'total_income': 0,
                'total_expense': 0
            }

        class MockDB:
            def test_connection(self):
                return False

        db = MockDB()

    account_query = MockAccountQuery()
    st.warning("⚠️ 数据库模块未正确加载，请安装必要的依赖：pip install pymysql openpyxl")


def render_search_filters():
    """渲染搜索筛选器"""

    # 创建筛选器布局
    col1, col2, col3 = st.columns([1, 1, 1])
    
    with col1:
        uid = st.text_input(
            "客户号",
            key="uid_filter"
        )
    
    with col2:
        business_no = st.text_input(
            "业务订单号",
            key="business_no_filter"
        )
    
    with col3:
        limit = st.selectbox(
            "查询条数",
            options=[50, 100, 200, 500, 1000],
            index=0,  # 默认50
            key="limit_filter"
        )
    
    # 查询按钮
    cols = st.columns(10)
    
    with cols[9]:
        search_clicked = st.button(
            "🔍 查询",
            type="primary",
            use_container_width=True,
            help="点击执行查询"
        )
    
    return {
        'uid': uid,
        'business_no': business_no,
        'limit': limit,
        'search_clicked': search_clicked
    }


def render_data_table(df):
    """渲染数据表格"""
    if df.empty:
        st.info("📝 没有查询到符合条件的数据")
        return
    
    # 显示记录数
    st.markdown(f"**共查询到 {len(df)} 条记录**")
    
    # 配置表格显示
    column_config = {
        "ID编号": st.column_config.NumberColumn(
            "ID编号",
            help="记录的唯一标识",
            format="%d"
        ),
        "变动金额": st.column_config.NumberColumn(
            "变动金额",
            help="账户变动金额，正数为收入，负数为支出",
            format="%.2f"
        ),
        "当前可用余额": st.column_config.NumberColumn(
            "当前可用余额",
            help="变动后的账户余额",
            format="%.2f"
        ),
        "创建时间": st.column_config.DatetimeColumn(
            "创建时间",
            help="记录创建时间",
            format="YYYY-MM-DD HH:mm:ss"
        ),
        "更新时间": st.column_config.DatetimeColumn(
            "更新时间",
            help="记录更新时间",
            format="YYYY-MM-DD HH:mm:ss"
        )
    }
    
    # 显示数据表格
    st.dataframe(
        df,
        column_config=column_config,
        use_container_width=True,
        hide_index=True,
        height=400
    )
    

def render():
    """主渲染函数"""
    st.title("💰 动账查询")

    # 渲染搜索筛选器
    filters = render_search_filters()
    
    # 初始化session state
    if 'query_executed' not in st.session_state:
        st.session_state.query_executed = False
    if 'query_results' not in st.session_state:
        st.session_state.query_results = pd.DataFrame()
    if 'query_stats' not in st.session_state:
        st.session_state.query_stats = {}
    
    # 执行查询
    if filters['search_clicked']:
        with st.spinner("正在查询数据..."):
            try:
                # 查询数据
                df = account_query.query_account_details(
                    uid=filters['uid'] if filters['uid'] else None,
                    business_no=filters['business_no'] if filters['business_no'] else None,
                    limit=filters['limit']
                )
                
                # 获取统计信息
                stats = account_query.get_statistics(
                    uid=filters['uid'] if filters['uid'] else None,
                    business_no=filters['business_no'] if filters['business_no'] else None
                )
                
                # 保存到session state
                st.session_state.query_results = df
                st.session_state.query_stats = stats
                st.session_state.query_executed = True
                
                # 显示结果
                if not df.empty:
                    render_data_table(df)
                else:
                    st.info("📝 没有查询到符合条件的数据")
                    
            except Exception as e:
                st.error(f"查询失败: {str(e)}")
                st.session_state.query_executed = False


if __name__ == "__main__":
    render()
