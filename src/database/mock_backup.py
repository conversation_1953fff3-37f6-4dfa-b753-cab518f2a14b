#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/21
# <AUTHOR> Kai


"""
Mock配置备份数据库模块
用于保存和恢复渠道的原始响应数据
"""

import sqlite3
import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Any


class MockBackupDB:
    """Mock配置备份数据库管理类"""
    
    def __init__(self, db_path: str = "data/mock_backup.db"):
        """初始化数据库连接"""
        self.db_path = db_path
        
        # 确保数据目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # 初始化数据库
        self._init_database()
    
    def _init_database(self):
        """初始化数据库表结构"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建渠道备份表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS channel_backup (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    test_case TEXT NOT NULL UNIQUE,
                    original_data TEXT NOT NULL,
                    backup_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    description TEXT,
                    config_count INTEGER DEFAULT 0
                )
            ''')
            
            # 创建配置项备份表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS config_backup (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    test_case TEXT NOT NULL,
                    config_index INTEGER NOT NULL,
                    api_url TEXT,
                    description TEXT,
                    mock_rule TEXT,
                    mock_body TEXT NOT NULL,
                    condition_rule TEXT,
                    create_time TEXT,
                    backup_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (test_case) REFERENCES channel_backup (test_case)
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_case ON channel_backup (test_case)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_config_test_case ON config_backup (test_case)')
            
            conn.commit()
    
    def save_channel_backup(self, test_case: str, channel_data: Dict[str, Any], description: str = "") -> bool:
        """
        保存渠道的完整备份数据
        
        Args:
            test_case: 渠道名称
            channel_data: 渠道的完整数据
            description: 备份描述
            
        Returns:
            bool: 保存是否成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 提取配置列表
                mock_config_list = channel_data.get("mockConfigList", [])
                config_count = len(mock_config_list)
                
                # 保存渠道主数据
                cursor.execute('''
                    INSERT OR REPLACE INTO channel_backup 
                    (test_case, original_data, description, config_count, update_time)
                    VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (
                    test_case,
                    json.dumps(channel_data, ensure_ascii=False),
                    description,
                    config_count
                ))
                
                # 删除旧的配置项备份
                cursor.execute('DELETE FROM config_backup WHERE test_case = ?', (test_case,))
                
                # 保存每个配置项
                for index, config in enumerate(mock_config_list):
                    cursor.execute('''
                        INSERT INTO config_backup 
                        (test_case, config_index, api_url, description, mock_rule, mock_body, condition_rule, create_time)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        test_case,
                        index,
                        config.get("apiUrl", ""),
                        config.get("description", ""),
                        json.dumps(config.get("mockRule")) if config.get("mockRule") is not None else None,
                        json.dumps(config.get("mockBody"), ensure_ascii=False) if config.get("mockBody") else "",
                        config.get("condition", ""),
                        config.get("createTime", "")
                    ))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"保存渠道备份失败: {e}")
            return False
    
    def get_channel_backup(self, test_case: str) -> Optional[Dict[str, Any]]:
        """
        获取渠道的备份数据
        
        Args:
            test_case: 渠道名称
            
        Returns:
            Dict: 渠道的完整数据，如果不存在返回None
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 查询渠道主数据
                cursor.execute('''
                    SELECT original_data, backup_time, description, config_count
                    FROM channel_backup 
                    WHERE test_case = ?
                ''', (test_case,))
                
                result = cursor.fetchone()
                if not result:
                    return None
                
                original_data, backup_time, description, config_count = result
                
                # 解析JSON数据
                channel_data = json.loads(original_data)
                
                # 添加备份信息
                channel_data["_backup_info"] = {
                    "backup_time": backup_time,
                    "description": description,
                    "config_count": config_count
                }
                
                return channel_data
                
        except Exception as e:
            print(f"获取渠道备份失败: {e}")
            return None
    
    def list_all_backups(self) -> List[Dict[str, Any]]:
        """
        列出所有备份的渠道
        
        Returns:
            List: 所有备份渠道的信息列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT test_case, backup_time, update_time, description, config_count
                    FROM channel_backup 
                    ORDER BY update_time DESC
                ''')
                
                results = cursor.fetchall()
                
                backups = []
                for test_case, backup_time, update_time, description, config_count in results:
                    backups.append({
                        "test_case": test_case,
                        "backup_time": backup_time,
                        "update_time": update_time,
                        "description": description or "",
                        "config_count": config_count
                    })
                
                return backups
                
        except Exception as e:
            print(f"列出备份失败: {e}")
            return []
    
    def delete_channel_backup(self, test_case: str) -> bool:
        """
        删除渠道的备份数据
        
        Args:
            test_case: 渠道名称
            
        Returns:
            bool: 删除是否成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 删除配置项备份
                cursor.execute('DELETE FROM config_backup WHERE test_case = ?', (test_case,))
                
                # 删除渠道主数据
                cursor.execute('DELETE FROM channel_backup WHERE test_case = ?', (test_case,))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"删除渠道备份失败: {e}")
            return False
    
    def backup_exists(self, test_case: str) -> bool:
        """
        检查渠道是否已有备份
        
        Args:
            test_case: 渠道名称
            
        Returns:
            bool: 是否存在备份
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('SELECT 1 FROM channel_backup WHERE test_case = ?', (test_case,))
                return cursor.fetchone() is not None
                
        except Exception as e:
            print(f"检查备份存在性失败: {e}")
            return False


# 全局数据库实例
mock_backup_db = MockBackupDB()
