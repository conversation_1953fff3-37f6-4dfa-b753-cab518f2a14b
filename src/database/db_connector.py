#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
数据库连接器模块
提供通用的数据库查询功能
"""

import pymysql
import pandas as pd
from typing import Dict, List, Any, Optional
import streamlit as st
from contextlib import contextmanager
import logging
import sys
import os

# 添加配置路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

try:
    from src.config.database_config import DATABASE_CONFIG
except ImportError:
    # 默认配置
    DATABASE_CONFIG = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '',
        'database': 'epay_cloud',
        'charset': 'utf8mb4',
        'autocommit': True
    }

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatabaseConnector:
    """数据库连接器类"""
    
    def __init__(self):
        """初始化数据库连接配置"""
        self.config = DATABASE_CONFIG.copy()
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        connection = None
        try:
            connection = pymysql.connect(**self.config)
            yield connection
        except Exception as e:
            logger.error(f"数据库连接错误: {e}")
            if connection:
                connection.rollback()
            raise
        finally:
            if connection:
                connection.close()
    
    def execute_query(self, sql: str, params: Optional[tuple] = None) -> pd.DataFrame:
        """
        执行查询SQL并返回DataFrame
        
        Args:
            sql: SQL查询语句
            params: 查询参数
            
        Returns:
            pd.DataFrame: 查询结果
        """
        try:
            with self.get_connection() as conn:
                df = pd.read_sql(sql, conn, params=params)
                return df
        except Exception as e:
            logger.error(f"查询执行错误: {e}")
            st.error(f"数据库查询失败: {str(e)}")
            return pd.DataFrame()
    
    def execute_update(self, sql: str, params: Optional[tuple] = None) -> int:
        """
        执行更新SQL
        
        Args:
            sql: SQL更新语句
            params: 更新参数
            
        Returns:
            int: 影响的行数
        """
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    affected_rows = cursor.execute(sql, params)
                    conn.commit()
                    return affected_rows
        except Exception as e:
            logger.error(f"更新执行错误: {e}")
            st.error(f"数据库更新失败: {str(e)}")
            return 0
    
    def test_connection(self) -> bool:
        """
        测试数据库连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    return True
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            return False


class AccountDetailQuery:
    """动账查询专用类"""
    
    def __init__(self):
        self.db = DatabaseConnector()
        
        # 字段中文映射
        self.field_mapping = {
            'id': 'ID编号',
            'account_type': '账户类型',
            'uid': '用户ID编号',
            'account_no': '账户编号',
            'business_code': '业务编码',
            'business_no': '业务订单号',
            'sub_business_no': '子业务订单号',
            'currency': '币种',
            'amount': '变动金额',
            'balance': '当前可用余额',
            'category': '账户类别',
            'fee_flag': '手续费标识',
            'voucher_flag': '是否生成凭证',
            'create_time': '创建时间',
            'update_time': '更新时间'
        }
        
        # 枚举值中文映射
        self.enum_mapping = {
            'account_type': {
                1: 'Epay账户',
                2: 'VA账户'
            },
            'category': {
                1: '客户现金账户',
                2: '客户在途账户',
                3: '客户冻结账户',
                4: '客户保证金账户',
                5: '客户授信账户',
                11: '平台现金账户',
                12: '平台在途账户',
                13: '平台冻结账户',
                14: '平台保证金账户',
                15: '平台授信账户',
                16: '平台汇兑损益账户',
                20: '平台手续费账户',
                30: '渠道备付金账户',
                31: '渠道在途账户',
                32: '渠道手续费账户'
            },
            'fee_flag': {
                0: '交易金额',
                1: '交易手续费',
                2: '渠道手续费'
            },
            'voucher_flag': {
                0: '否',
                1: '是'
            }
        }
    
    def query_account_details(self, uid: Optional[str] = None, business_no: Optional[str] = None, 
                            limit: int = 100) -> pd.DataFrame:
        """
        查询动账流水
        
        Args:
            uid: 客户号（用户ID）
            business_no: 业务订单号
            limit: 查询限制条数
            
        Returns:
            pd.DataFrame: 查询结果
        """
        # 基础SQL，排除version字段
        base_sql = """
        SELECT 
            id, account_type, uid, account_no, business_code, business_no, 
            sub_business_no, currency, amount, balance, category, fee_flag, 
            voucher_flag, create_time, update_time
        FROM epay_cloud.fms_account_detail
        WHERE 1=1
        """
        
        params = []
        
        # 添加筛选条件
        if uid and uid.strip():
            base_sql += " AND uid = %s"
            params.append(uid.strip())
        
        if business_no and business_no.strip():
            base_sql += " AND business_no = %s"
            params.append(business_no.strip())
        
        # 添加排序和限制
        base_sql += " ORDER BY id ASC LIMIT %s"
        params.append(limit)
        
        # 执行查询
        df = self.db.execute_query(base_sql, tuple(params) if params else None)
        
        if not df.empty:
            # 应用字段映射
            df = self._apply_field_mapping(df)
            # 应用枚举值映射
            df = self._apply_enum_mapping(df)
        
        return df
    
    def _apply_field_mapping(self, df: pd.DataFrame) -> pd.DataFrame:
        """应用字段中文映射"""
        # 重命名列
        df_mapped = df.rename(columns=self.field_mapping)
        return df_mapped
    
    def _apply_enum_mapping(self, df: pd.DataFrame) -> pd.DataFrame:
        """应用枚举值中文映射"""
        df_mapped = df.copy()
        
        # 映射账户类型
        if '账户类型' in df_mapped.columns:
            df_mapped['账户类型'] = df_mapped['账户类型'].map(
                lambda x: self.enum_mapping['account_type'].get(x, str(x))
            )
        
        # 映射账户类别
        if '账户类别' in df_mapped.columns:
            df_mapped['账户类别'] = df_mapped['账户类别'].map(
                lambda x: self.enum_mapping['category'].get(x, str(x))
            )
        
        # 映射手续费标识
        if '手续费标识' in df_mapped.columns:
            df_mapped['手续费标识'] = df_mapped['手续费标识'].map(
                lambda x: self.enum_mapping['fee_flag'].get(x, str(x))
            )
        
        # 映射是否生成凭证
        if '是否生成凭证' in df_mapped.columns:
            df_mapped['是否生成凭证'] = df_mapped['是否生成凭证'].map(
                lambda x: self.enum_mapping['voucher_flag'].get(x, str(x))
            )
        
        return df_mapped
    
    def get_statistics(self, uid: Optional[str] = None, business_no: Optional[str] = None) -> Dict[str, Any]:
        """
        获取统计信息
        
        Args:
            uid: 客户号
            business_no: 业务订单号
            
        Returns:
            Dict: 统计信息
        """
        base_sql = """
        SELECT 
            COUNT(*) as total_count,
            COUNT(DISTINCT uid) as unique_users,
            COUNT(DISTINCT business_no) as unique_orders,
            SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as total_income,
            SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as total_expense
        FROM epay_cloud.fms_account_detail
        WHERE 1=1
        """
        
        params = []
        
        if uid and uid.strip():
            base_sql += " AND uid = %s"
            params.append(uid.strip())
        
        if business_no and business_no.strip():
            base_sql += " AND business_no = %s"
            params.append(business_no.strip())
        
        df = self.db.execute_query(base_sql, tuple(params) if params else None)
        
        if not df.empty:
            return df.iloc[0].to_dict()
        else:
            return {
                'total_count': 0,
                'unique_users': 0,
                'unique_orders': 0,
                'total_income': 0,
                'total_expense': 0
            }


# 全局实例
db_connector = DatabaseConnector()
account_query = AccountDetailQuery()
