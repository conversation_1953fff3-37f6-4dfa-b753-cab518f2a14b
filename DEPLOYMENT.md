# EpayTools 部署文档

## 📋 项目概述

EpayTools 是一个基于 Streamlit 的多功能工具平台，提供 Mock 服务管理、数据工厂、加密解密、金额计算等实用工具。

## 🛠️ 系统要求

### 基础环境
- **Python**: 3.8 或更高版本
- **操作系统**: Windows / macOS / Linux
- **内存**: 最低 2GB，推荐 4GB+
- **磁盘空间**: 最低 1GB

### 依赖服务
- **Mock服务**: 需要外部 Mock API 服务 (http://web.epaydev.xyz)
- **数据库**: SQLite (自动创建)

## 📦 安装部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd EpayTools
```

### 2. 创建虚拟环境
```bash
# 创建虚拟环境
python -m venv venvs

# 激活虚拟环境
# Windows
venvs\Scripts\activate
# macOS/Linux
source venvs/bin/activate
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

### 4. 配置环境
```bash
# 创建必要的目录
mkdir -p data/backups
mkdir -p logs

# 设置权限 (Linux/macOS)
chmod 755 data
chmod 755 logs
```

## 🚀 启动服务

### 开发环境启动
```bash
# 激活虚拟环境
source venvs/bin/activate  # macOS/Linux
# 或
venvs\Scripts\activate     # Windows

# 启动主应用
streamlit run main.py --server.port 8501

# 启动Mock服务 (可选)
python mock_server.py
```

### 生产环境启动
```bash
# 使用 nohup 后台运行
nohup streamlit run main.py --server.port 8501 --server.address 0.0.0.0 > logs/app.log 2>&1 &

# 或使用 systemd 服务 (Linux)
sudo systemctl start epaytools
sudo systemctl enable epaytools
```

## 🔧 配置说明

### 主要配置文件
- `src/config/menu_config.py` - 菜单配置
- `main.py` - 主应用入口
- `mock_server.py` - Mock服务配置

### 环境变量
```bash
# 可选环境变量
export STREAMLIT_SERVER_PORT=8501
export STREAMLIT_SERVER_ADDRESS=0.0.0.0
export MOCK_SERVER_PORT=8001
```

### 数据库配置
- **类型**: SQLite
- **位置**: `data/mock_backup.db`
- **自动创建**: 首次运行时自动创建表结构

## 🌐 网络配置

### 端口说明
- **8501**: Streamlit 主应用端口
- **8001**: Mock 服务端口 (可选)

### 防火墙设置
```bash
# 开放端口 (Linux)
sudo ufw allow 8501
sudo ufw allow 8001

# 或使用 iptables
sudo iptables -A INPUT -p tcp --dport 8501 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 8001 -j ACCEPT
```

### 反向代理 (Nginx)
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:8501;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 🐳 Docker 部署

### Dockerfile
```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8501

CMD ["streamlit", "run", "main.py", "--server.port=8501", "--server.address=0.0.0.0"]
```

### docker-compose.yml
```yaml
version: '3.8'

services:
  epaytools:
    build: .
    ports:
      - "8501:8501"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - STREAMLIT_SERVER_PORT=8501
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
    restart: unless-stopped
```

### 构建和运行
```bash
# 构建镜像
docker build -t epaytools .

# 运行容器
docker run -d -p 8501:8501 -v $(pwd)/data:/app/data epaytools

# 或使用 docker-compose
docker-compose up -d
```

## 📊 监控和日志

### 日志配置
- **应用日志**: `logs/app.log`
- **Mock服务日志**: `logs/mock.log`
- **错误日志**: `logs/error.log`

### 健康检查
```bash
# 检查服务状态
curl http://localhost:8501/health

# 检查Mock服务
curl http://localhost:8001/health
```

### 监控脚本
```bash
#!/bin/bash
# monitor.sh
while true; do
    if ! curl -f http://localhost:8501 > /dev/null 2>&1; then
        echo "$(date): Service is down, restarting..."
        systemctl restart epaytools
    fi
    sleep 60
done
```

## 🔒 安全配置

### 访问控制
```python
# 在 main.py 中添加认证
import streamlit_authenticator as stauth

# 配置用户认证
authenticator = stauth.Authenticate(
    credentials,
    'epaytools',
    'auth_key',
    cookie_expiry_days=30
)
```

### HTTPS 配置
```bash
# 使用 Let's Encrypt
sudo certbot --nginx -d your-domain.com

# 或使用自签名证书
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365
```

## 🚨 故障排除

### 常见问题

1. **端口被占用**
```bash
# 查找占用端口的进程
lsof -i :8501
# 杀死进程
kill -9 <PID>
```

2. **依赖安装失败**
```bash
# 升级 pip
pip install --upgrade pip
# 清理缓存
pip cache purge
```

3. **数据库权限问题**
```bash
# 修改数据目录权限
chmod 755 data/
chmod 666 data/mock_backup.db
```

4. **Mock服务连接失败**
- 检查外部API服务状态
- 验证网络连接
- 确认API接口地址正确

### 性能优化

1. **内存优化**
```python
# 在 main.py 中添加
import streamlit as st
st.set_page_config(
    page_title="EpayTools",
    layout="wide",
    initial_sidebar_state="expanded"
)
```

2. **缓存配置**
```python
# 使用 Streamlit 缓存
@st.cache_data
def load_data():
    # 数据加载逻辑
    pass
```

## 📋 维护指南

### 定期维护
- **日志清理**: 定期清理过大的日志文件
- **数据备份**: 定期备份 SQLite 数据库
- **依赖更新**: 定期更新 Python 依赖包

### 备份策略
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf backup_${DATE}.tar.gz data/ logs/
```

### 更新部署
```bash
# 拉取最新代码
git pull origin main

# 更新依赖
pip install -r requirements.txt

# 重启服务
systemctl restart epaytools
```

## 📞 技术支持

- **项目地址**: [GitHub Repository]
- **问题反馈**: [Issues]
- **技术文档**: [Wiki]
- **联系方式**: 深圳易派支付科技有限公司

---

**© 2025 深圳易派支付科技有限公司**
