#!/bin/bash

# EpayTools 快速部署脚本
# 适用于 Linux/macOS 系统

set -e

echo "🚀 EpayTools 快速部署脚本"
echo "================================"

# 检查 Python 版本
check_python() {
    echo "📋 检查 Python 环境..."
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        echo "✅ Python 版本: $PYTHON_VERSION"
        
        # 检查版本是否 >= 3.8
        if python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)"; then
            echo "✅ Python 版本满足要求 (>= 3.8)"
        else
            echo "❌ Python 版本过低，需要 3.8 或更高版本"
            exit 1
        fi
    else
        echo "❌ 未找到 Python3，请先安装 Python 3.8+"
        exit 1
    fi
}

# 创建虚拟环境
create_venv() {
    echo "📦 创建虚拟环境..."
    if [ ! -d "venvs" ]; then
        python3 -m venv venvs
        echo "✅ 虚拟环境创建成功"
    else
        echo "✅ 虚拟环境已存在"
    fi
}

# 激活虚拟环境并安装依赖
install_deps() {
    echo "📥 安装项目依赖..."
    source venvs/bin/activate
    
    # 升级 pip
    pip install --upgrade pip
    
    # 安装依赖
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
        echo "✅ 依赖安装完成"
    else
        echo "⚠️  requirements.txt 不存在，手动安装核心依赖..."
        pip install streamlit pandas requests sqlite3
    fi
}

# 创建必要目录
create_dirs() {
    echo "📁 创建必要目录..."
    mkdir -p data/backups
    mkdir -p logs
    chmod 755 data logs
    echo "✅ 目录创建完成"
}

# 检查端口占用
check_ports() {
    echo "🔍 检查端口占用..."
    
    if lsof -i :8501 &> /dev/null; then
        echo "⚠️  端口 8501 已被占用"
        read -p "是否要杀死占用进程? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            lsof -ti :8501 | xargs kill -9
            echo "✅ 已清理端口 8501"
        fi
    else
        echo "✅ 端口 8501 可用"
    fi
}

# 启动服务
start_service() {
    echo "🚀 启动 EpayTools 服务..."
    source venvs/bin/activate
    
    # 后台启动
    nohup streamlit run main.py --server.port 8501 --server.address 0.0.0.0 > logs/app.log 2>&1 &
    
    # 获取进程ID
    PID=$!
    echo $PID > epaytools.pid
    
    echo "✅ 服务启动成功"
    echo "📊 进程ID: $PID"
    echo "🌐 访问地址: http://localhost:8501"
    echo "📋 日志文件: logs/app.log"
    echo "🛑 停止服务: ./stop.sh 或 kill $PID"
}

# 创建停止脚本
create_stop_script() {
    cat > stop.sh << 'EOF'
#!/bin/bash
echo "🛑 停止 EpayTools 服务..."

if [ -f "epaytools.pid" ]; then
    PID=$(cat epaytools.pid)
    if kill -0 $PID 2>/dev/null; then
        kill $PID
        echo "✅ 服务已停止 (PID: $PID)"
        rm epaytools.pid
    else
        echo "⚠️  进程不存在，清理PID文件"
        rm epaytools.pid
    fi
else
    echo "⚠️  PID文件不存在，尝试查找进程..."
    pkill -f "streamlit run main.py"
    echo "✅ 已尝试停止相关进程"
fi
EOF
    chmod +x stop.sh
    echo "✅ 停止脚本创建完成: ./stop.sh"
}

# 创建重启脚本
create_restart_script() {
    cat > restart.sh << 'EOF'
#!/bin/bash
echo "🔄 重启 EpayTools 服务..."
./stop.sh
sleep 2
./deploy.sh --start-only
EOF
    chmod +x restart.sh
    echo "✅ 重启脚本创建完成: ./restart.sh"
}

# 创建状态检查脚本
create_status_script() {
    cat > status.sh << 'EOF'
#!/bin/bash
echo "📊 EpayTools 服务状态"
echo "====================="

if [ -f "epaytools.pid" ]; then
    PID=$(cat epaytools.pid)
    if kill -0 $PID 2>/dev/null; then
        echo "✅ 服务运行中 (PID: $PID)"
        echo "🌐 访问地址: http://localhost:8501"
        
        # 检查端口
        if lsof -i :8501 &> /dev/null; then
            echo "✅ 端口 8501 正常监听"
        else
            echo "⚠️  端口 8501 未监听"
        fi
        
        # 检查HTTP响应
        if curl -f http://localhost:8501 &> /dev/null; then
            echo "✅ HTTP 服务正常"
        else
            echo "⚠️  HTTP 服务异常"
        fi
    else
        echo "❌ 服务未运行 (PID文件存在但进程不存在)"
        rm epaytools.pid
    fi
else
    echo "❌ 服务未运行 (无PID文件)"
fi

echo ""
echo "📋 最近日志:"
if [ -f "logs/app.log" ]; then
    tail -5 logs/app.log
else
    echo "无日志文件"
fi
EOF
    chmod +x status.sh
    echo "✅ 状态检查脚本创建完成: ./status.sh"
}

# 主函数
main() {
    # 检查参数
    if [ "$1" = "--start-only" ]; then
        start_service
        return
    fi
    
    echo "开始部署 EpayTools..."
    echo ""
    
    check_python
    create_venv
    install_deps
    create_dirs
    check_ports
    create_stop_script
    create_restart_script
    create_status_script
    start_service
    
    echo ""
    echo "🎉 部署完成！"
    echo "================================"
    echo "🌐 访问地址: http://localhost:8501"
    echo "📊 查看状态: ./status.sh"
    echo "🛑 停止服务: ./stop.sh"
    echo "🔄 重启服务: ./restart.sh"
    echo "📋 查看日志: tail -f logs/app.log"
    echo ""
    echo "💡 提示: 首次启动可能需要几秒钟时间"
}

# 执行主函数
main "$@"
