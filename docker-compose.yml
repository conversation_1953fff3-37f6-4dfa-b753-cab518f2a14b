# EpayTools Docker Compose 配置
version: '3.8'

services:
  epaytools:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: epaytools
    ports:
      - "8501:8501"
    volumes:
      # 数据持久化
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app
      - STREAMLIT_SERVER_PORT=8501
      - DATABASE_PATH=data/mock_backup.db
      - API_BASE_URL=http://web.epaydev.xyz
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - epaytools-network

  # 可选：添加 Nginx 反向代理
  # nginx:
  #   image: nginx:alpine
  #   container_name: epaytools-nginx
  #   ports:
  #     - "80:80"
  #     - "443:443"
  #   volumes:
  #     - ./nginx.conf:/etc/nginx/nginx.conf:ro
  #     - ./ssl:/etc/nginx/ssl:ro
  #   depends_on:
  #     - epaytools
  #   restart: unless-stopped
  #   networks:
  #     - epaytools-network

networks:
  epaytools-network:
    driver: bridge

volumes:
  epaytools-data:
    driver: local
  epaytools-logs:
    driver: local
