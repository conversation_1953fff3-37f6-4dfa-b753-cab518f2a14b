# EpayTools - 易派支付工具集

专业的支付工具集合平台，为深圳易派支付有限公司提供开发和测试工具。

## 🎯 主要功能

### ⚒️ 常用工具
- **金额计算**: 提现、充值、代付、代收等金额计算
- **汇率转换**: 多币种汇率计算和汇损处理

### 🎁 Mock服务
- **API模拟**: 快速创建Mock接口
- **动态响应**: 自定义状态码、响应头、响应体
- **请求日志**: 记录所有Mock请求

### 🚀 接口自动化
完整的企业级接口测试解决方案，支持全流程自动化测试。

**核心功能**:
- **项目管理**: 测试项目的创建和管理，支持项目分类
- **环境管理**: 多环境配置和切换，支持环境变量
- **数据库管理**: 多数据库支持和数据断言，支持MySQL/PostgreSQL/SQLite等
- **用例管理**: 完整的测试用例CRUD操作，支持复制、编辑、删除
- **用例执行**: 真实HTTP请求执行和监控，支持并行执行
- **执行报告**: 详细的测试报告和数据分析，支持图表展示
- **定时任务**: 支持定时执行和巡检（简化版）
- **通知管理**: 钉钉、企业微信等通知集成（简化版）

**技术特色**:
- ✅ 真实HTTP请求执行，支持所有HTTP方法
- ✅ 多种认证方式：Basic Auth、Bearer Token、API Key
- ✅ 丰富的断言类型：状态码、JSONPath、XPath、正则表达式
- ✅ 变量提取和替换，支持动态参数
- ✅ 数据库集成，支持数据断言和数据准备
- ✅ CI/CD集成，提供完整的RESTful API
- ✅ Jenkins集成脚本和Pipeline配置

### 💹 数据工厂
- **动账查询**: 数据库流水查询和分析
- **假身份数据**: 测试数据批量生成

### 📊 性能测试
- **性能测试**: 基于Locust的负载测试
- **性能报告**: 详细的性能分析报告

## 🚀 快速开始

### 安装依赖
```bash
pip install -r requirements.txt

# 可选：安装数据库驱动（用于接口自动化）
pip install pymysql psycopg2-binary  # MySQL和PostgreSQL支持
```

### 启动应用
```bash
# 启动Web界面
streamlit run app.py

# 启动API服务（用于CI/CD集成）
python start_api_server.py --port 8888
```

### 访问应用
- **Web界面**: http://localhost:8501
- **API服务**: http://localhost:8888
- **API文档**: 查看 `examples/api_documentation.md`

## 📖 使用说明

### 🚀 接口自动化快速上手

#### 1. 创建第一个项目
1. 访问Web界面，点击"🚀接口自动化" → "项目管理"
2. 点击"➕ 添加项目"，填写项目信息
3. 保存项目

#### 2. 配置测试环境
1. 进入"环境管理"页面
2. 点击"➕ 添加环境"
3. 配置环境信息：
   ```
   环境名称: 测试环境
   基础URL: https://api.example.com
   超时时间: 30秒
   ```

#### 3. 创建测试用例
1. 进入"用例管理"页面
2. 点击"➕ 添加用例"
3. 配置用例信息：
   ```
   用例名称: 获取用户信息
   请求方法: GET
   请求URL: /api/v1/users/123
   ```
4. 添加断言：
   ```
   状态码断言: 等于 200
   JSONPath断言: $.code 等于 0
   ```

#### 4. 执行测试
1. 在用例列表中点击"🧪 测试"进行单用例测试
2. 或进入"用例执行"页面进行批量执行
3. 在"执行报告"页面查看结果

#### 5. CI/CD集成
```bash
# Jenkins集成示例
python examples/jenkins_integration.py \
  --api-url http://localhost:8888 \
  --project "示例项目" \
  --environment "测试环境"
```

### Mock服务
1. 导航到 "🎁Mock" -> "Mock管理"
2. 点击"新建接口"创建Mock API
3. 配置路径、方法、响应内容
4. 通过 `http://localhost:8001/mock/your-path` 访问

### 金额计算
1. 导航到 "⚒️常用工具" -> "金额计算"
2. 选择功能标签页
3. 配置参数并计算

## 📄 许可证

© 2025 深圳易派支付有限公司
