#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/21
# <AUTHOR> Kai


"""
Mock服务器启动脚本
"""

import argparse
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.mock.app import run_server


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="EpayTools Mock Server")
    parser.add_argument("--host", default="127.0.0.1", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8001, help="服务器端口")
    parser.add_argument("--reload", action="store_true", help="开启自动重载")
    
    args = parser.parse_args()
    
    print(f"启动Mock服务器...")
    print(f"地址: http://{args.host}:{args.port}")
    print(f"API文档: http://{args.host}:{args.port}/docs")
    print(f"Mock接口前缀: http://{args.host}:{args.port}/mock")
    
    run_server(host=args.host, port=args.port, reload=args.reload)


if __name__ == "__main__":
    main()
