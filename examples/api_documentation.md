# EpayTools API测试服务 - API文档

## 📖 概述

EpayTools API测试服务提供了完整的RESTful API接口，支持CI/CD集成、自动化测试执行和结果查询。

**服务地址**: `http://localhost:8888` (默认)

## 🔍 健康检查

### GET /health

检查API服务状态

**响应示例**:
```json
{
  "status": "ok",
  "timestamp": "2025-07-28T14:30:22.123456",
  "version": "1.0.0"
}
```

## 📁 项目管理

### GET /api/projects

获取所有项目列表

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "project-001",
      "name": "用户管理系统",
      "description": "用户注册、登录、权限管理相关接口",
      "created_at": "2025-07-28T10:00:00.000000",
      "updated_at": "2025-07-28T10:00:00.000000",
      "created_by": "admin"
    }
  ]
}
```

## 🌐 环境管理

### GET /api/environments

获取所有环境配置

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "env-001",
      "name": "测试环境",
      "description": "测试环境配置",
      "base_url": "https://test-api.example.com",
      "variables": {
        "host": "test-api.example.com",
        "version": "v1"
      },
      "headers": [
        {
          "key": "Content-Type",
          "value": "application/json",
          "enabled": true
        }
      ],
      "timeout": 30,
      "is_default": true
    }
  ]
}
```

## 📝 测试用例管理

### GET /api/test-cases

获取测试用例列表

**查询参数**:
- `project_id` (可选): 项目ID，筛选指定项目的用例
- `module_id` (可选): 模块ID，筛选指定模块的用例

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "case-001",
      "name": "用户登录接口测试",
      "description": "测试用户登录功能",
      "project_id": "project-001",
      "module_id": "module-001",
      "tags": ["登录", "核心功能"],
      "priority": "High",
      "status": "Enabled",
      "request": {
        "method": "POST",
        "url": "/api/v1/auth/login",
        "protocol": "HTTPS",
        "headers": [
          {
            "key": "Content-Type",
            "value": "application/json",
            "enabled": true
          }
        ],
        "query_params": [],
        "body_type": "JSON",
        "body_data": "{\"username\": \"test\", \"password\": \"123456\"}",
        "timeout": 30
      },
      "assertions": [
        {
          "id": "assert-001",
          "assertion_type": "Status Code",
          "field_path": "",
          "operator": "equals",
          "expected_value": "200",
          "enabled": true,
          "description": "检查状态码为200"
        }
      ],
      "variable_extractions": [
        {
          "id": "extract-001",
          "variable_name": "access_token",
          "source": "response_body",
          "extraction_method": "json_path",
          "expression": "$.data.token",
          "enabled": true
        }
      ],
      "skip": false,
      "skip_reason": "",
      "created_at": "2025-07-28T10:00:00.000000",
      "updated_at": "2025-07-28T10:00:00.000000"
    }
  ]
}
```

### POST /api/test-cases

创建新的测试用例

**请求体**: 测试用例JSON对象 (参考GET响应格式)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "case-002",
    "name": "新创建的测试用例",
    ...
  }
}
```

### PUT /api/test-cases/{case_id}

更新指定的测试用例

**路径参数**:
- `case_id`: 测试用例ID

**请求体**: 更新的测试用例JSON对象

### DELETE /api/test-cases/{case_id}

删除指定的测试用例

**路径参数**:
- `case_id`: 测试用例ID

## 🚀 测试执行

### POST /api/execute

执行测试用例

**请求体**:
```json
{
  "case_ids": ["case-001", "case-002"],
  "environment_id": "env-001",
  "parallel": false,
  "max_workers": 3,
  "stop_on_failure": false
}
```

**参数说明**:
- `case_ids`: 要执行的测试用例ID列表
- `environment_id` (可选): 执行环境ID
- `parallel` (可选): 是否并行执行，默认false
- `max_workers` (可选): 并行执行时的最大工作线程数，默认3
- `stop_on_failure` (可选): 遇到失败时是否停止执行，默认false

**响应示例**:
```json
{
  "success": true,
  "execution_id": "exec-001",
  "message": "测试执行已启动"
}
```

### GET /api/execution/{execution_id}/status

获取执行状态

**路径参数**:
- `execution_id`: 执行ID

**响应示例**:
```json
{
  "success": true,
  "data": {
    "execution_id": "exec-001",
    "status": "running",
    "start_time": "2025-07-28T14:30:00.000000",
    "end_time": null,
    "total_cases": 5,
    "completed_cases": 3,
    "passed_cases": 2,
    "failed_cases": 1,
    "skipped_cases": 0,
    "error_cases": 0,
    "current_case": "用户登录接口测试",
    "progress": 60.0
  }
}
```

**状态说明**:
- `running`: 执行中
- `completed`: 执行完成
- `failed`: 执行失败
- `stopped`: 已停止

### POST /api/execution/{execution_id}/stop

停止执行

**路径参数**:
- `execution_id`: 执行ID

**响应示例**:
```json
{
  "success": true,
  "message": "执行已停止"
}
```

### GET /api/execution/{execution_id}/report

获取执行报告

**路径参数**:
- `execution_id`: 执行ID

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "report-001",
    "name": "测试执行报告_20250728_143022",
    "description": "",
    "environment_id": "env-001",
    "environment_name": "测试环境",
    "total_cases": 5,
    "passed_cases": 4,
    "failed_cases": 1,
    "skipped_cases": 0,
    "error_cases": 0,
    "start_time": "2025-07-28T14:30:00.000000",
    "end_time": "2025-07-28T14:32:15.000000",
    "total_duration": 135.5,
    "pass_rate": 80.0,
    "case_results": [
      {
        "case_id": "case-001",
        "case_name": "用户登录接口测试",
        "status": "Passed",
        "response_time": 245.6,
        "error_message": "",
        "skip_reason": ""
      }
    ]
  }
}
```

## 📊 执行历史

### GET /api/execution-history

获取执行历史记录

**查询参数**:
- `limit` (可选): 返回记录数量限制，默认50

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "report-001",
      "name": "测试执行报告_20250728_143022",
      "total_cases": 5,
      "passed_cases": 4,
      "failed_cases": 1,
      "pass_rate": 80.0,
      "start_time": "2025-07-28T14:30:00.000000",
      "total_duration": 135.5
    }
  ]
}
```

## 🔧 错误处理

所有API接口在发生错误时都会返回统一的错误格式：

```json
{
  "success": false,
  "error": "错误描述信息"
}
```

**常见HTTP状态码**:
- `200`: 成功
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

## 💡 使用示例

### Python示例

```python
import requests

# 1. 健康检查
response = requests.get("http://localhost:8888/health")
print(response.json())

# 2. 获取项目列表
response = requests.get("http://localhost:8888/api/projects")
projects = response.json()["data"]

# 3. 获取测试用例
project_id = projects[0]["id"]
response = requests.get(f"http://localhost:8888/api/test-cases?project_id={project_id}")
test_cases = response.json()["data"]

# 4. 执行测试用例
case_ids = [case["id"] for case in test_cases]
execute_data = {
    "case_ids": case_ids,
    "parallel": False
}
response = requests.post("http://localhost:8888/api/execute", json=execute_data)
execution_id = response.json()["execution_id"]

# 5. 查询执行状态
response = requests.get(f"http://localhost:8888/api/execution/{execution_id}/status")
status = response.json()["data"]
print(f"执行状态: {status['status']}, 进度: {status['progress']}%")
```

### curl示例

```bash
# 健康检查
curl -X GET http://localhost:8888/health

# 获取项目列表
curl -X GET http://localhost:8888/api/projects

# 执行测试用例
curl -X POST http://localhost:8888/api/execute \
  -H "Content-Type: application/json" \
  -d '{
    "case_ids": ["case-001", "case-002"],
    "environment_id": "env-001",
    "parallel": false
  }'

# 查询执行状态
curl -X GET http://localhost:8888/api/execution/{execution_id}/status
```

## 🔗 Jenkins集成

参考 `examples/jenkins_integration.py` 和 `examples/Jenkinsfile` 文件，了解如何在Jenkins Pipeline中集成API测试服务。

### Jenkins Pipeline示例

```groovy
pipeline {
    agent any
    
    stages {
        stage('API测试') {
            steps {
                script {
                    sh '''
                        python3 examples/jenkins_integration.py \
                          --api-url http://api-test-server:8888 \
                          --project "用户管理系统" \
                          --environment "测试环境" \
                          --tags "冒烟测试,核心功能" \
                          --parallel
                    '''
                }
            }
        }
    }
}
```
