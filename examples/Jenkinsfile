pipeline {
    agent any
    
    parameters {
        choice(
            name: 'ENVIRONMENT',
            choices: ['测试环境', '预生产环境'],
            description: '选择测试环境'
        )
        choice(
            name: 'PROJECT',
            choices: ['用户管理系统', '支付服务', '订单管理'],
            description: '选择测试项目'
        )
        string(
            name: 'TAGS',
            defaultValue: '冒烟测试,核心功能',
            description: '测试标签（用逗号分隔）'
        )
        booleanParam(
            name: 'PARALLEL_EXECUTION',
            defaultValue: false,
            description: '是否并行执行测试用例'
        )
        string(
            name: 'API_TEST_URL',
            defaultValue: 'http://api-test-server:8888',
            description: 'API测试服务地址'
        )
    }
    
    environment {
        // 设置Python环境
        PYTHONPATH = "${WORKSPACE}"
        // API测试服务地址
        API_TEST_SERVER = "${params.API_TEST_URL}"
    }
    
    stages {
        stage('准备环境') {
            steps {
                echo '🔧 准备测试环境...'
                
                // 检查Python环境
                sh 'python3 --version'
                
                // 安装依赖
                sh '''
                    pip3 install requests flask flask-cors
                '''
                
                // 检查API测试服务是否可用
                script {
                    def healthCheck = sh(
                        script: "curl -f ${API_TEST_SERVER}/health || exit 1",
                        returnStatus: true
                    )
                    
                    if (healthCheck != 0) {
                        error("❌ API测试服务不可用: ${API_TEST_SERVER}")
                    }
                    
                    echo "✅ API测试服务正常"
                }
            }
        }
        
        stage('执行API测试') {
            steps {
                echo "🚀 开始执行API测试..."
                echo "📁 项目: ${params.PROJECT}"
                echo "🌐 环境: ${params.ENVIRONMENT}"
                echo "🏷️ 标签: ${params.TAGS}"
                echo "⚡ 并行执行: ${params.PARALLEL_EXECUTION}"
                
                script {
                    // 构建测试命令
                    def testCommand = "python3 examples/jenkins_integration.py"
                    testCommand += " --api-url '${API_TEST_SERVER}'"
                    testCommand += " --project '${params.PROJECT}'"
                    testCommand += " --environment '${params.ENVIRONMENT}'"
                    
                    if (params.TAGS) {
                        def tags = params.TAGS.split(',').collect { it.trim() }.join(' ')
                        testCommand += " --tags ${tags}"
                    }
                    
                    if (params.PARALLEL_EXECUTION) {
                        testCommand += " --parallel"
                    }
                    
                    testCommand += " --timeout 600"
                    
                    echo "执行命令: ${testCommand}"
                    
                    // 执行测试
                    def testResult = sh(
                        script: testCommand,
                        returnStatus: true
                    )
                    
                    // 检查测试结果
                    if (testResult == 0) {
                        echo "🎉 API测试执行成功！"
                        currentBuild.result = 'SUCCESS'
                    } else {
                        echo "❌ API测试执行失败！"
                        currentBuild.result = 'FAILURE'
                        error("API测试失败，退出码: ${testResult}")
                    }
                }
            }
        }
        
        stage('生成测试报告') {
            steps {
                echo '📊 生成测试报告...'
                
                script {
                    // 这里可以调用API获取详细的测试报告
                    // 并生成Jenkins可识别的测试报告格式
                    
                    sh '''
                        echo "测试报告生成完成" > test_report.txt
                        echo "项目: ${PROJECT}" >> test_report.txt
                        echo "环境: ${ENVIRONMENT}" >> test_report.txt
                        echo "执行时间: $(date)" >> test_report.txt
                    '''
                }
            }
            
            post {
                always {
                    // 归档测试报告
                    archiveArtifacts artifacts: 'test_report.txt', fingerprint: true
                }
            }
        }
    }
    
    post {
        always {
            echo '🧹 清理工作空间...'
            
            // 清理临时文件
            sh 'rm -f *.tmp'
        }
        
        success {
            echo '🎉 Pipeline执行成功！'
            
            // 发送成功通知
            script {
                def message = """
                ✅ API测试执行成功
                
                📁 项目: ${params.PROJECT}
                🌐 环境: ${params.ENVIRONMENT}
                🏷️ 标签: ${params.TAGS}
                ⏰ 执行时间: ${new Date()}
                🔗 构建链接: ${BUILD_URL}
                """
                
                // 这里可以发送钉钉、企业微信或邮件通知
                echo message
            }
        }
        
        failure {
            echo '❌ Pipeline执行失败！'
            
            // 发送失败通知
            script {
                def message = """
                ❌ API测试执行失败
                
                📁 项目: ${params.PROJECT}
                🌐 环境: ${params.ENVIRONMENT}
                🏷️ 标签: ${params.TAGS}
                ⏰ 执行时间: ${new Date()}
                🔗 构建链接: ${BUILD_URL}
                📋 查看详细日志: ${BUILD_URL}console
                """
                
                // 这里可以发送钉钉、企业微信或邮件通知
                echo message
            }
        }
        
        unstable {
            echo '⚠️ Pipeline执行不稳定！'
        }
    }
}
