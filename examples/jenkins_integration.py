#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2025/7/28
# <AUTHOR> Kai

"""
Jenkins集成示例脚本
演示如何在CI/CD流水线中调用API测试服务
"""

import requests
import time
import sys
import json
from typing import Dict, List, Any, Optional


class ApiTestClient:
    """API测试客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8888"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
    
    def health_check(self) -> bool:
        """健康检查"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            return response.status_code == 200
        except:
            return False
    
    def get_projects(self) -> List[Dict[str, Any]]:
        """获取项目列表"""
        response = self.session.get(f"{self.base_url}/api/projects")
        response.raise_for_status()
        return response.json()["data"]
    
    def get_environments(self) -> List[Dict[str, Any]]:
        """获取环境列表"""
        response = self.session.get(f"{self.base_url}/api/environments")
        response.raise_for_status()
        return response.json()["data"]
    
    def get_test_cases(self, project_id: str = None) -> List[Dict[str, Any]]:
        """获取测试用例列表"""
        params = {}
        if project_id:
            params["project_id"] = project_id
        
        response = self.session.get(f"{self.base_url}/api/test-cases", params=params)
        response.raise_for_status()
        return response.json()["data"]
    
    def execute_test_cases(self, case_ids: List[str], environment_id: str = None,
                          parallel: bool = False, max_workers: int = 3,
                          stop_on_failure: bool = False) -> str:
        """执行测试用例"""
        data = {
            "case_ids": case_ids,
            "environment_id": environment_id,
            "parallel": parallel,
            "max_workers": max_workers,
            "stop_on_failure": stop_on_failure
        }
        
        response = self.session.post(f"{self.base_url}/api/execute", json=data)
        response.raise_for_status()
        return response.json()["execution_id"]
    
    def get_execution_status(self, execution_id: str) -> Dict[str, Any]:
        """获取执行状态"""
        response = self.session.get(f"{self.base_url}/api/execution/{execution_id}/status")
        response.raise_for_status()
        return response.json()["data"]
    
    def wait_for_completion(self, execution_id: str, timeout: int = 300) -> Dict[str, Any]:
        """等待执行完成"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status = self.get_execution_status(execution_id)
            
            if status["status"] in ["completed", "failed", "stopped"]:
                return status
            
            print(f"⏳ 执行进度: {status.get('progress', 0):.1f}% "
                  f"({status.get('completed_cases', 0)}/{status.get('total_cases', 0)})")
            
            time.sleep(5)
        
        raise TimeoutError(f"执行超时 ({timeout}秒)")
    
    def get_execution_report(self, execution_id: str) -> Dict[str, Any]:
        """获取执行报告"""
        response = self.session.get(f"{self.base_url}/api/execution/{execution_id}/report")
        response.raise_for_status()
        return response.json()["data"]


def run_api_tests_for_jenkins(api_url: str, project_name: str = None, 
                             environment_name: str = None, tags: List[str] = None,
                             parallel: bool = False, timeout: int = 300) -> int:
    """
    为Jenkins运行API测试
    
    Args:
        api_url: API测试服务地址
        project_name: 项目名称（可选）
        environment_name: 环境名称（可选）
        tags: 标签列表（可选）
        parallel: 是否并行执行
        timeout: 超时时间（秒）
    
    Returns:
        退出码：0=成功，1=失败
    """
    client = ApiTestClient(api_url)
    
    try:
        # 1. 健康检查
        print("🔍 检查API测试服务...")
        if not client.health_check():
            print("❌ API测试服务不可用")
            return 1
        print("✅ API测试服务正常")
        
        # 2. 获取项目和环境
        print("📋 获取项目和环境信息...")
        projects = client.get_projects()
        environments = client.get_environments()
        
        # 选择项目
        target_project = None
        if project_name:
            target_project = next((p for p in projects if p["name"] == project_name), None)
            if not target_project:
                print(f"❌ 项目不存在: {project_name}")
                return 1
        else:
            target_project = projects[0] if projects else None
        
        if not target_project:
            print("❌ 没有可用的项目")
            return 1
        
        print(f"📁 使用项目: {target_project['name']}")
        
        # 选择环境
        target_environment = None
        if environment_name:
            target_environment = next((e for e in environments if e["name"] == environment_name), None)
            if not target_environment:
                print(f"❌ 环境不存在: {environment_name}")
                return 1
        else:
            # 使用默认环境
            target_environment = next((e for e in environments if e["is_default"]), None)
            if not target_environment and environments:
                target_environment = environments[0]
        
        if target_environment:
            print(f"🌐 使用环境: {target_environment['name']}")
        
        # 3. 获取测试用例
        print("📝 获取测试用例...")
        test_cases = client.get_test_cases(target_project["id"])
        
        if not test_cases:
            print("❌ 没有可用的测试用例")
            return 1
        
        # 根据标签过滤用例
        if tags:
            filtered_cases = []
            for case in test_cases:
                case_tags = case.get("tags", [])
                if any(tag in case_tags for tag in tags):
                    filtered_cases.append(case)
            test_cases = filtered_cases
        
        if not test_cases:
            print("❌ 没有符合条件的测试用例")
            return 1
        
        print(f"📊 找到 {len(test_cases)} 个测试用例")
        
        # 4. 执行测试用例
        print("🚀 开始执行测试用例...")
        case_ids = [case["id"] for case in test_cases]
        environment_id = target_environment["id"] if target_environment else None
        
        execution_id = client.execute_test_cases(
            case_ids=case_ids,
            environment_id=environment_id,
            parallel=parallel,
            max_workers=3,
            stop_on_failure=False
        )
        
        print(f"📋 执行ID: {execution_id}")
        
        # 5. 等待执行完成
        print("⏳ 等待执行完成...")
        final_status = client.wait_for_completion(execution_id, timeout)
        
        # 6. 获取执行报告
        print("📊 获取执行报告...")
        report = client.get_execution_report(execution_id)
        
        # 7. 输出结果
        print("\n" + "=" * 50)
        print("📊 执行结果汇总")
        print("=" * 50)
        print(f"总用例数: {report['total_cases']}")
        print(f"通过数量: {report['passed_cases']}")
        print(f"失败数量: {report['failed_cases']}")
        print(f"跳过数量: {report['skipped_cases']}")
        print(f"错误数量: {report['error_cases']}")
        print(f"通过率: {report['pass_rate']:.1f}%")
        print(f"执行时长: {report['total_duration']:.1f}秒")
        
        # 8. 输出失败用例详情
        failed_cases = [r for r in report['case_results'] if r['status'] == 'Failed']
        if failed_cases:
            print("\n❌ 失败用例详情:")
            for case in failed_cases:
                print(f"  - {case['case_name']}: {case['error_message']}")
        
        # 9. 判断是否成功
        if report['failed_cases'] == 0 and report['error_cases'] == 0:
            print("\n🎉 所有测试用例执行成功！")
            return 0
        else:
            print(f"\n❌ 有 {report['failed_cases'] + report['error_cases']} 个用例执行失败")
            return 1
    
    except Exception as e:
        print(f"❌ 执行过程中发生错误: {e}")
        return 1


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Jenkins API测试集成脚本')
    parser.add_argument('--api-url', default='http://localhost:8888', help='API测试服务地址')
    parser.add_argument('--project', help='项目名称')
    parser.add_argument('--environment', help='环境名称')
    parser.add_argument('--tags', nargs='+', help='标签列表')
    parser.add_argument('--parallel', action='store_true', help='并行执行')
    parser.add_argument('--timeout', type=int, default=300, help='超时时间（秒）')
    
    args = parser.parse_args()
    
    print("🚀 Jenkins API测试集成")
    print("=" * 50)
    
    exit_code = run_api_tests_for_jenkins(
        api_url=args.api_url,
        project_name=args.project,
        environment_name=args.environment,
        tags=args.tags,
        parallel=args.parallel,
        timeout=args.timeout
    )
    
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
