#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据库迁移脚本 - 修复VariableExtraction数据结构
"""

import sqlite3
import json
import os
from datetime import datetime

def migrate_variable_extractions():
    """迁移变量提取数据，添加缺失的description字段"""
    
    db_path = "data/api_test.db"
    
    if not os.path.exists(db_path):
        print("数据库文件不存在，无需迁移")
        return
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 获取所有测试用例
            cursor.execute('SELECT id, variable_extractions FROM test_cases WHERE variable_extractions IS NOT NULL AND variable_extractions != ""')
            rows = cursor.fetchall()
            
            updated_count = 0
            
            for row in rows:
                case_id, extractions_json = row
                
                try:
                    extractions_data = json.loads(extractions_json)
                    
                    # 检查是否需要更新
                    needs_update = False
                    
                    for extraction in extractions_data:
                        if 'description' not in extraction:
                            extraction['description'] = ""
                            needs_update = True
                        
                        # 确保其他必要字段存在
                        if 'id' not in extraction:
                            extraction['id'] = f"var_extract_{datetime.now().timestamp()}"
                            needs_update = True
                        
                        if 'default_value' not in extraction:
                            extraction['default_value'] = ""
                            needs_update = True
                    
                    # 如果需要更新，保存回数据库
                    if needs_update:
                        updated_extractions_json = json.dumps(extractions_data)
                        cursor.execute(
                            'UPDATE test_cases SET variable_extractions = ? WHERE id = ?',
                            (updated_extractions_json, case_id)
                        )
                        updated_count += 1
                        print(f"更新用例 {case_id} 的变量提取配置")
                
                except json.JSONDecodeError as e:
                    print(f"用例 {case_id} 的变量提取配置JSON格式错误: {e}")
                    continue
                except Exception as e:
                    print(f"处理用例 {case_id} 时出错: {e}")
                    continue
            
            conn.commit()
            print(f"迁移完成，共更新了 {updated_count} 个用例")
            
    except Exception as e:
        print(f"迁移失败: {e}")

def add_database_assertions_column():
    """添加数据库断言字段（如果不存在）"""
    
    db_path = "data/api_test.db"
    
    if not os.path.exists(db_path):
        print("数据库文件不存在，无需添加字段")
        return
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查字段是否已存在
            cursor.execute("PRAGMA table_info(test_cases)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'database_assertions' not in columns:
                cursor.execute('ALTER TABLE test_cases ADD COLUMN database_assertions TEXT')
                print("成功添加 database_assertions 字段")
            else:
                print("database_assertions 字段已存在")
            
            conn.commit()
            
    except Exception as e:
        print(f"添加字段失败: {e}")

def main():
    """主函数"""
    print("开始数据库迁移...")
    print("=" * 50)
    
    # 1. 添加数据库断言字段
    print("1. 添加数据库断言字段...")
    add_database_assertions_column()
    
    # 2. 迁移变量提取数据
    print("\n2. 迁移变量提取数据...")
    migrate_variable_extractions()
    
    print("\n" + "=" * 50)
    print("数据库迁移完成！")

if __name__ == "__main__":
    main()
